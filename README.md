# 百家乐游戏

一个基于Web的百家乐游戏，使用HTML、CSS和JavaScript开发。

## 功能特点

- 🎴 使用真实的扑克牌图片（从网上下载的公共域图片）
- 🎯 完整的百家乐游戏规则实现
- 🃏 **5副牌场次系统**：使用5副牌（260张）作为一个场次，牌发完或剩余不足时自动开启新场次
- 📈 **完整路表功能**：包含大路、大眼仔、小路、曱甴路等赌场标准路表
- 💰 虚拟货币系统
- 📊 详细的游戏统计记录（场次、局数、剩余牌数）
- 🎨 精美的用户界面和动画效果
- 📱 响应式设计，支持移动设备

## 游戏规则

### 基本规则
- 目标：预测庄家或闲家哪一方的牌点数更接近9点
- 牌值：A=1，2-9按面值，10/J/Q/K=0
- 计分：两张牌点数相加，只取个位数

### 补牌规则
- 闲家点数0-5：必须补牌
- 闲家点数6-7：不补牌
- 庄家补牌规则根据自身点数和闲家第三张牌决定

### 赔率
- 庄家胜：1:0.95
- 闲家胜：1:1
- 和局：1:8

## 如何运行

1. 确保你有Python 3环境
2. 在项目目录下运行：
   ```bash
   python3 -m http.server 8000
   ```
3. 打开浏览器访问：http://localhost:8000

## 如何游戏

1. 选择下注类型（庄家、闲家或和局）
2. 设置下注金额
3. 点击"发牌"开始游戏
4. 系统自动处理发牌和补牌
5. 查看结果并获得奖金
6. 观察路表变化，分析游戏趋势
7. 当5副牌发完时，系统自动开启新场次

## 路表说明

- **大路**：记录每局的胜负结果，庄家胜显示红色"庄"，闲家胜显示蓝色"闲"
- **大眼仔**：根据大路的规律性进行预测分析
- **小路**：进一步的趋势分析工具
- **曱甴路**：最精细的趋势分析路表

路表是百家乐的重要分析工具，帮助玩家识别游戏趋势和模式。

## 项目结构

```
baccarat/
├── index.html          # 主页面
├── css/
│   └── style.css       # 样式文件
├── js/
│   └── game.js         # 游戏逻辑
├── assets/
│   └── images/
│       └── cards/      # 扑克牌图片
└── README.md           # 说明文件
```

## 扑克牌图片来源

扑克牌图片来自 [Tek Eye](https://tekeye.uk/playing_cards/svg-playing-cards) 的公共域SVG扑克牌集合。这些图片是免费使用的，无需版权许可。

## 技术栈

- HTML5
- CSS3 (包含动画效果)
- 原生JavaScript (ES6+)
- 响应式设计

## 浏览器兼容性

支持所有现代浏览器：
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 许可证

本项目使用MIT许可证。扑克牌图片为公共域资源。

---

享受游戏吧！🎲
