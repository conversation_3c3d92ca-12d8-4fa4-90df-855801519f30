* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #0f4c3a, #1a5f4a);
    color: white;
    min-height: 100vh;
}

.game-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
}

header h1 {
    font-size: 2.5em;
    margin-bottom: 20px;
    color: #ffd700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.game-info {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.session-info {
    display: flex;
    justify-content: center;
    gap: 30px;
    background: rgba(0,0,0,0.4);
    padding: 10px;
    border-radius: 8px;
}

.info-item {
    font-size: 1em;
    font-weight: bold;
    color: #ffd700;
}

.score-board {
    display: flex;
    justify-content: center;
    gap: 30px;
    background: rgba(0,0,0,0.3);
    padding: 15px;
    border-radius: 10px;
}

.score-item {
    font-size: 1.1em;
    font-weight: bold;
}

.game-area {
    display: grid;
    grid-template-columns: 1fr 300px 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.banker-area, .player-area {
    background: rgba(0,0,0,0.4);
    padding: 20px;
    border-radius: 15px;
    text-align: center;
}

.banker-area h2, .player-area h2 {
    margin-bottom: 20px;
    color: #ffd700;
    font-size: 1.5em;
}

.cards-container {
    min-height: 120px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
    padding: 10px;
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    border: 2px dashed rgba(255,255,255,0.3);
}

.card {
    width: 70px;
    height: 100px;
    background: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8em;
    color: black;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
    animation: cardDeal 0.5s ease-out;
}

.card:hover {
    transform: translateY(-5px) scale(1.05);
}

@keyframes cardDeal {
    from {
        transform: translateY(-50px) rotateY(180deg);
        opacity: 0;
    }
    to {
        transform: translateY(0) rotateY(0deg);
        opacity: 1;
    }
}

.card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
}

.points {
    font-size: 1.3em;
    font-weight: bold;
    color: #ffd700;
}

.game-controls {
    background: rgba(0,0,0,0.5);
    padding: 25px;
    border-radius: 15px;
    text-align: center;
}

.betting-area {
    margin-bottom: 25px;
}

.betting-area h3 {
    margin-bottom: 15px;
    color: #ffd700;
}

.bet-buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    justify-content: center;
}

.bet-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    background: #2c5f4f;
    color: white;
    font-size: 1em;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.bet-btn:hover {
    background: #3a7a65;
    transform: translateY(-2px);
}

.bet-btn.selected {
    background: #ffd700;
    color: #0f4c3a;
    border-color: #ffed4e;
    font-weight: bold;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.bet-amount {
    margin-bottom: 15px;
}

.bet-amount label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.bet-amount input {
    padding: 8px 12px;
    border: none;
    border-radius: 5px;
    font-size: 1em;
    text-align: center;
    width: 120px;
}

.current-bet {
    font-size: 1.1em;
    margin-bottom: 15px;
    color: #ffd700;
}

.game-buttons {
    margin-bottom: 20px;
}

.action-btn {
    padding: 15px 30px;
    border: none;
    border-radius: 8px;
    background: #ff6b35;
    color: white;
    font-size: 1.1em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 0 10px;
}

.action-btn:hover:not(:disabled) {
    background: #ff8c5a;
    transform: translateY(-2px);
}

.action-btn:disabled {
    background: #666;
    cursor: not-allowed;
    opacity: 0.6;
}

.balance {
    font-size: 1.2em;
    font-weight: bold;
    color: #ffd700;
}

.game-result {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.game-result.hidden {
    display: none;
}

.result-content {
    background: #1a5f4a;
    padding: 40px;
    border-radius: 15px;
    text-align: center;
    border: 3px solid #ffd700;
}

.result-content h3 {
    font-size: 2em;
    margin-bottom: 15px;
    color: #ffd700;
}

.result-content p {
    font-size: 1.2em;
    margin-bottom: 25px;
}

.rules-section {
    background: rgba(0,0,0,0.3);
    border-radius: 10px;
    padding: 20px;
}

.rules-section summary {
    font-size: 1.2em;
    font-weight: bold;
    cursor: pointer;
    color: #ffd700;
    margin-bottom: 15px;
}

.rules-content {
    margin-top: 15px;
}

.rules-content h4 {
    margin-bottom: 10px;
    color: #ffd700;
}

.rules-content ul {
    list-style-position: inside;
    line-height: 1.6;
}

.rules-content li {
    margin-bottom: 5px;
}

/* 路表样式 */
.roadmap-section {
    background: rgba(0,0,0,0.4);
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 30px;
}

.roadmap-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
}

.roadmap-item {
    text-align: center;
}

.roadmap-item h4 {
    color: #ffd700;
    margin-bottom: 10px;
    font-size: 1em;
}

.roadmap {
    background: white;
    border-radius: 8px;
    min-height: 120px;
    padding: 5px;
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 1px;
    overflow: hidden;
}

.road-cell {
    width: 100%;
    height: 20px;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
}

.road-cell.banker {
    background: #ff4444;
    color: white;
}

.road-cell.player {
    background: #4444ff;
    color: white;
}

.road-cell.tie {
    background: #44ff44;
    color: black;
}

.road-cell.banker-pair {
    border: 2px solid #ff8888;
}

.road-cell.player-pair {
    border: 2px solid #8888ff;
}

.big-road .road-cell {
    height: 25px;
    font-size: 12px;
}

.big-eye .road-cell,
.small-road .road-cell,
.cockroach .road-cell {
    height: 15px;
    font-size: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .game-area {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .game-info {
        gap: 10px;
    }

    .session-info {
        flex-direction: column;
        gap: 10px;
    }

    .score-board {
        flex-direction: column;
        gap: 10px;
    }

    .bet-buttons {
        flex-direction: column;
        align-items: center;
    }

    .bet-btn {
        width: 150px;
    }

    .roadmap-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .roadmap {
        grid-template-columns: repeat(8, 1fr);
        min-height: 80px;
    }

    .road-cell {
        height: 15px;
        font-size: 8px;
    }

    .big-road .road-cell {
        height: 18px;
        font-size: 10px;
    }
}
