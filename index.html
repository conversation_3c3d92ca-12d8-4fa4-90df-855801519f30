<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>百家乐游戏</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="game-container">
        <header>
            <h1>百家乐游戏</h1>
            <div class="game-info">
                <div class="session-info">
                    <div class="info-item">
                        <span>场次:</span>
                        <span id="session-number">1</span>
                    </div>
                    <div class="info-item">
                        <span>剩余牌数:</span>
                        <span id="cards-remaining">260</span>
                    </div>
                    <div class="info-item">
                        <span>局数:</span>
                        <span id="round-number">0</span>
                    </div>
                </div>
                <div class="score-board">
                    <div class="score-item">
                        <span>庄家胜:</span>
                        <span id="banker-wins">0</span>
                    </div>
                    <div class="score-item">
                        <span>闲家胜:</span>
                        <span id="player-wins">0</span>
                    </div>
                    <div class="score-item">
                        <span>和局:</span>
                        <span id="ties">0</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- 路表区域 -->
        <div class="roadmap-section">
            <div class="roadmap-container">
                <div class="roadmap-item">
                    <h4>大路</h4>
                    <div class="roadmap big-road" id="big-road"></div>
                </div>
                <div class="roadmap-item">
                    <h4>大眼仔</h4>
                    <div class="roadmap big-eye" id="big-eye"></div>
                </div>
                <div class="roadmap-item">
                    <h4>小路</h4>
                    <div class="roadmap small-road" id="small-road"></div>
                </div>
                <div class="roadmap-item">
                    <h4>曱甴路</h4>
                    <div class="roadmap cockroach" id="cockroach-road"></div>
                </div>
            </div>
        </div>

        <main class="game-area">
            <!-- 庄家区域 -->
            <div class="banker-area">
                <h2>庄家 (Banker)</h2>
                <div class="cards-container" id="banker-cards">
                    <!-- 庄家的牌将在这里显示 -->
                </div>
                <div class="points">
                    <span>点数: </span>
                    <span id="banker-points">0</span>
                </div>
            </div>

            <!-- 游戏控制区域 -->
            <div class="game-controls">
                <div class="betting-area">
                    <h3>下注区域</h3>
                    <div class="bet-buttons">
                        <button id="bet-banker" class="bet-btn">庄家</button>
                        <button id="bet-player" class="bet-btn">闲家</button>
                        <button id="bet-tie" class="bet-btn">和局</button>
                    </div>
                    <div class="bet-amount">
                        <label for="bet-input">下注金额:</label>
                        <input type="number" id="bet-input" min="10" max="1000" value="100">
                    </div>
                    <div class="current-bet">
                        <span>当前下注: </span>
                        <span id="current-bet-display">无</span>
                    </div>
                </div>
                
                <div class="game-buttons">
                    <button id="deal-btn" class="action-btn" disabled>发牌</button>
                    <button id="new-game-btn" class="action-btn">新游戏</button>
                </div>

                <div class="balance">
                    <span>余额: ¥</span>
                    <span id="balance">1000</span>
                </div>
            </div>

            <!-- 闲家区域 -->
            <div class="player-area">
                <h2>闲家 (Player)</h2>
                <div class="cards-container" id="player-cards">
                    <!-- 闲家的牌将在这里显示 -->
                </div>
                <div class="points">
                    <span>点数: </span>
                    <span id="player-points">0</span>
                </div>
            </div>
        </main>

        <!-- 游戏结果显示 -->
        <div id="game-result" class="game-result hidden">
            <div class="result-content">
                <h3 id="result-text"></h3>
                <p id="result-details"></p>
                <button id="continue-btn" class="action-btn">继续游戏</button>
            </div>
        </div>

        <!-- 游戏规则说明 -->
        <div class="rules-section">
            <details>
                <summary>游戏规则</summary>
                <div class="rules-content">
                    <h4>百家乐基本规则:</h4>
                    <ul>
                        <li>目标：预测庄家或闲家哪一方的牌点数更接近9点</li>
                        <li>牌值：A=1，2-9按面值，10/J/Q/K=0</li>
                        <li>计分：两张牌点数相加，只取个位数</li>
                        <li>补牌规则：根据特定规则决定是否补第三张牌</li>
                        <li>赔率：庄家1:0.95，闲家1:1，和局1:8</li>
                    </ul>
                </div>
            </details>
        </div>
    </div>

    <script src="js/game.js"></script>
</body>
</html>
