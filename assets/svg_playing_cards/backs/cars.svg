<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   inkscape:export-ydpi="96"
   inkscape:export-xdpi="96"
   inkscape:export-filename="E:\DAN\OneDrive\projects\Playing_Cards\backs\png-096-dpi\cars.png"
   height="333"
   width="234"
   inkscape:version="1.0 (4035a4fb49, 2020-05-01)"
   version="1.1"
   viewBox="0 0 234 333.00001"
   sodipodi:docname="cars.svg"
   id="svg2">
  <title
     id="title5632">Vintage red cars</title>
  <defs
     id="defs4">
    <linearGradient
       inkscape:collect="always"
       id="linearGradient4628">
      <stop
         offset="0"
         style="stop-color:#dc0000"
         id="stop4630" />
      <stop
         offset="1"
         style="stop-color:#0e232e"
         id="stop4632" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       id="linearGradient4409">
      <stop
         offset="0"
         style="stop-color:#2d2d2d"
         id="stop4411" />
      <stop
         offset="1"
         style="stop-color:#666666"
         id="stop4413" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       id="linearGradient4403">
      <stop
         offset="0"
         style="stop-color:#666666"
         id="stop4405" />
      <stop
         offset="1"
         style="stop-color:#bdcdd4"
         id="stop4407" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       id="linearGradient4281">
      <stop
         offset="0"
         style="stop-color:#2d2d2d"
         id="stop4283" />
      <stop
         offset="1"
         style="stop-color:#2d2d2d"
         id="stop4285" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       id="linearGradient4231">
      <stop
         offset="0"
         style="stop-color:#2d2d2d"
         id="stop4233" />
      <stop
         offset="1"
         style="stop-color:#ffffff"
         id="stop4235" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       id="linearGradient4063">
      <stop
         offset="0"
         style="stop-color:#666666"
         id="stop4065" />
      <stop
         offset="1"
         style="stop-color:#2d2d2d"
         id="stop4067" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       x1="6.0609002"
       y1="853.35999"
       x2="8.0811996"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4231"
       y2="829.12"
       id="linearGradient4239" />
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:collect="always"
       x="-0.18461999"
       y="-0.19459"
       width="1.3692"
       height="1.3892"
       id="filter4259">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="3.0304577"
         id="feGaussianBlur4261" />
    </filter>
    <radialGradient
       inkscape:collect="always"
       r="106.39"
       gradientTransform="matrix(-0.93117,0.027936,-0.025856,-0.93225,-288.39,1761.3)"
       cx="-61.618999"
       cy="891.90002"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4063"
       id="radialGradient4293" />
    <radialGradient
       inkscape:collect="always"
       r="96.925003"
       gradientTransform="matrix(1.0511,0.18411,-0.24505,1.399,221.75,-313.78)"
       cx="-235.02"
       cy="914.98999"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4281"
       id="radialGradient4295" />
    <linearGradient
       inkscape:collect="always"
       x1="87.455002"
       y1="946.09003"
       x2="82.877998"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4403"
       y2="905.08002"
       id="linearGradient4401" />
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:collect="always"
       x="-0.011626"
       y="-0.93803"
       width="1.0233001"
       height="2.8761001"
       id="filter4892">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="4.0303255"
         id="feGaussianBlur4894" />
    </filter>
    <linearGradient
       inkscape:collect="always"
       x1="-831.42999"
       y1="852.35999"
       gradientTransform="matrix(0.042048,0,0,0.093523,376.53,862.48)"
       x2="-780"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4628"
       y2="849.51001"
       id="linearGradient4897" />
    <linearGradient
       inkscape:collect="always"
       x1="-831.42999"
       y1="852.35999"
       gradientTransform="matrix(0.042048,0,0,0.093523,373.42,862.48)"
       x2="-780"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4628"
       y2="849.51001"
       id="linearGradient4900" />
    <linearGradient
       inkscape:collect="always"
       x1="-831.42999"
       y1="852.35999"
       gradientTransform="matrix(0.042048,0,0,0.093523,371.08,862.48)"
       x2="-780"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4628"
       y2="849.51001"
       id="linearGradient4903" />
    <linearGradient
       inkscape:collect="always"
       x1="-831.42999"
       y1="852.35999"
       gradientTransform="matrix(0.042048,0,0,0.093523,367.96,862.48)"
       x2="-780"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4628"
       y2="849.51001"
       id="linearGradient4906" />
    <linearGradient
       inkscape:collect="always"
       x1="-831.42999"
       y1="852.35999"
       gradientTransform="matrix(0.042048,0,0,0.093523,364.07,862.48)"
       x2="-780"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4628"
       y2="849.51001"
       id="linearGradient4909" />
    <linearGradient
       inkscape:collect="always"
       x1="-831.42999"
       y1="852.35999"
       gradientTransform="matrix(0.042048,0,0,0.093523,360.17,862.48)"
       x2="-780"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4628"
       y2="849.51001"
       id="linearGradient4912" />
    <linearGradient
       inkscape:collect="always"
       x1="-831.42999"
       y1="852.35999"
       gradientTransform="matrix(0.042048,0,0,0.093523,356.21,862.48)"
       x2="-780"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4628"
       y2="849.51001"
       id="linearGradient4915" />
    <linearGradient
       inkscape:collect="always"
       x1="-831.42999"
       y1="852.35999"
       gradientTransform="matrix(0.042048,0,0,0.093523,353.09,862.48)"
       x2="-780"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4628"
       y2="849.51001"
       id="linearGradient4918" />
    <linearGradient
       inkscape:collect="always"
       x1="-831.42999"
       y1="852.35999"
       gradientTransform="matrix(0.042048,0,0,0.093523,350.76,862.48)"
       x2="-780"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4628"
       y2="849.51001"
       id="linearGradient4921" />
    <linearGradient
       inkscape:collect="always"
       x1="-831.42999"
       y1="852.35999"
       gradientTransform="matrix(0.042048,0,0,0.093523,347.64,862.48)"
       x2="-780"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4628"
       y2="849.51001"
       id="linearGradient4924" />
    <linearGradient
       inkscape:collect="always"
       x1="-831.42999"
       y1="852.35999"
       gradientTransform="matrix(0.042048,0,0,0.093523,343.75,862.48)"
       x2="-780"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4628"
       y2="849.51001"
       id="linearGradient4927" />
    <linearGradient
       inkscape:collect="always"
       x1="-831.42999"
       y1="852.35999"
       gradientTransform="matrix(0.042048,0,0,0.093523,339.85,862.48)"
       x2="-780"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4628"
       y2="849.51001"
       id="linearGradient4930" />
    <linearGradient
       inkscape:collect="always"
       x1="-831.42999"
       y1="852.35999"
       gradientTransform="matrix(0.042048,0,0,0.093523,344.92,862.48)"
       x2="-780"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4628"
       y2="849.51001"
       id="linearGradient4933" />
    <linearGradient
       inkscape:collect="always"
       x1="-831.42999"
       y1="852.35999"
       gradientTransform="matrix(0.042048,0,0,0.093523,341.81,862.48)"
       x2="-780"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4628"
       y2="849.51001"
       id="linearGradient4936" />
    <linearGradient
       inkscape:collect="always"
       x1="-831.42999"
       y1="852.35999"
       gradientTransform="matrix(0.042048,0,0,0.093523,339.47,862.48)"
       x2="-780"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4628"
       y2="849.51001"
       id="linearGradient4939" />
    <linearGradient
       inkscape:collect="always"
       x1="-831.42999"
       y1="852.35999"
       gradientTransform="matrix(0.042048,0,0,0.093523,336.35,862.48)"
       x2="-780"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4628"
       y2="849.51001"
       id="linearGradient4942" />
    <linearGradient
       inkscape:collect="always"
       x1="-831.42999"
       y1="852.35999"
       gradientTransform="matrix(0.042048,0,0,0.093523,332.46,862.48)"
       x2="-780"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4628"
       y2="849.51001"
       id="linearGradient4945" />
    <linearGradient
       inkscape:collect="always"
       x1="-831.42999"
       y1="852.35999"
       gradientTransform="matrix(0.042048,0,0,0.093523,328.56,862.48)"
       x2="-780"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4628"
       y2="849.51001"
       id="linearGradient4948" />
    <linearGradient
       inkscape:collect="always"
       x1="-831.42999"
       y1="852.35999"
       gradientTransform="matrix(0.041679,0,0,0.098342,314.45,858)"
       x2="-780"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4628"
       y2="849.51001"
       id="linearGradient4951" />
    <linearGradient
       inkscape:collect="always"
       x1="-831.42999"
       y1="852.35999"
       gradientTransform="matrix(0.041679,0,0,0.098342,311.36,858)"
       x2="-780"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4628"
       y2="849.51001"
       id="linearGradient4954" />
    <linearGradient
       inkscape:collect="always"
       x1="-831.42999"
       y1="852.35999"
       gradientTransform="matrix(0.041679,0,0,0.098342,309.04,858)"
       x2="-780"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4628"
       y2="849.51001"
       id="linearGradient4957" />
    <linearGradient
       inkscape:collect="always"
       x1="-831.42999"
       y1="852.35999"
       gradientTransform="matrix(0.041679,0,0,0.098342,305.95,858)"
       x2="-780"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4628"
       y2="849.51001"
       id="linearGradient4960" />
    <linearGradient
       inkscape:collect="always"
       x1="-831.42999"
       y1="852.35999"
       gradientTransform="matrix(0.041679,0,0,0.098342,302.09,858)"
       x2="-780"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4628"
       y2="849.51001"
       id="linearGradient4963" />
    <linearGradient
       inkscape:collect="always"
       x1="-831.42999"
       y1="852.35999"
       gradientTransform="matrix(0.041679,0,0,0.098342,298.23,858)"
       x2="-780"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4628"
       y2="849.51001"
       id="linearGradient4966" />
    <linearGradient
       inkscape:collect="always"
       x1="-183.85001"
       y1="737.19"
       gradientTransform="matrix(0.5,0,0,0.5,207.33,521.29)"
       x2="-5.0507998"
       gradientUnits="userSpaceOnUse"
       y2="838.21002"
       id="linearGradient4970">
      <stop
         offset="0"
         style="stop-color:#b50000"
         id="stop4582" />
      <stop
         offset="1"
         style="stop-color:#b50000"
         id="stop4584" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       x1="-322.85999"
       y1="749.51001"
       gradientTransform="matrix(0.5,0,0,0.5,207.33,521.29)"
       x2="-188.57001"
       gradientUnits="userSpaceOnUse"
       y2="743.78998"
       id="linearGradient4973">
      <stop
         offset="0"
         style="stop-color:#b50000"
         id="stop4608" />
      <stop
         offset="1"
         style="stop-color:#dc0000"
         id="stop4610" />
    </linearGradient>
    <radialGradient
       inkscape:collect="always"
       r="26.997999"
       gradientTransform="matrix(0.11638,0.71316,-0.60712,0.099079,654.59,757)"
       cx="61.941002"
       cy="691.26001"
       gradientUnits="userSpaceOnUse"
       id="radialGradient4976">
      <stop
         offset="0"
         style="stop-color:#bdcdd4"
         id="stop5482" />
      <stop
         offset="1"
         style="stop-color:#ffffff"
         id="stop5484" />
    </radialGradient>
    <linearGradient
       inkscape:collect="always"
       x1="-218.19"
       y1="1000.8"
       gradientTransform="matrix(0.47641,0,0,0.47641,548,545.67)"
       x2="-173.75"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4409"
       y2="914.97998"
       id="linearGradient4980" />
    <radialGradient
       inkscape:collect="always"
       r="67.369003"
       gradientTransform="matrix(-0.55861,0.094412,-0.086733,-0.51317,372.51,1483.8)"
       cx="-246.48"
       cy="924.06"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4063"
       id="radialGradient5034" />
    <radialGradient
       inkscape:collect="always"
       r="67.369003"
       gradientTransform="matrix(-0.5273,0.08912,-0.081872,-0.48441,375.77,1455.9)"
       cx="-246.48"
       cy="924.06"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4063"
       id="radialGradient5037" />
    <linearGradient
       inkscape:collect="always"
       x1="62.856998"
       y1="863.78998"
       gradientTransform="matrix(0.5,0,0,0.5,207.33,521.29)"
       x2="60"
       gradientUnits="userSpaceOnUse"
       y2="800.92999"
       id="linearGradient5043">
      <stop
         offset="0"
         style="stop-color:#b50000"
         id="stop4620" />
      <stop
         offset="1"
         style="stop-color:#dc0000"
         id="stop4622" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       x1="-218.19"
       y1="1000.8"
       gradientTransform="matrix(0.47641,0,0,0.47641,201.52,543.15)"
       x2="-173.75"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4409"
       y2="914.97998"
       id="linearGradient5046" />
    <radialGradient
       inkscape:collect="always"
       r="67.369003"
       gradientTransform="matrix(-0.55861,0.094412,-0.086733,-0.51317,26.028,1481.3)"
       cx="-246.48"
       cy="924.06"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4063"
       id="radialGradient5100" />
    <radialGradient
       inkscape:collect="always"
       r="67.369003"
       gradientTransform="matrix(-0.5273,0.08912,-0.081872,-0.48441,29.283,1453.4)"
       cx="-246.48"
       cy="924.06"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4063"
       id="radialGradient5103" />
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5350">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5352" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5354">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5356" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5358">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5360" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5362">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5364" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5366">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5368" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5370">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5372" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5374">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5376" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5418">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5420" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5422">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5424" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5426">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5428" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5430">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5432" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5434">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5436" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5438">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5440" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5442">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5444" />
    </filter>
    <linearGradient
       inkscape:collect="always"
       x1="-831.42999"
       y1="852.35999"
       gradientTransform="matrix(0.042048,0,0,0.093523,379.36,862.48)"
       x2="-780"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4628"
       y2="849.51001"
       id="linearGradient5468" />
    <linearGradient
       inkscape:collect="always"
       x1="-831.42999"
       y1="852.35999"
       gradientTransform="matrix(0.042048,0,0,0.093523,383.26,862.48)"
       x2="-780"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4628"
       y2="849.51001"
       id="linearGradient5470" />
    <linearGradient
       inkscape:collect="always"
       x1="-831.42999"
       y1="852.35999"
       gradientTransform="matrix(0.042048,0,0,0.093523,387.15,862.48)"
       x2="-780"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4628"
       y2="849.51001"
       id="linearGradient5472" />
    <linearGradient
       inkscape:collect="always"
       x1="-831.42999"
       y1="852.35999"
       gradientTransform="matrix(0.042048,0,0,0.093523,390.27,862.48)"
       x2="-780"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4628"
       y2="849.51001"
       id="linearGradient5474" />
    <linearGradient
       inkscape:collect="always"
       x1="-831.42999"
       y1="852.35999"
       gradientTransform="matrix(0.042048,0,0,0.093523,392.61,862.48)"
       x2="-780"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4628"
       y2="849.51001"
       id="linearGradient5476" />
    <linearGradient
       inkscape:collect="always"
       x1="-831.42999"
       y1="852.35999"
       gradientTransform="matrix(0.042048,0,0,0.093523,395.72,862.48)"
       x2="-780"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4628"
       y2="849.51001"
       id="linearGradient5478" />
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:collect="always"
       x="-0.027811"
       y="-0.24101999"
       width="1.0556"
       height="1.482"
       id="filter5499">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="1.8076857"
         id="feGaussianBlur5501" />
    </filter>
    <linearGradient
       inkscape:collect="always"
       x1="-376"
       y1="880.35999"
       gradientTransform="matrix(0.5,0,0,0.5,207.33,521.29)"
       x2="-256"
       gradientUnits="userSpaceOnUse"
       y2="802.35999"
       id="linearGradient5519">
      <stop
         offset="0"
         style="stop-color:#b50000"
         id="stop5515" />
      <stop
         offset="1"
         style="stop-color:#ff4141"
         id="stop5517" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       x1="-372"
       y1="912.35999"
       x2="-278"
       gradientUnits="userSpaceOnUse"
       y2="798.35999"
       id="linearGradient5531">
      <stop
         offset="0"
         style="stop-color:#dc0000"
         id="stop5527" />
      <stop
         offset="1"
         style="stop-color:#2d2d2d"
         id="stop5529" />
    </linearGradient>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:collect="always"
       id="filter5537">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="4.7824692"
         id="feGaussianBlur5539" />
    </filter>
    <linearGradient
       inkscape:collect="always"
       x1="20"
       y1="928.35999"
       gradientTransform="matrix(0.5,0,0,0.5,200.33,519.29)"
       x2="18"
       gradientUnits="userSpaceOnUse"
       y2="880.35999"
       id="linearGradient5558">
      <stop
         offset="0"
         style="stop-color:#0e232e"
         id="stop5554" />
      <stop
         offset="1"
         style="stop-color:#0e232e;stop-opacity:0"
         id="stop5556" />
    </linearGradient>
    <filter
       style="color-interpolation-filters:sRGB"
       inkscape:collect="always"
       x="-0.027948"
       y="-0.12884"
       width="1.0559"
       height="1.2577"
       id="filter5562">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="1.9330982"
         id="feGaussianBlur5564" />
    </filter>
    <linearGradient
       inkscape:collect="always"
       x1="462"
       y1="796.35999"
       x2="372"
       gradientUnits="userSpaceOnUse"
       y2="826.35999"
       id="linearGradient5576">
      <stop
         offset="0"
         style="stop-color:#ffffff"
         id="stop5572" />
      <stop
         offset="1"
         style="stop-color:#b50000"
         id="stop5574" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       x1="-176.11"
       y1="763.75"
       x2="-194.14"
       gradientUnits="userSpaceOnUse"
       y2="798.97998"
       id="linearGradient5602">
      <stop
         offset="0"
         style="stop-color:#ffffff"
         id="stop5598" />
      <stop
         offset="1"
         style="stop-color:#ffffff;stop-opacity:0"
         id="stop5600" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       x1="34"
       y1="672.35999"
       gradientTransform="matrix(0.5,0,0,0.5,207.33,521.29)"
       x2="102"
       gradientUnits="userSpaceOnUse"
       y2="738.35999"
       id="linearGradient5604">
      <stop
         offset="0"
         style="stop-color:#0e232e"
         id="stop5608" />
      <stop
         offset="1"
         style="stop-color:#b50000"
         id="stop5610" />
    </linearGradient>
    <linearGradient
       y2="905.08002"
       x2="82.877998"
       y1="946.09003"
       x1="87.455002"
       gradientUnits="userSpaceOnUse"
       id="linearGradient2263"
       xlink:href="#linearGradient4403"
       inkscape:collect="always" />
    <linearGradient
       y2="829.12"
       x2="8.0811996"
       y1="853.35999"
       x1="6.0609002"
       gradientUnits="userSpaceOnUse"
       id="linearGradient2265"
       xlink:href="#linearGradient4231"
       inkscape:collect="always" />
    <radialGradient
       r="106.39"
       cy="891.90002"
       cx="-61.618999"
       gradientTransform="matrix(-0.93117,0.027936,-0.025856,-0.93225,-288.39,1761.3)"
       gradientUnits="userSpaceOnUse"
       id="radialGradient2267"
       xlink:href="#linearGradient4063"
       inkscape:collect="always" />
    <radialGradient
       r="96.925003"
       cy="914.98999"
       cx="-235.02"
       gradientTransform="matrix(1.0511,0.18411,-0.24505,1.399,221.75,-313.78)"
       gradientUnits="userSpaceOnUse"
       id="radialGradient2269"
       xlink:href="#linearGradient4281"
       inkscape:collect="always" />
    <linearGradient
       y2="905.08002"
       x2="82.877998"
       y1="946.09003"
       x1="87.455002"
       gradientUnits="userSpaceOnUse"
       id="linearGradient2263-2"
       xlink:href="#linearGradient4403"
       inkscape:collect="always" />
    <filter
       inkscape:collect="always"
       x="-0.18461999"
       y="-0.19459"
       width="1.3692"
       height="1.3892"
       id="filter4259-4"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="3.0304577"
         id="feGaussianBlur4261-5" />
    </filter>
    <radialGradient
       inkscape:collect="always"
       r="67.369003"
       gradientTransform="matrix(-0.5273,0.08912,-0.081872,-0.48441,29.283,1453.4)"
       cx="-246.48"
       cy="924.06"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4063"
       id="radialGradient5103-3" />
    <radialGradient
       inkscape:collect="always"
       r="67.369003"
       gradientTransform="matrix(-0.55861,0.094412,-0.086733,-0.51317,26.028,1481.3)"
       cx="-246.48"
       cy="924.06"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4063"
       id="radialGradient5100-1" />
    <linearGradient
       y2="829.12"
       x2="8.0811996"
       y1="853.35999"
       x1="6.0609002"
       gradientUnits="userSpaceOnUse"
       id="linearGradient2265-0"
       xlink:href="#linearGradient4231"
       inkscape:collect="always" />
    <radialGradient
       r="106.39"
       cy="891.90002"
       cx="-61.618999"
       gradientTransform="matrix(-0.93117,0.027936,-0.025856,-0.93225,-288.39,1761.3)"
       gradientUnits="userSpaceOnUse"
       id="radialGradient2267-1"
       xlink:href="#linearGradient4063"
       inkscape:collect="always" />
    <radialGradient
       r="96.925003"
       cy="914.98999"
       cx="-235.02"
       gradientTransform="matrix(1.0511,0.18411,-0.24505,1.399,221.75,-313.78)"
       gradientUnits="userSpaceOnUse"
       id="radialGradient2269-7"
       xlink:href="#linearGradient4281"
       inkscape:collect="always" />
    <radialGradient
       inkscape:collect="always"
       r="67.369003"
       gradientTransform="matrix(-0.5273,0.08912,-0.081872,-0.48441,375.77,1455.9)"
       cx="-246.48"
       cy="924.06"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4063"
       id="radialGradient5037-8" />
    <radialGradient
       inkscape:collect="always"
       r="67.369003"
       gradientTransform="matrix(-0.55861,0.094412,-0.086733,-0.51317,372.51,1483.8)"
       cx="-246.48"
       cy="924.06"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4063"
       id="radialGradient5034-1" />
    <radialGradient
       inkscape:collect="always"
       r="106.39"
       gradientTransform="matrix(-0.93117,0.027936,-0.025856,-0.93225,-288.39,1761.3)"
       cx="-61.618999"
       cy="891.90002"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4063"
       id="radialGradient4293-6" />
    <radialGradient
       inkscape:collect="always"
       r="96.925003"
       gradientTransform="matrix(1.0511,0.18411,-0.24505,1.399,221.75,-313.78)"
       cx="-235.02"
       cy="914.98999"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4281"
       id="radialGradient4295-7" />
    <radialGradient
       inkscape:collect="always"
       r="26.997999"
       gradientTransform="matrix(0.11638,0.71316,-0.60712,0.099079,654.59,757)"
       cx="61.941002"
       cy="691.26001"
       gradientUnits="userSpaceOnUse"
       id="radialGradient4976-5">
      <stop
         offset="0"
         style="stop-color:#bdcdd4"
         id="stop5482-5" />
      <stop
         offset="1"
         style="stop-color:#ffffff"
         id="stop5484-4" />
    </radialGradient>
    <filter
       inkscape:collect="always"
       x="-0.011626"
       y="-0.93803"
       width="1.0233001"
       height="2.8761001"
       id="filter4892-3"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="4.0303255"
         id="feGaussianBlur4894-9" />
    </filter>
    <filter
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5350-2"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5352-4" />
    </filter>
    <filter
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5354-7"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5356-1" />
    </filter>
    <filter
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5358-4"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5360-5" />
    </filter>
    <filter
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5362-7"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5364-9" />
    </filter>
    <filter
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5366-7"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5368-0" />
    </filter>
    <filter
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5370-1"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5372-9" />
    </filter>
    <filter
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5374-5"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5376-0" />
    </filter>
    <filter
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5418-0"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5420-8" />
    </filter>
    <filter
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5422-7"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5424-4" />
    </filter>
    <filter
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5426-4"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5428-1" />
    </filter>
    <filter
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5430-9"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5432-6" />
    </filter>
    <filter
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5434-8"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5436-0" />
    </filter>
    <filter
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5438-5"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5440-2" />
    </filter>
    <filter
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5442-7"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5444-5" />
    </filter>
    <filter
       inkscape:collect="always"
       x="-0.027811"
       y="-0.24101999"
       width="1.0556"
       height="1.482"
       id="filter5499-4"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="1.8076857"
         id="feGaussianBlur5501-8" />
    </filter>
    <linearGradient
       inkscape:collect="always"
       x1="-372"
       y1="912.35999"
       x2="-278"
       gradientUnits="userSpaceOnUse"
       y2="798.35999"
       id="linearGradient5531-5">
      <stop
         offset="0"
         style="stop-color:#dc0000"
         id="stop5527-4" />
      <stop
         offset="1"
         style="stop-color:#2d2d2d"
         id="stop5529-8" />
    </linearGradient>
    <filter
       inkscape:collect="always"
       id="filter5537-9"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="4.7824692"
         id="feGaussianBlur5539-8" />
    </filter>
    <linearGradient
       inkscape:collect="always"
       x1="462"
       y1="796.35999"
       x2="372"
       gradientUnits="userSpaceOnUse"
       y2="826.35999"
       id="linearGradient5576-0">
      <stop
         offset="0"
         style="stop-color:#ffffff"
         id="stop5572-9" />
      <stop
         offset="1"
         style="stop-color:#b50000"
         id="stop5574-2" />
    </linearGradient>
    <filter
       inkscape:collect="always"
       x="-0.027948"
       y="-0.12884"
       width="1.0559"
       height="1.2577"
       id="filter5562-8"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="1.9330982"
         id="feGaussianBlur5564-7" />
    </filter>
    <linearGradient
       inkscape:collect="always"
       x1="-176.11"
       y1="763.75"
       x2="-194.14"
       gradientUnits="userSpaceOnUse"
       y2="798.97998"
       id="linearGradient5602-0">
      <stop
         offset="0"
         style="stop-color:#ffffff"
         id="stop5598-4" />
      <stop
         offset="1"
         style="stop-color:#ffffff;stop-opacity:0"
         id="stop5600-5" />
    </linearGradient>
    <linearGradient
       y2="838.21002"
       x2="-5.0507998"
       y1="737.19"
       x1="-183.85001"
       gradientTransform="matrix(0.5,0,0,0.5,207.33,521.29)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3536"
       xlink:href="#linearGradient4970"
       inkscape:collect="always" />
    <linearGradient
       y2="905.08002"
       x2="82.877998"
       y1="946.09003"
       x1="87.455002"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3538"
       xlink:href="#linearGradient4403"
       inkscape:collect="always" />
    <linearGradient
       y2="829.12"
       x2="8.0811996"
       y1="853.35999"
       x1="6.0609002"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3540"
       xlink:href="#linearGradient4231"
       inkscape:collect="always" />
    <linearGradient
       y2="914.97998"
       x2="-173.75"
       y1="1000.8"
       x1="-218.19"
       gradientTransform="matrix(0.47641,0,0,0.47641,201.52,543.15)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3542"
       xlink:href="#linearGradient4409"
       inkscape:collect="always" />
    <linearGradient
       y2="800.92999"
       x2="60"
       y1="863.78998"
       x1="62.856998"
       gradientTransform="matrix(0.5,0,0,0.5,207.33,521.29)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3544"
       xlink:href="#linearGradient5043"
       inkscape:collect="always" />
    <linearGradient
       y2="914.97998"
       x2="-173.75"
       y1="1000.8"
       x1="-218.19"
       gradientTransform="matrix(0.47641,0,0,0.47641,548,545.67)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3546"
       xlink:href="#linearGradient4409"
       inkscape:collect="always" />
    <linearGradient
       y2="738.35999"
       x2="102"
       y1="672.35999"
       x1="34"
       gradientTransform="matrix(0.5,0,0,0.5,207.33,521.29)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3548"
       xlink:href="#linearGradient5604"
       inkscape:collect="always" />
    <linearGradient
       y2="743.78998"
       x2="-188.57001"
       y1="749.51001"
       x1="-322.85999"
       gradientTransform="matrix(0.5,0,0,0.5,207.33,521.29)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3550"
       xlink:href="#linearGradient4973"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.041679,0,0,0.098342,298.23,858)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3552"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.041679,0,0,0.098342,302.09,858)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3554"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.041679,0,0,0.098342,305.95,858)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3556"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.041679,0,0,0.098342,309.04,858)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3558"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.041679,0,0,0.098342,311.36,858)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3560"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.041679,0,0,0.098342,314.45,858)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3562"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,328.56,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3564"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,332.46,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3566"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,336.35,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3568"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,339.47,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3570"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,341.81,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3572"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,344.92,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3574"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,339.85,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3576"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,343.75,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3578"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,347.64,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3580"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,350.76,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3582"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,353.09,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3584"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,356.21,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3586"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,360.17,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3588"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,364.07,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3590"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,367.96,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3592"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,371.08,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3594"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,373.42,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3596"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,376.53,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3598"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,379.36,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3600"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,383.26,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3602"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,387.15,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3604"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,390.27,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3606"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,392.61,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3608"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,395.72,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3610"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="802.35999"
       x2="-256"
       y1="880.35999"
       x1="-376"
       gradientTransform="matrix(0.5,0,0,0.5,207.33,521.29)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3612"
       xlink:href="#linearGradient5519"
       inkscape:collect="always" />
    <linearGradient
       y2="880.35999"
       x2="18"
       y1="928.35999"
       x1="20"
       gradientTransform="matrix(0.5,0,0,0.5,200.33,519.29)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient3614"
       xlink:href="#linearGradient5558"
       inkscape:collect="always" />
    <linearGradient
       y2="905.08002"
       x2="82.877998"
       y1="946.09003"
       x1="87.455002"
       gradientUnits="userSpaceOnUse"
       id="linearGradient2263-0"
       xlink:href="#linearGradient4403"
       inkscape:collect="always" />
    <filter
       inkscape:collect="always"
       x="-0.18461999"
       y="-0.19459"
       width="1.3692"
       height="1.3892"
       id="filter4259-48"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="3.0304577"
         id="feGaussianBlur4261-51" />
    </filter>
    <radialGradient
       inkscape:collect="always"
       r="67.369003"
       gradientTransform="matrix(-0.5273,0.08912,-0.081872,-0.48441,29.283,1453.4)"
       cx="-246.48"
       cy="924.06"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4063"
       id="radialGradient5103-0" />
    <radialGradient
       inkscape:collect="always"
       r="67.369003"
       gradientTransform="matrix(-0.55861,0.094412,-0.086733,-0.51317,26.028,1481.3)"
       cx="-246.48"
       cy="924.06"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4063"
       id="radialGradient5100-9" />
    <linearGradient
       y2="829.12"
       x2="8.0811996"
       y1="853.35999"
       x1="6.0609002"
       gradientUnits="userSpaceOnUse"
       id="linearGradient2265-1"
       xlink:href="#linearGradient4231"
       inkscape:collect="always" />
    <radialGradient
       r="106.39"
       cy="891.90002"
       cx="-61.618999"
       gradientTransform="matrix(-0.93117,0.027936,-0.025856,-0.93225,-288.39,1761.3)"
       gradientUnits="userSpaceOnUse"
       id="radialGradient2267-2"
       xlink:href="#linearGradient4063"
       inkscape:collect="always" />
    <radialGradient
       r="96.925003"
       cy="914.98999"
       cx="-235.02"
       gradientTransform="matrix(1.0511,0.18411,-0.24505,1.399,221.75,-313.78)"
       gradientUnits="userSpaceOnUse"
       id="radialGradient2269-9"
       xlink:href="#linearGradient4281"
       inkscape:collect="always" />
    <radialGradient
       inkscape:collect="always"
       r="67.369003"
       gradientTransform="matrix(-0.5273,0.08912,-0.081872,-0.48441,375.77,1455.9)"
       cx="-246.48"
       cy="924.06"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4063"
       id="radialGradient5037-2" />
    <radialGradient
       inkscape:collect="always"
       r="67.369003"
       gradientTransform="matrix(-0.55861,0.094412,-0.086733,-0.51317,372.51,1483.8)"
       cx="-246.48"
       cy="924.06"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4063"
       id="radialGradient5034-8" />
    <radialGradient
       inkscape:collect="always"
       r="106.39"
       gradientTransform="matrix(-0.93117,0.027936,-0.025856,-0.93225,-288.39,1761.3)"
       cx="-61.618999"
       cy="891.90002"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4063"
       id="radialGradient4293-7" />
    <radialGradient
       inkscape:collect="always"
       r="96.925003"
       gradientTransform="matrix(1.0511,0.18411,-0.24505,1.399,221.75,-313.78)"
       cx="-235.02"
       cy="914.98999"
       gradientUnits="userSpaceOnUse"
       xlink:href="#linearGradient4281"
       id="radialGradient4295-71" />
    <radialGradient
       inkscape:collect="always"
       r="26.997999"
       gradientTransform="matrix(0.11638,0.71316,-0.60712,0.099079,654.59,757)"
       cx="61.941002"
       cy="691.26001"
       gradientUnits="userSpaceOnUse"
       id="radialGradient4976-9">
      <stop
         offset="0"
         style="stop-color:#bdcdd4"
         id="stop5482-3" />
      <stop
         offset="1"
         style="stop-color:#ffffff"
         id="stop5484-2" />
    </radialGradient>
    <filter
       inkscape:collect="always"
       x="-0.011626"
       y="-0.93803"
       width="1.0233001"
       height="2.8761001"
       id="filter4892-8"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="4.0303255"
         id="feGaussianBlur4894-3" />
    </filter>
    <filter
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5350-20"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5352-8" />
    </filter>
    <filter
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5354-0"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5356-4" />
    </filter>
    <filter
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5358-3"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5360-2" />
    </filter>
    <filter
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5362-73"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5364-0" />
    </filter>
    <filter
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5366-6"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5368-1" />
    </filter>
    <filter
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5370-4"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5372-1" />
    </filter>
    <filter
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5374-9"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5376-08" />
    </filter>
    <filter
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5418-4"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5420-2" />
    </filter>
    <filter
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5422-2"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5424-1" />
    </filter>
    <filter
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5426-7"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5428-6" />
    </filter>
    <filter
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5430-94"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5432-5" />
    </filter>
    <filter
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5434-0"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5436-3" />
    </filter>
    <filter
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5438-4"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5440-9" />
    </filter>
    <filter
       inkscape:collect="always"
       x="-0.45910999"
       y="-0.020686001"
       width="1.9182"
       height="1.0414"
       id="filter5442-3"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.48857242"
         id="feGaussianBlur5444-9" />
    </filter>
    <filter
       inkscape:collect="always"
       x="-0.027811"
       y="-0.24101999"
       width="1.0556"
       height="1.482"
       id="filter5499-0"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="1.8076857"
         id="feGaussianBlur5501-88" />
    </filter>
    <linearGradient
       inkscape:collect="always"
       x1="-372"
       y1="912.35999"
       x2="-278"
       gradientUnits="userSpaceOnUse"
       y2="798.35999"
       id="linearGradient5531-9">
      <stop
         offset="0"
         style="stop-color:#dc0000"
         id="stop5527-6" />
      <stop
         offset="1"
         style="stop-color:#2d2d2d"
         id="stop5529-1" />
    </linearGradient>
    <filter
       inkscape:collect="always"
       id="filter5537-5"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="4.7824692"
         id="feGaussianBlur5539-89" />
    </filter>
    <linearGradient
       inkscape:collect="always"
       x1="462"
       y1="796.35999"
       x2="372"
       gradientUnits="userSpaceOnUse"
       y2="826.35999"
       id="linearGradient5576-9">
      <stop
         offset="0"
         style="stop-color:#ffffff"
         id="stop5572-93" />
      <stop
         offset="1"
         style="stop-color:#b50000"
         id="stop5574-5" />
    </linearGradient>
    <filter
       inkscape:collect="always"
       x="-0.027948"
       y="-0.12884"
       width="1.0559"
       height="1.2577"
       id="filter5562-83"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="1.9330982"
         id="feGaussianBlur5564-5" />
    </filter>
    <linearGradient
       inkscape:collect="always"
       x1="-176.11"
       y1="763.75"
       x2="-194.14"
       gradientUnits="userSpaceOnUse"
       y2="798.97998"
       id="linearGradient5602-3">
      <stop
         offset="0"
         style="stop-color:#ffffff"
         id="stop5598-7" />
      <stop
         offset="1"
         style="stop-color:#ffffff;stop-opacity:0"
         id="stop5600-2" />
    </linearGradient>
    <linearGradient
       y2="838.21002"
       x2="-5.0507998"
       y1="737.19"
       x1="-183.85001"
       gradientTransform="matrix(0.5,0,0,0.5,207.33,521.29)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4310"
       xlink:href="#linearGradient4970"
       inkscape:collect="always" />
    <linearGradient
       y2="905.08002"
       x2="82.877998"
       y1="946.09003"
       x1="87.455002"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4312"
       xlink:href="#linearGradient4403"
       inkscape:collect="always" />
    <linearGradient
       y2="829.12"
       x2="8.0811996"
       y1="853.35999"
       x1="6.0609002"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4314"
       xlink:href="#linearGradient4231"
       inkscape:collect="always" />
    <linearGradient
       y2="914.97998"
       x2="-173.75"
       y1="1000.8"
       x1="-218.19"
       gradientTransform="matrix(0.47641,0,0,0.47641,201.52,543.15)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4316"
       xlink:href="#linearGradient4409"
       inkscape:collect="always" />
    <linearGradient
       y2="800.92999"
       x2="60"
       y1="863.78998"
       x1="62.856998"
       gradientTransform="matrix(0.5,0,0,0.5,207.33,521.29)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4318"
       xlink:href="#linearGradient5043"
       inkscape:collect="always" />
    <linearGradient
       y2="914.97998"
       x2="-173.75"
       y1="1000.8"
       x1="-218.19"
       gradientTransform="matrix(0.47641,0,0,0.47641,548,545.67)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4320"
       xlink:href="#linearGradient4409"
       inkscape:collect="always" />
    <linearGradient
       y2="738.35999"
       x2="102"
       y1="672.35999"
       x1="34"
       gradientTransform="matrix(0.5,0,0,0.5,207.33,521.29)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4322"
       xlink:href="#linearGradient5604"
       inkscape:collect="always" />
    <linearGradient
       y2="743.78998"
       x2="-188.57001"
       y1="749.51001"
       x1="-322.85999"
       gradientTransform="matrix(0.5,0,0,0.5,207.33,521.29)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4324"
       xlink:href="#linearGradient4973"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.041679,0,0,0.098342,298.23,858)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4326"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.041679,0,0,0.098342,302.09,858)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4328"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.041679,0,0,0.098342,305.95,858)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4330"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.041679,0,0,0.098342,309.04,858)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4332"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.041679,0,0,0.098342,311.36,858)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4334"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.041679,0,0,0.098342,314.45,858)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4336"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,328.56,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4338"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,332.46,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4340"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,336.35,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4342"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,339.47,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4344"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,341.81,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4346"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,344.92,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4348"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,339.85,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4350"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,343.75,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4352"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,347.64,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4354"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,350.76,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4356"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,353.09,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4358"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,356.21,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4360"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,360.17,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4362"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,364.07,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4364"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,367.96,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4366"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,371.08,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4368"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,373.42,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4370"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,376.53,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4372"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,379.36,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4374"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,383.26,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4376"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,387.15,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4378"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,390.27,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4380"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,392.61,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4382"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="849.51001"
       x2="-780"
       y1="852.35999"
       x1="-831.42999"
       gradientTransform="matrix(0.042048,0,0,0.093523,395.72,862.48)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4384"
       xlink:href="#linearGradient4628"
       inkscape:collect="always" />
    <linearGradient
       y2="802.35999"
       x2="-256"
       y1="880.35999"
       x1="-376"
       gradientTransform="matrix(0.5,0,0,0.5,207.33,521.29)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4386"
       xlink:href="#linearGradient5519"
       inkscape:collect="always" />
    <linearGradient
       y2="880.35999"
       x2="18"
       y1="928.35999"
       x1="20"
       gradientTransform="matrix(0.5,0,0,0.5,200.33,519.29)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4388"
       xlink:href="#linearGradient5558"
       inkscape:collect="always" />
    <linearGradient
       y2="838.21002"
       x2="-5.0507998"
       y1="737.19"
       x1="-183.85001"
       gradientTransform="matrix(0.5,0,0,0.5,207.33,521.29)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4390"
       xlink:href="#linearGradient4970"
       inkscape:collect="always" />
    <linearGradient
       y2="800.92999"
       x2="60"
       y1="863.78998"
       x1="62.856998"
       gradientTransform="matrix(0.5,0,0,0.5,207.33,521.29)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4392"
       xlink:href="#linearGradient5043"
       inkscape:collect="always" />
    <linearGradient
       y2="738.35999"
       x2="102"
       y1="672.35999"
       x1="34"
       gradientTransform="matrix(0.5,0,0,0.5,207.33,521.29)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4394"
       xlink:href="#linearGradient5604"
       inkscape:collect="always" />
    <linearGradient
       y2="743.78998"
       x2="-188.57001"
       y1="749.51001"
       x1="-322.85999"
       gradientTransform="matrix(0.5,0,0,0.5,207.33,521.29)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4396"
       xlink:href="#linearGradient4973"
       inkscape:collect="always" />
    <linearGradient
       y2="802.35999"
       x2="-256"
       y1="880.35999"
       x1="-376"
       gradientTransform="matrix(0.5,0,0,0.5,207.33,521.29)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4398"
       xlink:href="#linearGradient5519"
       inkscape:collect="always" />
    <linearGradient
       y2="880.35999"
       x2="18"
       y1="928.35999"
       x1="20"
       gradientTransform="matrix(0.5,0,0,0.5,200.33,519.29)"
       gradientUnits="userSpaceOnUse"
       id="linearGradient4400"
       xlink:href="#linearGradient5558"
       inkscape:collect="always" />
  </defs>
  <sodipodi:namedview
     inkscape:document-rotation="0"
     showborder="false"
     inkscape:snap-global="false"
     fit-margin-bottom="0"
     fit-margin-right="0"
     fit-margin-left="0"
     fit-margin-top="0"
     inkscape:document-units="px"
     inkscape:pageopacity="0.0"
     inkscape:window-width="1920"
     inkscape:cy="-545.63196"
     inkscape:cx="168.4039"
     inkscape:current-layer="layer1"
     borderopacity="1.0"
     showgrid="false"
     inkscape:window-x="-8"
     inkscape:zoom="2"
     inkscape:window-maximized="1"
     inkscape:window-height="1057"
     pagecolor="#ffffff"
     inkscape:window-y="-8"
     inkscape:pageshadow="2"
     bordercolor="#666666"
     id="base" />
  <g
     transform="translate(-507.64083,-1162.5071)"
     inkscape:groupmode="layer"
     inkscape:label="Layer 1"
     id="layer1">
    <g
       style="stroke-width:3.18132"
       transform="matrix(0.31382064,0,0,0.31484931,348.59139,1024.3881)"
       id="g4980">
      <rect
         style="fill:#5aa02c;fill-opacity:1;stroke:#808080;stroke-width:2.40478;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
         id="rect4266"
         width="743.24408"
         height="1055.244"
         x="508.01877"
         y="439.88516"
         ry="28.41267"
         rx="28.421436" />
      <g
         id="g2261"
         transform="matrix(1.2155564,0,0,1.2164629,587.74071,-519.61417)"
         style="stroke-width:2.6162">
        <path
           d="m 107.83,901 -4.5457,-11.112 c 5.5446,-5.4099 10.837,-11.325 24.244,-1.0102 0,0 5.2576,28.551 15.152,37.881 6.9724,6.5743 17.693,8.8746 27.274,9.0914 12.947,0.29289 25.694,-5.0155 37.376,-10.607 4.4664,-2.1377 7.416,-10.127 12.122,-8.5863 2.0239,0.66256 2.3983,3.9651 2.0203,6.0609 -17.718,40.015 -80.384,30.6 -79.802,16.668 2.1582,-7.9633 -6.367,-35.888 -33.84,-38.386 z"
           inkscape:connector-curvature="0"
           style="fill:url(#linearGradient4390);stroke-width:0"
           sodipodi:nodetypes="cccssaaccc"
           id="path4578" />
        <path
           inkscape:connector-curvature="0"
           style="fill:#0e232e;stroke:#000000;stroke-width:0.551495px"
           sodipodi:nodetypes="sssss"
           d="m 240.69,907.69 c -2.89,-1.8389 -9.8058,-4.8782 -15.637,-6.9591 -0.52945,-0.18892 -1.7035,1.2078 -1.1728,1.3918 3.3511,1.1618 11.327,5.1371 16.224,7.4231 0.51805,0.24185 1.077,-1.5436 0.58641,-1.8558 z"
           id="path4616" />
        <path
           d="m 233.3,876.16 c -4.3057,6.267 -11.422,21.264 -16.295,33.91 -0.44237,1.1481 2.8282,3.6941 3.2589,2.5433 2.7202,-7.2668 12.028,-24.564 17.381,-35.182 0.5663,-1.1234 -3.6143,-2.3356 -4.3452,-1.2716 z"
           inkscape:connector-curvature="0"
           style="fill:#0e232e;stroke:#000000;stroke-width:1.24269px"
           sodipodi:nodetypes="sssss"
           id="path4588" />
        <path
           d="m 0.25,992.42 23.739,1.0102 118.19,1.5152 c 0,0 57.245,-1.545 85.863,-1.0102 10.28,0.19214 20.537,1.9616 30.81,1.5152 44.117,-1.2672 61.545,-1.5717 110.66,-1.0508 l 101.47,-65.114 -26.769,-10.607 -78.792,18.688 c 0,0 -119.84,53.501 -180.31,41.416 -29.824,-5.9599 -74.246,-53.033 -74.246,-53.033 H 35.104 Z"
           inkscape:connector-curvature="0"
           style="fill:#2d2d2d;stroke:#000000;stroke-width:1.3081px"
           sodipodi:nodetypes="cccscccccsccc"
           id="path4557" />
        <ellipse
           transform="matrix(0.44138,0,0,0.48031,37.916,558.7)"
           style="fill:url(#linearGradient2263);stroke-width:0"
           id="path4263"
           cx="104.55079"
           cy="887.20227"
           rx="73.236061"
           ry="64.144684" />
        <ellipse
           transform="matrix(0.5251,0,0,0.5251,29.94,528.22)"
           style="fill:#2d2d2d;stroke-width:0;filter:url(#filter4259)"
           id="path4245"
           cx="104.55079"
           cy="870.02966"
           rx="19.697975"
           ry="18.687822" />
        <ellipse
           transform="matrix(0.34075,0,0,0.34075,85.276,700.17)"
           style="fill:#9eabb0;stroke-width:0"
           id="path4241"
           cx="-2.0203052"
           cy="834.67432"
           rx="19.192898"
           ry="18.687822" />
        <path
           d="m 84.094,951.83 c -18.234,0 -33.015,14.667 -33.015,32.752 0,18.085 14.781,32.736 33.015,32.736 18.234,0 33.015,-14.652 33.015,-32.736 0,-18.085 -14.781,-32.752 -33.015,-32.752 z m 0,2.4301 c 16.88,0 30.569,13.579 30.569,30.322 0,16.742 -13.689,30.306 -30.569,30.306 -16.88,0 -30.569,-13.564 -30.569,-30.306 0,-16.742 13.689,-30.322 30.569,-30.322 z"
           inkscape:connector-curvature="0"
           style="fill:url(#radialGradient5103);stroke:#817e7e;stroke-width:1.84657;stroke-linecap:round;stroke-linejoin:round"
           id="path4039" />
        <path
           inkscape:connector-curvature="0"
           style="fill:url(#radialGradient5100);stroke:#817e7e;stroke-width:1.95618;stroke-linecap:round;stroke-linejoin:round"
           d="m 84.094,949.89 c -19.316,0 -34.975,15.538 -34.975,34.696 0,19.158 15.659,34.68 34.975,34.68 19.316,0 34.975,-15.522 34.975,-34.68 0,-19.158 -15.659,-34.696 -34.975,-34.696 z m 0,2.5744 c 17.883,0 32.384,14.386 32.384,32.122 0,17.736 -14.502,32.106 -32.384,32.106 -17.883,0 -32.384,-14.369 -32.384,-32.106 0,-17.736 14.502,-32.122 32.384,-32.122 z"
           id="path4050" />
        <path
           d="m 84.094,947.46 c -20.67,0 -37.425,16.627 -37.425,37.127 0,20.5 16.756,37.11 37.425,37.11 20.67,0 37.425,-16.609 37.425,-37.11 0,-20.5 -16.756,-37.127 -37.425,-37.127 z m 0,2.7547 c 19.136,0 34.653,15.393 34.653,34.372 0,18.979 -15.518,34.355 -34.653,34.355 -19.136,0 -34.653,-15.376 -34.653,-34.355 0,-18.979 15.518,-34.372 34.653,-34.372 z"
           inkscape:connector-curvature="0"
           style="fill:#666666;stroke:#817e7e;stroke-width:2.09325;stroke-linecap:round;stroke-linejoin:round"
           id="path4054" />
        <g
           transform="matrix(-0.49937,0,0,0.49632,-25.588,625.19)"
           style="stroke:#333333;stroke-width:3.9243"
           id="g3167">
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -214.15,715.98 -14.142,-51.518"
             id="path3169" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -211.12,780.63 -17.173,-48.487"
             id="path3171" />
          <path
             d="m -212.77,718.6 -0.32647,-53.423"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path3173" />
          <path
             d="m -226.57,781.83 -4.038,-51.28"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path3175" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -211.8,721.08 13.511,-51.687"
             id="path3177" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -241.5,778.58 9.3718,-50.578"
             id="path3179" />
          <path
             d="m -211.33,723.27 26.429,-46.429"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path3181" />
          <path
             d="m -254.9,771.13 22.143,-46.429"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path3183" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -211.43,725.52 37.545,-38.006"
             id="path3185" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -265.91,760.47 33.405,-39.116"
             id="path3187" />
          <path
             d="m -212.12,727.66 46.102,-26.994"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path3189" />
          <path
             d="m -273.78,747.32 42.391,-29.137"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path3191" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -213.33,729.54 51.52,-14.14"
             id="path3193" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -277.98,732.57 48.487,-17.173"
             id="path3195" />
          <path
             d="m -214.99,731.05 53.423,-0.32649"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path3197" />
          <path
             d="m -278.22,717.25 51.28,-4.038"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path3199" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -217.47,732.02 51.687,13.511"
             id="path3201" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -274.97,702.32 50.578,9.3718"
             id="path3203" />
          <path
             d="m -219.66,732.5 46.429,26.429"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path3205" />
          <path
             d="m -267.52,688.93 46.429,22.143"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path3207" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -221.91,732.39 38.006,37.545"
             id="path3209" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -256.86,677.92 39.116,33.405"
             id="path3211" />
        </g>
        <g
           transform="matrix(0.47794,0,0,0.48362,188.55,633.61)"
           style="stroke:#4d3636;stroke-width:3.92482"
           id="g3051">
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -214.15,715.98 -14.142,-51.518"
             id="path3007" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -211.12,780.63 -17.173,-48.487"
             id="path3009" />
          <path
             d="m -212.77,718.6 -0.32647,-53.423"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path3011" />
          <path
             d="m -226.57,781.83 -4.038,-51.28"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path3013" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -211.8,721.08 13.511,-51.687"
             id="path3015" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -241.5,778.58 9.3718,-50.578"
             id="path3017" />
          <path
             d="m -211.33,723.27 26.429,-46.429"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path3019" />
          <path
             d="m -254.9,771.13 22.143,-46.429"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path3021" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -211.43,725.52 37.545,-38.006"
             id="path3023" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -265.91,760.47 33.405,-39.116"
             id="path3025" />
          <path
             d="m -212.12,727.66 46.102,-26.994"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path3027" />
          <path
             d="m -273.78,747.32 42.391,-29.137"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path3029" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -213.33,729.54 51.52,-14.14"
             id="path3031" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -277.98,732.57 48.487,-17.173"
             id="path3033" />
          <path
             d="m -214.99,731.05 53.423,-0.32649"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path3035" />
          <path
             d="m -278.22,717.25 51.28,-4.038"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path3037" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -217.47,732.02 51.687,13.511"
             id="path3039" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -274.97,702.32 50.578,9.3718"
             id="path3041" />
          <path
             d="m -219.66,732.5 46.429,26.429"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path3043" />
          <path
             d="m -267.52,688.93 46.429,22.143"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path3045" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -221.91,732.39 38.006,37.545"
             id="path3047" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -256.86,677.92 39.116,33.405"
             id="path3049" />
        </g>
        <ellipse
           transform="matrix(0.2962,0,0,0.2962,85.186,737.36)"
           style="fill:url(#linearGradient2265);stroke:#333333;stroke-width:3.9243;stroke-linecap:round;stroke-linejoin:round"
           id="path4229"
           cx="-2.0203052"
           cy="834.67432"
           rx="19.192898"
           ry="18.687822" />
        <g
           transform="matrix(0.5599,0,0,0.55238,222.05,473.09)"
           style="stroke:#554f4f;stroke-width:2.61562"
           id="g4289">
          <path
             inkscape:connector-curvature="0"
             style="fill:url(#radialGradient2267);stroke:#554f4f;stroke-width:2.61562;stroke-linecap:round;stroke-linejoin:round"
             d="m -245.98,826.55 c -55.038,0 -99.649,44.13 -99.649,98.548 0,54.418 44.611,98.519 99.649,98.519 55.038,0 99.677,-44.102 99.677,-98.519 0,-54.418 -44.639,-98.548 -99.677,-98.548 z m 0,26.945 c 39.99,0 72.414,32.064 72.414,71.603 0,39.539 -32.424,71.575 -72.414,71.575 -39.99,0 -72.387,-32.035 -72.387,-71.575 0,-39.539 32.397,-71.603 72.387,-71.603 z"
             id="path4058" />
          <path
             inkscape:connector-curvature="0"
             style="fill:url(#radialGradient2269);stroke:#554f4f;stroke-width:2.61562;stroke-linecap:round;stroke-linejoin:round"
             d="m -244.61,826.11 c -1.6718,-0.0324 -1.4493,9.1844 -1.8988,15.193 h 0.0339 c 1.0817,0 2.1499,0.029 3.2212,0.0707 -0.21479,-6.0212 0.31566,-15.231 -1.3563,-15.263 z m 1.3563,15.263 c 0.10313,2.8885 0.35196,5.0421 1.1529,5.0878 0.80081,0.0456 1.329,-2.0538 1.7632,-4.9112 -0.96597,-0.072 -1.9409,-0.13863 -2.9161,-0.17665 z m 2.9161,0.17665 c 1.08,0.0806 2.1536,0.19581 3.2212,0.318 0.24291,-6.0201 1.5296,-15.176 -0.13562,-15.334 -1.6648,-0.15802 -2.1804,9.0588 -3.0856,15.016 z m 3.2212,0.318 c -0.11639,2.8848 -0.0146,5.0527 0.77989,5.1585 0.79573,0.10599 1.4855,-1.985 2.1362,-4.8052 -0.97056,-0.14693 -1.9344,-0.24097 -2.9161,-0.35331 z m 2.9161,0.35331 c 1.0702,0.16202 2.1315,0.3621 3.1873,0.56531 0.70049,-5.9846 2.6651,-15.016 1.0172,-15.299 -1.6474,-0.2827 -2.8498,8.861 -4.2045,14.733 z m 3.1873,0.56531 c -0.33604,2.871 -0.37794,5.0638 0.40689,5.2292 0.78191,0.16473 1.6161,-1.849 2.4752,-4.5932 -0.95558,-0.22034 -1.9138,-0.44961 -2.8821,-0.63599 z m 2.8821,0.63599 c 1.0556,0.2434 2.1142,0.49329 3.1534,0.77731 1.1507,-5.9147 3.7941,-14.786 2.1701,-15.193 -1.6246,-0.40672 -3.5227,8.6637 -5.3235,14.416 z m 3.1534,0.77731 c -0.54956,2.8248 -0.76767,5.0059 0,5.2291 0.7684,0.22344 1.7471,-1.7381 2.8143,-4.4165 -0.92615,-0.2895 -1.874,-0.55565 -2.8143,-0.81262 z m 2.8143,0.81262 c 1.0302,0.32204 2.0399,0.66323 3.0517,1.0246 1.597,-5.8112 4.9114,-14.453 3.3229,-14.981 -1.5878,-0.52739 -4.143,8.3555 -6.3746,13.956 z m 3.0517,1.0246 c -0.76267,2.7752 -1.1215,4.9135 -0.37299,5.1938 0.74738,0.27995 1.8894,-1.5534 3.1534,-4.1338 -0.91975,-0.36741 -1.8442,-0.72553 -2.7804,-1.06 z m 2.7804,1.06 c 1.007,0.40228 1.9976,0.83106 2.9838,1.272 2.0314,-5.6747 5.988,-14.087 4.4419,-14.733 -1.545,-0.64609 -4.773,8.0462 -7.4257,13.462 z m 2.9838,1.272 c -0.96564,2.6975 -1.5365,4.753 -0.81377,5.0878 0.72165,0.33431 2.0078,-1.3857 3.4586,-3.8512 -0.87151,-0.42808 -1.7557,-0.83918 -2.6448,-1.2366 z m 2.6448,1.2366 c 0.96152,0.4723 1.9429,0.93991 2.8822,1.4486 2.4572,-5.5056 7.0533,-13.584 5.5608,-14.345 -1.4936,-0.76163 -5.3841,7.6979 -8.443,12.896 z m 2.8822,1.4486 c -1.1683,2.6178 -1.8482,4.6642 -1.1529,5.0525 0.69397,0.38753 2.0625,-1.2557 3.6959,-3.6039 -0.8429,-0.49709 -1.6806,-0.98145 -2.543,-1.4486 z m 2.543,1.4486 c 0.93425,0.55097 1.8372,1.1445 2.7465,1.7313 2.8619,-5.3066 8.1152,-13.082 6.6798,-13.956 -1.4317,-0.87197 -5.9809,7.2718 -9.4263,12.225 z m 2.7465,1.7313 c -1.3457,2.4952 -2.1847,4.4396 -1.5258,4.8758 0.65669,0.43479 2.1472,-1.0638 3.9333,-3.2505 -0.79343,-0.55349 -1.5932,-1.0998 -2.4074,-1.6253 z m 2.4074,1.6253 c 0.89266,0.62269 1.7794,1.2508 2.6448,1.9079 3.2542,-5.0768 9.0979,-12.445 7.7309,-13.426 -1.367,-0.98127 -6.559,6.8453 -10.376,11.518 z m 2.6448,1.9079 c -1.5229,2.3758 -2.4867,4.2515 -1.8649,4.7345 0.61993,0.48153 2.2312,-0.89633 4.1706,-2.9326 -0.75602,-0.61873 -1.5268,-1.2104 -2.3057,-1.8019 z m 2.3057,1.8019 c 0.8283,0.67785 1.6415,1.375 2.4414,2.0846 3.6257,-4.819 10.074,-11.741 8.782,-12.826 -1.2902,-1.0828 -7.0611,6.371 -11.223,10.741 z m 2.4414,2.0846 c -1.6829,2.2367 -2.7501,4.0324 -2.1701,4.5578 0.57819,0.52375 2.2344,-0.71318 4.3062,-2.5792 -0.69861,-0.66606 -1.4136,-1.3377 -2.1362,-1.9786 z m 2.1362,1.9786 c 0.78547,0.74887 1.5515,1.5169 2.3057,2.2966 3.9737,-4.5351 10.976,-11.007 9.7654,-12.19 -1.2075,-1.1799 -7.5864,5.8539 -12.071,9.893 z m 2.3057,2.2966 c -1.8212,2.0786 -3.009,3.7478 -2.4752,4.3105 0.53371,0.56256 2.2515,-0.50216 4.4419,-2.1906 -0.64738,-0.7195 -1.2936,-1.4242 -1.9666,-2.1199 z m 1.9666,2.1199 c 0.72923,0.81043 1.4744,1.5983 2.1701,2.4379 4.307,-4.2231 11.764,-10.177 10.647,-11.448 -1.1172,-1.2707 -8.0368,5.3249 -12.817,9.0097 z m 2.1701,2.4379 c -1.974,1.9356 -3.3038,3.4973 -2.8143,4.0985 0.48671,0.59779 2.2832,-0.33317 4.5775,-1.8373 -0.58076,-0.75413 -1.1561,-1.5286 -1.7632,-2.2613 z m 1.7632,2.2613 c 0.66776,0.86706 1.3007,1.7554 1.9327,2.6499 4.6043,-3.8919 12.585,-9.3118 11.562,-10.67 -1.02,-1.3545 -8.4477,4.7114 -13.495,8.0204 z m 1.9327,2.6499 c -2.0735,1.7527 -3.4548,3.1872 -3.0178,3.8159 0.43555,0.62652 2.2772,-0.14489 4.6453,-1.4486 -0.52538,-0.80374 -1.074,-1.5838 -1.6276,-2.3672 z m 1.6276,2.3672 c 0.59415,0.9089 1.1722,1.822 1.7293,2.7559 4.8809,-3.5371 13.329,-8.3514 12.41,-9.787 -0.91817,-1.4338 -8.8546,4.1216 -14.139,7.0311 z m 1.7293,2.7559 c -2.1782,1.5785 -3.6407,2.8776 -3.2551,3.5332 0.38091,0.64765 2.2726,-0.004 4.6792,-1.0953 -0.45464,-0.82804 -0.94092,-1.6279 -1.4241,-2.4379 z m 1.4241,2.4379 c 0.52864,0.96283 1.0368,1.9463 1.5258,2.9326 5.1266,-3.1644 13.967,-7.3615 13.156,-8.8683 -0.81198,-1.5088 -9.1931,3.4488 -14.682,5.9358 z m 1.5258,2.9326 c -2.2573,1.3933 -3.824,2.5741 -3.4925,3.2506 0.32625,0.66563 2.2448,0.17614 4.6792,-0.70664 -0.3857,-0.85292 -0.77189,-1.7072 -1.1868,-2.5439 z m 1.1868,2.5439 c 0.45698,1.0106 0.87345,2.0415 1.2885,3.0739 5.3246,-2.7834 14.604,-6.3333 13.902,-7.9144 -0.69893,-1.5745 -9.5324,2.7887 -15.191,4.8405 z m 1.2885,3.0739 c -2.2592,1.181 -3.9002,2.251 -3.6281,2.9325 0.27248,0.68268 2.1814,0.33166 4.6453,-0.35331 -0.31478,-0.87086 -0.67289,-1.7227 -1.0172,-2.5792 z m 1.0172,2.5792 c 0.37906,1.0487 0.74983,2.1114 1.085,3.1799 5.5142,-2.3782 15.093,-5.2564 14.512,-6.8898 -0.57927,-1.6283 -9.8033,2.0991 -15.597,3.7099 z m 1.085,3.1799 c -2.3205,1.0008 -4.0158,1.9185 -3.7976,2.6146 0.21623,0.68984 2.0966,0.47846 4.5436,0 -0.24103,-0.87328 -0.47582,-1.7535 -0.74595,-2.6146 z m 0.74595,2.6146 c 0.29585,1.0719 0.5627,2.1612 0.81378,3.2506 5.6584,-1.9682 15.581,-4.1096 15.123,-5.7945 -0.45697,-1.6788 -10.047,1.3924 -15.937,2.5439 z m 0.81378,3.2506 c -2.3221,0.80773 -3.9933,1.5986 -3.8316,2.2966 0.16075,0.69353 1.9891,0.66848 4.408,0.38865 -0.17493,-0.90294 -0.37089,-1.7934 -0.57644,-2.6852 z m 0.57644,2.6852 c 0.21475,1.1085 0.44269,2.1969 0.61033,3.3212 5.7816,-1.5431 15.928,-2.9788 15.597,-4.6992 -0.33009,-1.719 -10.26,0.68988 -16.208,1.3779 z m 0.61033,3.3212 c -2.3447,0.62579 -4.0406,1.241 -3.9333,1.9433 0.1061,0.69399 1.8912,0.83119 4.2724,0.74197 -0.10228,-0.89597 -0.20689,-1.7986 -0.33908,-2.6852 z m 0.33908,2.6852 c 0.12708,1.1128 0.22506,2.2303 0.30516,3.3565 5.8559,-1.1212 16.239,-1.7428 16.038,-3.4979 -0.19956,-1.7486 -10.374,-0.0823 -16.343,0.14133 z m 0.30516,3.3565 c -2.3058,0.44149 -3.9519,0.96708 -3.8994,1.6606 0.0521,0.68644 1.7269,0.92912 4.035,1.0246 -0.0335,-0.89992 -0.0722,-1.7932 -0.13562,-2.6852 z m 0.13562,2.6852 c 0.0365,0.98504 0.0678,1.974 0.0678,2.9679 0,0.14188 7.7e-4,0.28227 0,0.42397 5.9003,-0.69454 16.444,-0.51878 16.377,-2.2966 -0.0666,-1.7722 -10.499,-0.84927 -16.445,-1.0953 z m 0.0678,3.3919 c -2.2644,0.26655 -3.899,0.65985 -3.8994,1.3426 -3.3e-4,0.67641 1.6013,1.074 3.8315,1.3426 0.0346,-0.89088 0.063,-1.7871 0.0678,-2.6852 z m -0.0678,2.6852 c -0.0443,1.139 -0.11112,2.2656 -0.20344,3.3919 5.9106,-0.26845 16.58,0.69526 16.649,-1.0953 0.0688,-1.7841 -10.559,-1.5875 -16.445,-2.2966 z m -0.20344,3.3919 c -2.2098,0.10047 -3.8142,0.35665 -3.8655,1.0246 -0.0509,0.6623 1.4556,1.1595 3.5942,1.59 0.1004,-0.86818 0.19941,-1.7381 0.27126,-2.6146 z m -0.27126,2.6146 c -0.13102,1.1332 -0.26209,2.2737 -0.44079,3.3919 5.8937,0.15957 16.612,1.9668 16.818,0.17666 0.20562,-1.7859 -10.59,-2.4035 -16.377,-3.5685 z m -0.44079,3.3919 c -2.1584,-0.0585 -3.6973,0.0897 -3.7976,0.74197 -0.0992,0.6442 1.2887,1.2574 3.3229,1.8373 0.16616,-0.8552 0.3365,-1.7145 0.4747,-2.5792 z m -0.4747,2.5792 c -0.21901,1.1272 -0.47933,2.2468 -0.74598,3.3565 5.8254,0.5657 16.575,3.2718 16.92,1.484 0.34315,-1.7776 -10.524,-3.23 -16.174,-4.8405 z m -0.74598,3.3565 c -2.0524,-0.19928 -3.5166,-0.16549 -3.662,0.45932 -0.14419,0.61937 1.1149,1.3021 3.0178,2.0139 0.22518,-0.81852 0.44512,-1.6444 0.64424,-2.4733 z m -0.64424,2.4733 c -0.3099,1.1266 -0.62513,2.2504 -0.98333,3.3566 5.738,0.97369 16.403,4.5248 16.886,2.7559 0.48151,-1.7621 -10.437,-4.0678 -15.903,-6.1124 z m -0.98333,3.3566 c -1.9679,-0.33395 -3.3715,-0.38801 -3.5603,0.212 -0.18781,0.597 0.95379,1.3538 2.7465,2.1906 0.28785,-0.79903 0.55129,-1.592 0.81376,-2.4026 z m -0.81376,2.4026 c -0.39653,1.1007 -0.84488,2.1728 -1.2885,3.2505 5.6095,1.359 16.196,5.8068 16.818,4.0632 0.6175,-1.7304 -10.267,-4.8575 -15.53,-7.3137 z m -1.2885,3.2505 c -1.8542,-0.44918 -3.1633,-0.56817 -3.3907,0 -0.22554,0.56342 0.775,1.3646 2.4074,2.2966 0.34094,-0.75878 0.66594,-1.5255 0.98331,-2.2966 z m -0.98331,2.2966 c -0.48023,1.0688 -0.96605,2.1366 -1.4919,3.1799 5.4619,1.7408 15.823,7.0738 16.581,5.3705 0.75507,-1.6965 -10.094,-5.6991 -15.089,-8.5504 z m -1.4919,3.1799 c -1.7559,-0.55966 -3.0246,-0.75004 -3.289,-0.21199 -0.26115,0.53141 0.64728,1.3846 2.1362,2.4026 0.39316,-0.72281 0.78189,-1.4546 1.1528,-2.1906 z m -1.1528,2.1906 c -0.56636,1.0412 -1.1519,2.0611 -1.7632,3.0739 5.2747,2.0899 15.348,8.3349 16.242,6.6778 0.88897,-1.648 -9.7709,-6.533 -14.478,-9.7517 z m -1.7632,3.0739 c -1.6298,-0.64574 -2.7901,-0.88991 -3.0856,-0.38867 -0.28973,0.49152 0.48355,1.3674 1.7971,2.4379 0.43591,-0.67499 0.87284,-1.3606 1.2885,-2.0493 z m -1.2885,2.0493 c -0.64982,1.0062 -1.3407,1.9932 -2.0344,2.9679 5.0502,2.3982 14.839,9.5542 15.869,7.9497 1.0234,-1.5947 -9.4726,-7.3629 -13.834,-10.918 z m -2.0344,2.9679 c -1.4809,-0.70324 -2.5625,-1.0241 -2.8821,-0.56532 -0.31749,0.45569 0.32534,1.3866 1.4919,2.5086 0.48225,-0.63628 0.92702,-1.2925 1.3902,-1.9433 z m -1.3902,1.9433 c -0.73365,0.96799 -1.4613,1.9288 -2.2379,2.8619 4.8324,2.7318 14.171,10.681 15.326,9.151 1.151,-1.5254 -9.0752,-8.1532 -13.088,-12.013 z m -2.2379,2.8619 c -1.3847,-0.78279 -2.3992,-1.1677 -2.7465,-0.74196 -0.34026,0.41706 0.20736,1.3212 1.2207,2.4732 0.50675,-0.57462 1.0357,-1.1423 1.5258,-1.7313 z m -1.5258,1.7313 c -0.8058,0.91369 -1.6286,1.809 -2.4753,2.6852 4.5693,2.9959 13.4,11.842 14.682,10.388 1.2751,-1.4474 -8.5759,-8.9449 -12.207,-13.073 z m -2.4753,2.6852 c -1.2454,-0.81658 -2.2128,-1.1958 -2.577,-0.81266 -0.36197,0.38082 0.10515,1.2569 0.98333,2.4379 0.54491,-0.53281 1.0642,-1.0773 1.5936,-1.6252 z m -1.5936,1.6252 c -0.89166,0.87188 -1.8146,1.7137 -2.7465,2.5439 4.3018,3.261 12.605,12.883 14.004,11.518 1.3908,-1.3564 -8.0106,-9.6962 -11.257,-14.062 z m -2.7465,2.5439 c -1.1313,-0.85755 -1.9922,-1.2987 -2.3735,-0.95396 -0.37389,0.33803 -0.007,1.2005 0.71206,2.3672 0.5557,-0.46874 1.119,-0.93001 1.6615,-1.4133 z m -1.6615,1.4133 c -0.96131,0.8109 -1.9503,1.6011 -2.95,2.3673 4.0103,3.4734 11.642,13.917 13.156,12.649 1.5065,-1.2618 -7.4018,-10.468 -10.206,-15.016 z m -2.95,2.3673 c -1.0041,-0.86972 -1.8131,-1.3276 -2.204,-1.0246 -0.39217,0.30398 -0.13183,1.1799 0.4747,2.3672 0.58957,-0.42707 1.1521,-0.90024 1.7293,-1.3426 z m -1.7293,1.3426 c -1.018,0.73742 -2.0659,1.4294 -3.1195,2.1199 3.7236,3.7177 10.628,14.82901 12.241,13.67401 1.6047,-1.14961 -6.7086,-11.07101 -9.1211,-15.79301 z m -3.1195,2.1199 c -0.91212,-0.91066 -1.6288,-1.3986 -2.0344,-1.1306 -0.40201,0.26557 -0.24215,1.1236 0.23735,2.2966 0.61057,-0.37726 1.1976,-0.77308 1.7971,-1.166 z m -1.7971,1.166 c -1.0815,0.66828 -2.1739,1.2898 -3.289,1.908 3.4197,3.9218 9.4836,15.70001 11.189,14.66301 1.6999,-1.0332 -5.9212,-11.72901 -7.9005,-16.57101 z m -3.289,1.908 c -0.81353,-0.93297 -1.4839,-1.3971 -1.8988,-1.166 -0.40945,0.22813 -0.32782,1.0025 0.0339,2.1552 0.62448,-0.32506 1.2503,-0.64861 1.8649,-0.98928 z m -1.8649,0.98928 c -1.1324,0.5895 -2.295,1.159 -3.4586,1.6959 3.1054,4.098 8.3482,16.45703 10.138,15.54603 1.7854,-0.9081 -5.1342,-12.31703 -6.6798,-17.24203 z m -3.4586,1.6959 c -0.71663,-0.94569 -1.3424,-1.431 -1.7632,-1.2366 -0.4161,0.19227 -0.39079,0.95079 -0.13564,2.0846 0.63849,-0.27483 1.2691,-0.55735 1.8988,-0.84797 z m -1.8988,0.84797 c -1.1935,0.51379 -2.4053,0.99118 -3.6281,1.4486 2.7831,4.2288 7.118,17.10196 8.9855,16.32296 1.859,-0.7752 -4.2363,-12.79096 -5.3574,-17.77196 z m -3.6281,1.4486 c -0.61783,-0.93876 -1.1389,-1.4644 -1.5597,-1.3073 -0.41946,0.15665 -0.49275,0.90868 -0.33907,2.0139 0.639,-0.22088 1.2673,-0.47041 1.8988,-0.70666 z m -1.8988,0.70666 c -1.2181,0.42105 -2.4519,0.80193 -3.6959,1.166 2.4563,4.3723 5.8017,17.633 7.7309,16.995 1.9228,-0.6366 -3.3399,-13.162 -4.035,-18.161 z m -3.6959,1.166 c -0.53312,-0.94899 -1.0343,-1.5007 -1.458,-1.3779 -0.42072,0.12189 -0.56788,0.87078 -0.5086,1.9432 0.65902,-0.17484 1.3144,-0.37444 1.9666,-0.56531 z m -1.9666,0.56531 c -1.2613,0.33466 -2.5466,0.60852 -3.8316,0.88327 2.127,4.46142 4.4921,18.09002 6.4763,17.59502 1.9752,-0.4923 -2.3693,-13.495 -2.6448,-18.47902 z m -3.8316,0.88327 c -0.4459,-0.93525 -0.86884,-1.5012 -1.2885,-1.4132 -0.41764,0.0875 -0.61427,0.77556 -0.64424,1.802 0.64652,-0.12198 1.2918,-0.2516 1.9327,-0.38877 z m -1.9327,0.38877 c -1.2908,0.24328 -2.5875,0.45426 -3.8994,0.63588 1.788,4.57657 3.0992,18.43497 5.12,18.08997 2.0182,-0.3443 -1.3644,-13.802 -1.2207,-18.72597 z m -3.8994,0.63588 c -0.36984,-0.94666 -0.76634,-1.5041 -1.1868,-1.4486 -0.41771,0.0554 -0.63631,0.69735 -0.74598,1.696 0.65124,-0.0742 1.2863,-0.15797 1.9327,-0.24745 z m -1.9327,0.24745 c -1.3121,0.14946 -2.6367,0.26672 -3.9672,0.35312 1.4435,4.6673 1.7179,18.636 3.7638,18.444 2.044,-0.1922 -0.33086,-13.932 0.20344,-18.79697 z m -3.9672,0.35312 c -0.29468,-0.95279 -0.63225,-1.54277 -1.0511,-1.51917 -0.42054,0.0236 -0.69635,0.67387 -0.8816,1.66047 0.64621,-0.027 1.2906,-0.1 1.9327,-0.1413 z m -1.9327,0.1413 c -1.1382,0.048 -2.2747,0.07 -3.4246,0.07 h -0.54254 c 1.0797,4.776 0.28709,18.658 2.3396,18.62 2.0529,-0.039 0.72451,-13.881 1.6275,-18.691 z m -3.9672,0.07 c -0.22172,-0.98073 -0.52789,-1.61707 -0.9494,-1.62517 -0.41901,-0.01 -0.72426,0.59349 -0.98333,1.55457 0.64452,0.02 1.2845,0.066 1.9327,0.07 z m -1.9327,-0.07 c -1.3401,-0.039 -2.6782,-0.1077 -4.001,-0.212 0.7154,4.8395 -1.1347,18.644 0.91548,18.761 2.052,0.1174 1.815,-13.835 3.0856,-18.549 z m -4.001,-0.212 c -0.14666,-0.99183 -0.39476,-1.62077 -0.8138,-1.66047 -0.41813,-0.0401 -0.78695,0.5411 -1.1189,1.484 0.63854,0.0653 1.2898,0.12617 1.9327,0.17657 z m -1.9327,-0.17651 c -1.3174,-0.13501 -2.6355,-0.2623 -3.9333,-0.45936 0.30978,4.94517 -2.5333,18.45697 -0.50862,18.72597 2.0363,0.271 2.8181,-13.655 4.4419,-18.26697 z m -3.9333,-0.45936 c -0.0654,-1.0452 -0.25269,-1.7289 -0.67816,-1.8019 -0.42202,-0.0729 -0.84416,0.49994 -1.2546,1.4486 0.64952,0.11498 1.2779,0.25384 1.9327,0.35332 z m -1.9327,-0.35332 c -1.3035,-0.23054 -2.5846,-0.48548 -3.8655,-0.77732 -0.0719,4.96181 -3.9299,18.19901 -1.9327,18.62001 2.0018,0.4216 3.8373,-13.31 5.7982,-17.84301 z m -3.8655,-0.77732 c 0.0151,-1.0526 -0.12255,-1.7674 -0.54251,-1.8726 -0.42702,-0.10697 -0.92774,0.44942 -1.4241,1.4133 0.64949,0.16508 1.3109,0.30991 1.9666,0.45927 z m -1.9666,-0.45927 c -1.2619,-0.3208 -2.5266,-0.64574 -3.7637,-1.0246 -0.50939,5.02108 -5.268,17.73598 -3.3229,18.30198 1.9525,0.568 4.7935,-12.824 7.0867,-17.27698 z m -3.7637,-1.0246 c 0.11263,-1.1101 0.052,-1.8374 -0.37298,-1.9786 -0.42212,-0.14022 -0.95028,0.3647 -1.5258,1.3073 0.6366,0.21255 1.2552,0.47418 1.8988,0.6713 z m -1.8988,-0.6713 c -1.2471,-0.4164 -2.4771,-0.86833 -3.6959,-1.3426 -0.89058,4.9692 -6.6094,17.30898 -4.7131,18.01898 1.8993,0.7115 5.7842,-12.378 8.4091,-16.67698 z m -3.6959,-1.3426 c 0.19703,-1.0994 0.20981,-1.8411 -0.20345,-2.0139 -0.42771,-0.17888 -1.0142,0.33595 -1.6954,1.3073 0.62125,0.25989 1.2697,0.46183 1.8988,0.70664 z m -1.8988,-0.70664 c -1.1842,-0.4954 -2.3381,-1.041 -3.4925,-1.59 -1.3382,4.9564 -7.8921,16.64502 -6.0694,17.48902 1.8226,0.8444 6.6104,-11.69002 9.5619,-15.89902 z m -3.4925,-1.59 c 0.3088,-1.1438 0.34508,-1.9447 -0.0678,-2.1553 -0.41571,-0.21196 -1.0372,0.30721 -1.7971,1.2366 0.61453,0.31187 1.2413,0.62209 1.8649,0.91864 z m -1.8649,-0.91864 c -1.1475,-0.58232 -2.2761,-1.2029 -3.3908,-1.8373 -1.7554,4.877 -9.103,15.91396 -7.3579,16.88896 1.7505,0.9775 7.4832,-11.05696 10.749,-15.05096 z m -3.3908,-1.8373 c 0.41514,-1.1533 0.50537,-1.9801 0.10166,-2.2259 -0.41059,-0.25007 -1.1026,0.24309 -1.9666,1.166 0.61369,0.37128 1.2405,0.70461 1.8649,1.06 z m -1.8649,-1.06 c -1.0818,-0.65448 -2.14,-1.3112 -3.1873,-2.0139 -2.2115,4.7917 -10.195,15.08896 -8.5447,16.18196 1.6592,1.0986 8.1549,-10.34696 11.732,-14.16796 z m -3.1873,-2.0139 c 0.54872,-1.1889 0.73799,-2.0456 0.33909,-2.3319 -0.39906,-0.28643 -1.1407,0.16648 -2.1023,1.06 0.58985,0.4193 1.1615,0.8682 1.7632,1.272 z m -1.7632,-1.272 c -1.0236,-0.72756 -2.031,-1.4881 -3.0178,-2.2612 -2.5943,4.6255 -11.29,14.229 -9.7315,15.44 1.5626,1.2136 8.8754,-9.5793 12.749,-13.179 z m -3.0178,-2.2612 c 0.65198,-1.1625 0.92238,-2.0484 0.54252,-2.3672 -0.38539,-0.32342 -1.2076,0.12922 -2.2718,0.98931 0.5662,0.46912 1.15,0.92407 1.7293,1.3779 z m -1.7293,-1.3779 c -0.95598,-0.79212 -1.8969,-1.6035 -2.8143,-2.4379 -3.0257,4.4568 -12.232,13.209 -10.783,14.521 1.4569,1.3197 9.4381,-8.7226 13.597,-12.084 z m -2.8143,-2.4379 c 0.79465,-1.1705 1.181,-2.0791 0.81377,-2.4379 -0.37256,-0.36408 -1.2875,0.0824 -2.4752,0.91863 0.54082,0.52022 1.1061,1.0141 1.6615,1.5193 z m -1.6615,-1.5193 c -0.88518,-0.8515 -1.7321,-1.7239 -2.577,-2.6146 -3.4357,4.2447 -13.134,12.162 -11.8,13.568 1.34,1.4125 9.9367,-7.8275 14.377,-10.953 z m -2.577,-2.6146 c 0.93939,-1.1606 1.4348,-2.0754 1.085,-2.4732 -0.35415,-0.4028 -1.3392,-0.0147 -2.6448,0.77731 0.51273,0.57255 1.0308,1.1383 1.5597,1.6959 z m -1.5597,-1.6959 c -0.81768,-0.91307 -1.6323,-1.8415 -2.4074,-2.7912 -3.8346,3.9962 -13.859,11.055 -12.647,12.543 1.2189,1.4971 10.355,-6.9001 15.055,-9.7516 z m -2.4074,-2.7912 c 1.0956,-1.1418 1.7199,-2.0354 1.3902,-2.4732 -0.33147,-0.44017 -1.3933,-0.0975 -2.8143,0.63598 0.47352,0.61597 0.93306,1.2356 1.4241,1.8373 z m -1.4241,-1.8373 c -0.73424,-0.9552 -1.4792,-1.909 -2.1701,-2.8972 -4.2155,3.7105 -14.545,9.8893 -13.461,11.448 1.0931,1.5724 10.692,-6.0005 15.631,-8.5503 z m -2.1701,-2.8972 c 1.2616,-1.1105 2.0016,-1.995 1.6954,-2.4732 -0.30668,-0.47888 -1.4699,-0.21096 -3.0178,0.45933 0.43934,0.67076 0.86339,1.3572 1.3224,2.0139 z m -1.3224,-2.0139 c -0.65079,-0.99362 -1.2584,-2.0151 -1.8649,-3.0386 -4.5214,3.3853 -15.165,8.689 -14.207,10.317 0.96144,1.6347 10.909,-5.0426 16.072,-7.2784 z m -1.8649,-3.0386 c 1.3797,-1.033 2.2058,-1.8951 1.9327,-2.4026 -0.27832,-0.5172 -1.4746,-0.27716 -3.1534,0.31799 0.39064,0.70779 0.8091,1.39 1.2207,2.0846 z m -1.2207,-2.0846 c -0.57127,-1.035 -1.1361,-2.0823 -1.6615,-3.1445 -4.8653,3.0333 -15.603,7.4431 -14.784,9.1157 0.82611,1.6854 11.079,-4.0689 16.445,-5.9711 z m -1.6615,-3.1445 c 1.5713,-0.97961 2.5158,-1.8175 2.2718,-2.3672 -0.24489,-0.55171 -1.5267,-0.3938 -3.3229,0.10598 0.34482,0.75649 0.68327,1.5175 1.0511,2.2612 z m -1.0511,-2.2612 c -0.48354,-1.0608 -0.95303,-2.1303 -1.3902,-3.2152 -5.1337,2.6522 -16.011,6.1984 -15.326,7.9144 0.68955,1.7275 11.181,-3.1588 16.716,-4.6991 z m -1.3902,-3.2152 c 1.7112,-0.88408 2.7834,-1.6811 2.577,-2.2612 -0.2085,-0.58608 -1.5727,-0.50009 -3.4925,-0.10599 0.29147,0.79692 0.59924,1.5824 0.91551,2.3673 z m -0.91551,-2.3672 c -0.39903,-1.091 -0.76744,-2.1733 -1.1189,-3.2859 -5.3762,2.246 -16.28,4.9668 -15.733,6.7131 0.55068,1.7567 11.17,-2.2608 16.852,-3.4272 z m -1.1189,-3.2859 c 1.8591,-0.77667 3.0483,-1.5093 2.8821,-2.1199 -0.16681,-0.6128 -1.5834,-0.61914 -3.5942,-0.35333 0.23138,0.82592 0.45462,1.6584 0.71206,2.4732 z m -0.71206,-2.4732 c -0.30771,-1.0984 -0.62096,-2.2043 -0.8816,-3.3212 -5.5698,1.8225 -16.448,3.6713 -16.038,5.4411 0.41259,1.7802 11.134,-1.355 16.92,-2.1199 z m -0.8816,-3.3212 c 1.9798,-0.64782 3.3091,-1.3092 3.1873,-1.9433 -0.12275,-0.63917 -1.6227,-0.72941 -3.7298,-0.60065 0.16909,0.85159 0.34612,1.7023 0.54251,2.5439 z m -0.54251,-2.5439 c -0.22064,-1.1109 -0.43708,-2.2296 -0.61034,-3.3566 -5.7258,1.3834 -16.548,2.3861 -16.276,4.1692 0.27365,1.7905 11.022,-0.45432 16.886,-0.81265 z m -0.61034,-3.3566 c 2.0898,-0.50489 3.5332,-1.0772 3.4586,-1.7313 -0.0751,-0.6588 -1.6221,-0.86008 -3.7976,-0.88329 0.10439,0.8714 0.20647,1.752 0.33909,2.6146 z m -0.33909,-2.6146 c -0.13601,-1.136 -0.25207,-2.277 -0.33907,-3.4272 -5.8417,0.93312 -16.547,1.1809 -16.411,2.9679 0.13586,1.7937 10.848,0.39635 16.75,0.45931 z m -0.33907,-3.4272 c 2.1836,-0.34879 3.755,-0.77927 3.7298,-1.4486 -0.0253,-0.67583 -1.6611,-0.98143 -3.8994,-1.166 0.0372,0.87766 0.10375,1.7445 0.16954,2.6146 z m -0.16954,-2.6146 c -0.0477,-1.1242 -0.0678,-2.2561 -0.0678,-3.3919 0,-0.0119 -10e-6,-0.0236 0,-0.0353 -5.9223,0.47201 -16.478,-0.0832 -16.479,1.6959 -8.9e-4,1.7855 10.638,1.2442 16.547,1.7313 z m -0.0678,-3.4272 c 2.2726,-0.18114 3.9409,-0.48377 3.9672,-1.166 0.0265,-0.68547 -1.6298,-1.1294 -3.8994,-1.484 -0.0296,0.88035 -0.0674,1.7626 -0.0678,2.6499 z m 0.0678,-2.6499 c 0.0382,-1.1379 0.0833,-2.2665 0.16954,-3.3919 -5.9622,0.006 -16.276,-1.3026 -16.411,0.45932 -0.13586,1.7707 10.367,2.0148 16.242,2.9326 z m 0.16954,-3.3919 c 2.3444,-0.002 4.0912,-0.19302 4.1706,-0.88331 0.0804,-0.69853 -1.6056,-1.267 -3.9333,-1.8019 -0.0989,0.89073 -0.16851,1.7859 -0.23735,2.6852 z m 0.23735,-2.6852 c 0.12323,-1.1102 0.27181,-2.2252 0.44081,-3.3212 -5.9649,-0.46416 -16.009,-2.5106 -16.276,-0.77731 -0.26748,1.7384 10.013,2.7605 15.835,4.0985 z m 0.44081,-3.3212 c 2.4094,0.18747 4.2059,0.13026 4.3402,-0.56533 0.13501,-0.69948 -1.5365,-1.402 -3.8655,-2.1199 -0.17091,0.89801 -0.33464,1.7769 -0.4747,2.6852 z m 0.4747,-2.6852 c 0.21056,-1.1063 0.45566,-2.1963 0.71205,-3.2859 -5.9236,-0.92755 -15.677,-3.6774 -16.072,-1.9786 -0.39678,1.7044 9.6396,3.5011 15.36,5.2645 z m 0.71205,-3.2859 c 2.4391,0.38193 4.2865,0.48088 4.4758,-0.212 0.19013,-0.69578 -1.4841,-1.5344 -3.7976,-2.4379 -0.2387,0.87603 -0.46925,1.7622 -0.67815,2.6499 z m 0.67815,-2.6499 c 0.29398,-1.0789 0.61116,-2.155 0.94942,-3.2152 -5.8439,-1.385 -15.246,-4.8357 -15.767,-3.1799 -0.5226,1.6613 9.2322,4.2138 14.818,6.3951 z m 0.94942,-3.2152 c 2.4479,0.58019 4.3331,0.82624 4.5775,0.14134 0.24631,-0.69023 -1.3992,-1.6956 -3.6959,-2.7912 -0.31304,0.87262 -0.59881,1.7636 -0.88158,2.6499 z m 0.88158,-2.6499 c 0.37305,-1.0398 0.80562,-2.0545 1.2207,-3.0739 -5.7264,-1.832 -14.785,-5.916 -15.428,-4.3105 -0.64312,1.6066 8.7846,4.7974 14.207,7.3844 z m 1.2207,-3.0739 c 2.431,0.77776 4.3128,1.1655 4.6114,0.49465 0.30389,-0.68278 -1.2484,-1.8125 -3.5264,-3.1092 -0.38645,0.85916 -0.72884,1.7397 -1.085,2.6146 z m 1.085,-2.6146 c 0.44835,-0.99676 0.90209,-1.9932 1.3902,-2.9679 -5.5762,-2.2787 -14.162,-6.9465 -14.919,-5.4058 -0.75713,1.5408 8.2969,5.3954 13.529,8.3737 z m 1.3902,-2.9679 c 2.4286,0.99248 4.3242,1.5768 4.6792,0.91865 0.35975,-0.66692 -1.1349,-1.9367 -3.3568,-3.4272 -0.45201,0.82707 -0.89953,1.6642 -1.3224,2.5086 z m 1.3224,-2.5086 c 0.52611,-0.96272 1.0968,-1.8882 1.6615,-2.8266 -5.3875,-2.6956 -13.574,-7.9783 -14.445,-6.5011 -0.86692,1.4707 7.7781,5.9702 12.783,9.3277 z m 1.6615,-2.8266 c 2.3576,1.1796 4.24,1.9036 4.6453,1.272 0.41352,-0.64434 -1.016,-2.0342 -3.1534,-3.7099 -0.5228,0.80172 -0.99783,1.6168 -1.4919,2.4379 z m 1.4919,-2.4379 c 0.58841,-0.90229 1.2071,-1.7731 1.831,-2.6499 -5.1671,-3.1155 -12.829,-8.9912 -13.8,-7.5964 -0.97189,1.395 7.2238,6.5259 11.969,10.246 z m 1.831,-2.6499 c 2.3284,1.4039 4.15,2.2368 4.6114,1.6253 0.46424,-0.61523 -0.85705,-2.145 -2.8822,-3.9925 -0.59327,0.77231 -1.1643,1.5732 -1.7293,2.3672 z m 1.7293,-2.3672 c 0.65455,-0.85207 1.3125,-1.7192 2.0005,-2.5439 -4.9135,-3.507 -12.053,-9.8258 -13.122,-8.515 -1.0721,1.3141 6.6664,6.9944 11.122,11.059 z m 2.0005,-2.5439 c 2.2515,1.607 4.0311,2.631 4.5436,2.0493 0.51261,-0.58185 -0.74775,-2.1941 -2.6448,-4.2045 -0.64071,0.71422 -1.2841,1.4184 -1.8988,2.1552 z m 1.8988,-2.1552 c 0.72818,-0.81177 1.4771,-1.5856 2.2379,-2.3672 -4.6328,-3.8668 -11.278,-10.696 -12.444,-9.469 -1.1659,1.2265 6.0672,7.4499 10.206,11.836 z m 2.2379,-2.3672 c 2.1233,1.7722 3.8526,2.9442 4.408,2.4026 0.55839,-0.54459 -0.58458,-2.288 -2.3396,-4.4518 -0.70188,0.67076 -1.3915,1.3538 -2.0684,2.0492 z m 2.0684,-2.0492 c 0.77956,-0.74505 1.5641,-1.5122 2.3735,-2.2259 -4.3214,-4.2144 -10.381,-11.446 -11.63,-10.317 -1.2526,1.1324 5.4579,7.8592 9.2567,12.543 z m 2.3735,-2.2259 c 2.0136,1.9637 3.6367,3.2952 4.2384,2.7912 0.60355,-0.50546 -0.42429,-2.3451 -2.0344,-4.6638 -0.74567,0.6114 -1.4814,1.2354 -2.204,1.8726 z m 2.204,-1.8726 c 0.83882,-0.68775 1.7099,-1.3244 2.577,-1.9786 -3.9886,-4.5274 -9.4849,-12.197 -10.816,-11.165 -1.3296,1.0306 4.7994,8.1895 8.2395,13.144 z m 2.577,-1.9786 c 1.8584,2.1095 3.3967,3.5311 4.035,3.0739 0.64018,-0.4586 -0.26628,-2.3368 -1.6954,-4.7698 -0.78983,0.55279 -1.5712,1.1161 -2.3396,1.6959 z m 2.3396,-1.6959 c 0.88881,-0.62204 1.7976,-1.2151 2.7126,-1.8019 -3.6314,-4.817 -8.4969,-12.87 -9.901,-11.942 -1.4042,0.92769 4.1347,8.5455 7.1884,13.744 z m 2.7126,-1.8019 c 1.6999,2.2549 3.1243,3.8011 3.7976,3.3919 0.67804,-0.41204 -0.066,-2.4219 -1.3224,-4.9818 -0.84574,0.49984 -1.65,1.0606 -2.4752,1.5899 z m 2.4752,-1.5899 c 0.93048,-0.54994 1.86,-1.0766 2.8143,-1.59 -3.2494,-5.0826 -7.4532,-13.359 -8.9177,-12.543 -1.4657,0.8166 3.4471,8.7203 6.1034,14.133 z m 2.8143,-1.59 c 1.541,2.4103 2.8855,4.1057 3.5942,3.7452 0.70995,-0.36113 0.0818,-2.427 -0.98332,-5.0878 -0.8736,0.42994 -1.7557,0.88261 -2.6109,1.3426 z m 2.6109,-1.3426 c 0.95769,-0.47132 1.9371,-0.90835 2.916,-1.3426 -2.8513,-5.3146 -6.4146,-13.881 -7.9344,-13.179 -1.521,0.70282 2.7779,8.9251 5.0183,14.521 z m 2.916,-1.3426 c 1.3593,2.5336 2.5862,4.3351 3.3229,4.0279 0.73458,-0.30637 0.28165,-2.4294 -0.57643,-5.1585 -0.92212,0.36895 -1.8418,0.72928 -2.7465,1.1306 z m 2.7465,-1.1306 c 0.99948,-0.39989 1.9988,-0.80503 3.0178,-1.166 -2.438,-5.5147 -5.3495,-14.294 -6.9171,-13.709 -1.5709,0.58664 2.0918,9.1258 3.8994,14.875 z m 3.0178,-1.166 c 1.1649,2.635 2.2247,4.5265 2.9839,4.2752 0.75865,-0.25116 0.44999,-2.3923 -0.20345,-5.1938 -0.93683,0.29382 -1.8588,0.5922 -2.7804,0.91863 z m 2.7804,-0.91863 c 1.0176,-0.31914 2.0509,-0.63882 3.0856,-0.91863 -2.0115,-5.6826 -4.2248,-14.669 -5.8321,-14.203 -1.6081,0.46591 1.3777,9.2536 2.7465,15.122 z m 3.0856,-0.91863 c 0.96174,2.7169 1.9023,4.7161 2.6787,4.5225 0.77794,-0.19393 0.64531,-2.3736 0.20345,-5.2291 -0.96393,0.22308 -1.9319,0.44967 -2.8822,0.70664 z m 2.8822,-0.70664 c 1.0459,-0.24209 2.0922,-0.46999 3.1534,-0.67132 -1.5715,-5.8187 -3.1112,-14.935 -4.747,-14.592 -1.6365,0.3431 0.67219,9.3086 1.5937,15.263 z m 3.1534,-0.67132 c 0.75451,2.7936 1.5146,4.8342 2.3057,4.6992 0.79325,-0.13535 0.83651,-2.3334 0.61034,-5.2292 -0.98783,0.14997 -1.9404,0.34486 -2.916,0.52998 z m 2.916,-0.52998 c 1.0523,-0.15974 2.122,-0.26958 3.1873,-0.38865 -1.1249,-5.9202 -1.9367,-15.129 -3.5942,-14.91 -1.6551,0.21862 -0.0623,9.2914 0.4069,15.299 z m 3.1873,-0.38865 c 0.53989,2.8413 1.1677,4.9155 1.9666,4.8405 0.7955,-0.0747 0.98569,-2.2147 0.98333,-5.0878 -0.98422,0.0741 -1.976,0.13846 -2.95,0.24733 z m 2.95,-0.24733 c 1.0671,-0.0806 2.1429,-0.17305 3.2212,-0.21198 -0.67184,-5.9878 -0.8057,-15.216 -2.4752,-15.122 -1.6728,0.0937 -0.75092,9.3098 -0.74596,15.334 z m 3.2212,-0.21198 c 0.32234,2.8729 0.7576,4.9965 1.5598,4.9818 0.8022,-0.0147 1.1745,-2.1695 1.3902,-5.0525 -0.99057,3.8e-4 -1.9681,0.0351 -2.95,0.0707 z"
             id="path4275" />
        </g>
        <path
           inkscape:connector-curvature="0"
           style="fill:url(#linearGradient5046);stroke-width:0"
           d="m 84.094,944.52 c -22.306,0 -40.388,17.943 -40.388,40.067 0,22.123 18.082,40.047 40.388,40.047 22.306,0 40.388,-17.924 40.388,-40.047 0,-22.123 -18.082,-40.067 -40.388,-40.067 z m 0,2.9728 c 20.651,0 37.397,16.612 37.397,37.094 0,20.481 -16.746,37.075 -37.397,37.075 -20.651,0 -37.397,-16.593 -37.397,-37.075 0,-20.482 16.746,-37.094 37.397,-37.094 z"
           id="path4297" />
        <path
           d="M 25.504,987.37 1.26,990.9 c -16.81,-104.78 142.46,-117.57 140.92,-50 31.247,15.249 62.051,10.964 78.287,-20.203 h 21.718 l -6.0609,-19.698 c 55.161,6.6009 108.75,5.3578 162.63,5.5558 l 9.0914,13.132 c 48.173,-13.472 60.032,-0.79671 75.761,9.0914 l -14.142,2.0203 c -39.099,-30.71 -114.36,40.544 -174.76,51.518 -73.263,13.312 -101.43,4.7764 -149,7.0711 -6.3093,-86.321 -118.3,-81.669 -120.21,-2.0203 z"
           inkscape:connector-curvature="0"
           style="fill:url(#linearGradient4392);stroke-width:0"
           sodipodi:nodetypes="ccccccccccscc"
           id="path4417" />
        <ellipse
           transform="matrix(0.44138,0,0,0.48031,384.4,561.23)"
           style="fill:url(#linearGradient4401);stroke-width:0"
           id="path4419"
           cx="104.55079"
           cy="887.20227"
           rx="73.236061"
           ry="64.144684" />
        <ellipse
           transform="matrix(0.5251,0,0,0.5251,376.42,530.74)"
           style="fill:#2d2d2d;stroke-width:0;filter:url(#filter4259)"
           id="path4421"
           cx="104.55079"
           cy="870.02966"
           rx="19.697975"
           ry="18.687822" />
        <ellipse
           transform="matrix(0.34075,0,0,0.34075,431.76,702.7)"
           style="fill:#9eabb0;stroke-width:0"
           id="path4423"
           cx="-2.0203052"
           cy="834.67432"
           rx="19.192898"
           ry="18.687822" />
        <path
           inkscape:connector-curvature="0"
           style="fill:url(#radialGradient5037);stroke:#817e7e;stroke-width:1.84657;stroke-linecap:round;stroke-linejoin:round"
           d="m 430.58,954.36 c -18.234,0 -33.015,14.667 -33.015,32.752 0,18.085 14.781,32.736 33.015,32.736 18.234,0 33.015,-14.652 33.015,-32.736 0,-18.085 -14.781,-32.752 -33.015,-32.752 z m 0,2.4301 c 16.88,0 30.569,13.579 30.569,30.322 0,16.742 -13.689,30.306 -30.569,30.306 -16.88,0 -30.569,-13.564 -30.569,-30.306 0,-16.742 13.689,-30.322 30.569,-30.322 z"
           id="path4425" />
        <path
           d="m 430.58,952.41 c -19.316,0 -34.975,15.538 -34.975,34.696 0,19.158 15.659,34.68 34.975,34.68 19.316,0 34.975,-15.522 34.975,-34.68 0,-19.158 -15.659,-34.696 -34.975,-34.696 z m 0,2.5744 c 17.883,0 32.384,14.386 32.384,32.122 0,17.736 -14.502,32.106 -32.384,32.106 -17.883,0 -32.384,-14.369 -32.384,-32.106 0,-17.736 14.502,-32.122 32.384,-32.122 z"
           inkscape:connector-curvature="0"
           style="fill:url(#radialGradient5034);stroke:#817e7e;stroke-width:1.95618;stroke-linecap:round;stroke-linejoin:round"
           id="path4427" />
        <path
           inkscape:connector-curvature="0"
           style="fill:#666666;stroke:#817e7e;stroke-width:2.09325;stroke-linecap:round;stroke-linejoin:round"
           d="m 430.58,949.98 c -20.67,0 -37.425,16.627 -37.425,37.127 0,20.501 16.756,37.11 37.425,37.11 20.67,0 37.425,-16.609 37.425,-37.11 0,-20.5 -16.756,-37.127 -37.425,-37.127 z m 0,2.7547 c 19.136,0 34.653,15.393 34.653,34.372 0,18.979 -15.518,34.355 -34.653,34.355 -19.136,0 -34.653,-15.376 -34.653,-34.355 0,-18.979 15.518,-34.372 34.653,-34.372 z"
           id="path4429" />
        <g
           transform="matrix(-0.49937,0,0,0.49632,320.89,627.71)"
           style="stroke:#333333;stroke-width:3.9243"
           id="g4431">
          <path
             d="m -214.15,715.98 -14.142,-51.518"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path4433" />
          <path
             d="m -211.12,780.63 -17.173,-48.487"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path4435" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -212.77,718.6 -0.32647,-53.423"
             id="path4437" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -226.57,781.83 -4.038,-51.28"
             id="path4439" />
          <path
             d="m -211.8,721.08 13.511,-51.687"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path4441" />
          <path
             d="m -241.5,778.58 9.3718,-50.578"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path4443" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -211.33,723.27 26.429,-46.429"
             id="path4445" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -254.9,771.13 22.143,-46.429"
             id="path4447" />
          <path
             d="m -211.43,725.52 37.545,-38.006"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path4449" />
          <path
             d="m -265.91,760.47 33.405,-39.116"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path4451" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -212.12,727.66 46.102,-26.994"
             id="path4453" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -273.78,747.32 42.391,-29.137"
             id="path4455" />
          <path
             d="m -213.33,729.54 51.52,-14.14"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path4457" />
          <path
             d="m -277.98,732.57 48.487,-17.173"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path4459" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -214.99,731.05 53.423,-0.32649"
             id="path4461" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -278.22,717.25 51.28,-4.038"
             id="path4463" />
          <path
             d="m -217.47,732.02 51.687,13.511"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path4465" />
          <path
             d="m -274.97,702.32 50.578,9.3718"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path4467" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -219.66,732.5 46.429,26.429"
             id="path4469" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -267.52,688.93 46.429,22.143"
             id="path4471" />
          <path
             d="m -221.91,732.39 38.006,37.545"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path4473" />
          <path
             d="m -256.86,677.92 39.116,33.405"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path4475" />
        </g>
        <g
           transform="matrix(0.47794,0,0,0.48362,535.03,636.13)"
           style="stroke:#4d3636;stroke-width:3.92482"
           id="g4477">
          <path
             d="m -214.15,715.98 -14.142,-51.518"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path4479" />
          <path
             d="m -211.12,780.63 -17.173,-48.487"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path4481" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -212.77,718.6 -0.32647,-53.423"
             id="path4483" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -226.57,781.83 -4.038,-51.28"
             id="path4485" />
          <path
             d="m -211.8,721.08 13.511,-51.687"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path4487" />
          <path
             d="m -241.5,778.58 9.3718,-50.578"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path4489" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -211.33,723.27 26.429,-46.429"
             id="path4491" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -254.9,771.13 22.143,-46.429"
             id="path4493" />
          <path
             d="m -211.43,725.52 37.545,-38.006"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path4495" />
          <path
             d="m -265.91,760.47 33.405,-39.116"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path4497" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -212.12,727.66 46.102,-26.994"
             id="path4499" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -273.78,747.32 42.391,-29.137"
             id="path4501" />
          <path
             d="m -213.33,729.54 51.52,-14.14"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path4503" />
          <path
             d="m -277.98,732.57 48.487,-17.173"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path4505" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -214.99,731.05 53.423,-0.32649"
             id="path4507" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -278.22,717.25 51.28,-4.038"
             id="path4509" />
          <path
             d="m -217.47,732.02 51.687,13.511"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path4511" />
          <path
             d="m -274.97,702.32 50.578,9.3718"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path4513" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -219.66,732.5 46.429,26.429"
             id="path4515" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -267.52,688.93 46.429,22.143"
             id="path4517" />
          <path
             d="m -221.91,732.39 38.006,37.545"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path4519" />
          <path
             d="m -256.86,677.92 39.116,33.405"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path4521" />
        </g>
        <ellipse
           transform="matrix(0.2962,0,0,0.2962,431.67,739.89)"
           style="fill:url(#linearGradient4239);stroke:#333333;stroke-width:3.9243;stroke-linecap:round;stroke-linejoin:round"
           id="path4523"
           cx="-2.0203052"
           cy="834.67432"
           rx="19.192898"
           ry="18.687822" />
        <g
           transform="matrix(0.5599,0,0,0.55238,568.54,475.62)"
           style="stroke:#9f9999;stroke-width:0.470445"
           id="g4525">
          <path
             d="m -245.98,826.55 c -55.038,0 -99.649,44.13 -99.649,98.548 0,54.418 44.611,98.519 99.649,98.519 55.038,0 99.677,-44.102 99.677,-98.519 0,-54.418 -44.639,-98.548 -99.677,-98.548 z m 0,26.945 c 39.99,0 72.414,32.064 72.414,71.603 0,39.539 -32.424,71.575 -72.414,71.575 -39.99,0 -72.387,-32.035 -72.387,-71.575 0,-39.539 32.397,-71.603 72.387,-71.603 z"
             inkscape:connector-curvature="0"
             style="fill:url(#radialGradient4293);stroke:#9f9999;stroke-width:0.470445;stroke-linecap:round;stroke-linejoin:round"
             id="path4527" />
          <path
             d="m -244.61,826.11 c -1.6718,-0.0324 -1.4493,9.1844 -1.8988,15.193 h 0.0339 c 1.0817,0 2.1499,0.029 3.2212,0.0707 -0.21479,-6.0212 0.31566,-15.231 -1.3563,-15.263 z m 1.3563,15.263 c 0.10313,2.8885 0.35196,5.0421 1.1529,5.0878 0.80081,0.0456 1.329,-2.0538 1.7632,-4.9112 -0.96597,-0.072 -1.9409,-0.13863 -2.9161,-0.17665 z m 2.9161,0.17665 c 1.08,0.0806 2.1536,0.19581 3.2212,0.318 0.24291,-6.0201 1.5296,-15.176 -0.13562,-15.334 -1.6648,-0.15802 -2.1804,9.0588 -3.0856,15.016 z m 3.2212,0.318 c -0.11639,2.8848 -0.0146,5.0527 0.77989,5.1585 0.79573,0.10599 1.4855,-1.985 2.1362,-4.8052 -0.97056,-0.14693 -1.9344,-0.24097 -2.9161,-0.35331 z m 2.9161,0.35331 c 1.0702,0.16202 2.1315,0.3621 3.1873,0.56531 0.70049,-5.9846 2.6651,-15.016 1.0172,-15.299 -1.6474,-0.2827 -2.8498,8.861 -4.2045,14.733 z m 3.1873,0.56531 c -0.33604,2.871 -0.37794,5.0638 0.40689,5.2292 0.78191,0.16473 1.6161,-1.849 2.4752,-4.5932 -0.95558,-0.22034 -1.9138,-0.44961 -2.8821,-0.63599 z m 2.8821,0.63599 c 1.0556,0.2434 2.1142,0.49329 3.1534,0.77731 1.1507,-5.9147 3.7941,-14.786 2.1701,-15.193 -1.6246,-0.40672 -3.5227,8.6637 -5.3235,14.416 z m 3.1534,0.77731 c -0.54956,2.8248 -0.76767,5.0059 0,5.2291 0.7684,0.22344 1.7471,-1.7381 2.8143,-4.4165 -0.92615,-0.2895 -1.874,-0.55565 -2.8143,-0.81262 z m 2.8143,0.81262 c 1.0302,0.32204 2.0399,0.66323 3.0517,1.0246 1.597,-5.8112 4.9114,-14.453 3.3229,-14.981 -1.5878,-0.52739 -4.143,8.3555 -6.3746,13.956 z m 3.0517,1.0246 c -0.76267,2.7752 -1.1215,4.9135 -0.37299,5.1938 0.74738,0.27995 1.8894,-1.5534 3.1534,-4.1338 -0.91975,-0.36741 -1.8442,-0.72553 -2.7804,-1.06 z m 2.7804,1.06 c 1.007,0.40228 1.9976,0.83106 2.9838,1.272 2.0314,-5.6747 5.988,-14.087 4.4419,-14.733 -1.545,-0.64609 -4.773,8.0462 -7.4257,13.462 z m 2.9838,1.272 c -0.96564,2.6975 -1.5365,4.753 -0.81377,5.0878 0.72165,0.33431 2.0078,-1.3857 3.4586,-3.8512 -0.87151,-0.42808 -1.7557,-0.83918 -2.6448,-1.2366 z m 2.6448,1.2366 c 0.96152,0.4723 1.9429,0.93991 2.8822,1.4486 2.4572,-5.5056 7.0533,-13.584 5.5608,-14.345 -1.4936,-0.76163 -5.3841,7.6979 -8.443,12.896 z m 2.8822,1.4486 c -1.1683,2.6178 -1.8482,4.6642 -1.1529,5.0525 0.69397,0.38753 2.0625,-1.2557 3.6959,-3.6039 -0.8429,-0.49709 -1.6806,-0.98145 -2.543,-1.4486 z m 2.543,1.4486 c 0.93425,0.55097 1.8372,1.1445 2.7465,1.7313 2.8619,-5.3066 8.1152,-13.082 6.6798,-13.956 -1.4317,-0.87197 -5.9809,7.2718 -9.4263,12.225 z m 2.7465,1.7313 c -1.3457,2.4952 -2.1847,4.4396 -1.5258,4.8758 0.65669,0.43479 2.1472,-1.0638 3.9333,-3.2505 -0.79343,-0.55349 -1.5932,-1.0998 -2.4074,-1.6253 z m 2.4074,1.6253 c 0.89266,0.62269 1.7794,1.2508 2.6448,1.9079 3.2542,-5.0768 9.0979,-12.445 7.7309,-13.426 -1.367,-0.98127 -6.559,6.8453 -10.376,11.518 z m 2.6448,1.9079 c -1.5229,2.3758 -2.4867,4.2515 -1.8649,4.7345 0.61993,0.48153 2.2312,-0.89633 4.1706,-2.9326 -0.75602,-0.61873 -1.5268,-1.2104 -2.3057,-1.8019 z m 2.3057,1.8019 c 0.8283,0.67785 1.6415,1.375 2.4414,2.0846 3.6257,-4.819 10.074,-11.741 8.782,-12.826 -1.2902,-1.0828 -7.0611,6.371 -11.223,10.741 z m 2.4414,2.0846 c -1.6829,2.2367 -2.7501,4.0324 -2.1701,4.5578 0.57819,0.52375 2.2344,-0.71318 4.3062,-2.5792 -0.69861,-0.66606 -1.4136,-1.3377 -2.1362,-1.9786 z m 2.1362,1.9786 c 0.78547,0.74887 1.5515,1.5169 2.3057,2.2966 3.9737,-4.5351 10.976,-11.007 9.7654,-12.19 -1.2075,-1.1799 -7.5864,5.8539 -12.071,9.893 z m 2.3057,2.2966 c -1.8212,2.0786 -3.009,3.7478 -2.4752,4.3105 0.53371,0.56256 2.2515,-0.50216 4.4419,-2.1906 -0.64738,-0.7195 -1.2936,-1.4242 -1.9666,-2.1199 z m 1.9666,2.1199 c 0.72923,0.81043 1.4744,1.5983 2.1701,2.4379 4.307,-4.2231 11.764,-10.177 10.647,-11.448 -1.1172,-1.2707 -8.0368,5.3249 -12.817,9.0097 z m 2.1701,2.4379 c -1.974,1.9356 -3.3038,3.4973 -2.8143,4.0985 0.48671,0.59779 2.2832,-0.33317 4.5775,-1.8373 -0.58076,-0.75413 -1.1561,-1.5286 -1.7632,-2.2613 z m 1.7632,2.2613 c 0.66776,0.86706 1.3007,1.7554 1.9327,2.6499 4.6043,-3.8919 12.585,-9.3118 11.562,-10.67 -1.02,-1.3545 -8.4477,4.7114 -13.495,8.0204 z m 1.9327,2.6499 c -2.0735,1.7527 -3.4548,3.1872 -3.0178,3.8159 0.43555,0.62652 2.2772,-0.14489 4.6453,-1.4486 -0.52538,-0.80374 -1.074,-1.5838 -1.6276,-2.3672 z m 1.6276,2.3672 c 0.59415,0.9089 1.1722,1.822 1.7293,2.7559 4.8809,-3.5371 13.329,-8.3514 12.41,-9.787 -0.91817,-1.4338 -8.8546,4.1216 -14.139,7.0311 z m 1.7293,2.7559 c -2.1782,1.5785 -3.6407,2.8776 -3.2551,3.5332 0.38091,0.64765 2.2726,-0.004 4.6792,-1.0953 -0.45464,-0.82804 -0.94092,-1.6279 -1.4241,-2.4379 z m 1.4241,2.4379 c 0.52864,0.96283 1.0368,1.9463 1.5258,2.9326 5.1266,-3.1644 13.967,-7.3615 13.156,-8.8683 -0.81198,-1.5088 -9.1931,3.4488 -14.682,5.9358 z m 1.5258,2.9326 c -2.2573,1.3933 -3.824,2.5741 -3.4925,3.2506 0.32625,0.66563 2.2448,0.17614 4.6792,-0.70664 -0.3857,-0.85292 -0.77189,-1.7072 -1.1868,-2.5439 z m 1.1868,2.5439 c 0.45698,1.0106 0.87345,2.0415 1.2885,3.0739 5.3246,-2.7834 14.604,-6.3333 13.902,-7.9144 -0.69893,-1.5745 -9.5324,2.7887 -15.191,4.8405 z m 1.2885,3.0739 c -2.2592,1.181 -3.9002,2.251 -3.6281,2.9325 0.27248,0.68268 2.1814,0.33166 4.6453,-0.35331 -0.31478,-0.87086 -0.67289,-1.7227 -1.0172,-2.5792 z m 1.0172,2.5792 c 0.37906,1.0487 0.74983,2.1114 1.085,3.1799 5.5142,-2.3782 15.093,-5.2564 14.512,-6.8898 -0.57927,-1.6283 -9.8033,2.0991 -15.597,3.7099 z m 1.085,3.1799 c -2.3205,1.0008 -4.0158,1.9185 -3.7976,2.6146 0.21623,0.68984 2.0966,0.47846 4.5436,0 -0.24103,-0.87328 -0.47582,-1.7535 -0.74595,-2.6146 z m 0.74595,2.6146 c 0.29585,1.0719 0.5627,2.1612 0.81378,3.2506 5.6584,-1.9682 15.581,-4.1096 15.123,-5.7945 -0.45697,-1.6788 -10.047,1.3924 -15.937,2.5439 z m 0.81378,3.2506 c -2.3221,0.80773 -3.9933,1.5986 -3.8316,2.2966 0.16075,0.69353 1.9891,0.66848 4.408,0.38865 -0.17493,-0.90294 -0.37089,-1.7934 -0.57644,-2.6852 z m 0.57644,2.6852 c 0.21475,1.1085 0.44269,2.1969 0.61033,3.3212 5.7816,-1.5431 15.928,-2.9788 15.597,-4.6992 -0.33009,-1.719 -10.26,0.68988 -16.208,1.3779 z m 0.61033,3.3212 c -2.3447,0.62579 -4.0406,1.241 -3.9333,1.9433 0.1061,0.69399 1.8912,0.83119 4.2724,0.74197 -0.10228,-0.89597 -0.20689,-1.7986 -0.33908,-2.6852 z m 0.33908,2.6852 c 0.12708,1.1128 0.22506,2.2303 0.30516,3.3565 5.8559,-1.1212 16.239,-1.7428 16.038,-3.4979 -0.19956,-1.7486 -10.374,-0.0823 -16.343,0.14133 z m 0.30516,3.3565 c -2.3058,0.44149 -3.9519,0.96708 -3.8994,1.6606 0.0521,0.68644 1.7269,0.92912 4.035,1.0246 -0.0335,-0.89992 -0.0722,-1.7932 -0.13562,-2.6852 z m 0.13562,2.6852 c 0.0365,0.98504 0.0678,1.974 0.0678,2.9679 0,0.14188 7.7e-4,0.28227 0,0.42397 5.9003,-0.69454 16.444,-0.51878 16.377,-2.2966 -0.0666,-1.7722 -10.499,-0.84927 -16.445,-1.0953 z m 0.0678,3.3919 c -2.2644,0.26655 -3.899,0.65985 -3.8994,1.3426 -3.3e-4,0.67641 1.6013,1.074 3.8315,1.3426 0.0346,-0.89088 0.063,-1.7871 0.0678,-2.6852 z m -0.0678,2.6852 c -0.0443,1.139 -0.11112,2.2656 -0.20344,3.3919 5.9106,-0.26845 16.58,0.69526 16.649,-1.0953 0.0688,-1.7841 -10.559,-1.5875 -16.445,-2.2966 z m -0.20344,3.3919 c -2.2098,0.10047 -3.8142,0.35665 -3.8655,1.0246 -0.0509,0.6623 1.4556,1.1595 3.5942,1.59 0.1004,-0.86818 0.19941,-1.7381 0.27126,-2.6146 z m -0.27126,2.6146 c -0.13102,1.1332 -0.26209,2.2737 -0.44079,3.3919 5.8937,0.15957 16.612,1.9668 16.818,0.17666 0.20562,-1.7859 -10.59,-2.4035 -16.377,-3.5685 z m -0.44079,3.3919 c -2.1584,-0.0585 -3.6973,0.0897 -3.7976,0.74197 -0.0992,0.6442 1.2887,1.2574 3.3229,1.8373 0.16616,-0.8552 0.3365,-1.7145 0.4747,-2.5792 z m -0.4747,2.5792 c -0.21901,1.1272 -0.47933,2.2468 -0.74598,3.3565 5.8254,0.5657 16.575,3.2718 16.92,1.484 0.34315,-1.7776 -10.524,-3.23 -16.174,-4.8405 z m -0.74598,3.3565 c -2.0524,-0.19928 -3.5166,-0.16549 -3.662,0.45932 -0.14419,0.61937 1.1149,1.3021 3.0178,2.0139 0.22518,-0.81852 0.44512,-1.6444 0.64424,-2.4733 z m -0.64424,2.4733 c -0.3099,1.1266 -0.62513,2.2504 -0.98333,3.3566 5.738,0.97369 16.403,4.5248 16.886,2.7559 0.48151,-1.7621 -10.437,-4.0678 -15.903,-6.1124 z m -0.98333,3.3566 c -1.9679,-0.33395 -3.3715,-0.38801 -3.5603,0.212 -0.18781,0.597 0.95379,1.3538 2.7465,2.1906 0.28785,-0.79903 0.55129,-1.592 0.81376,-2.4026 z m -0.81376,2.4026 c -0.39653,1.1007 -0.84488,2.1728 -1.2885,3.2505 5.6095,1.359 16.196,5.8068 16.818,4.0632 0.6175,-1.7304 -10.267,-4.8575 -15.53,-7.3137 z m -1.2885,3.2505 c -1.8542,-0.44918 -3.1633,-0.56817 -3.3907,0 -0.22554,0.56342 0.775,1.3646 2.4074,2.2966 0.34094,-0.75878 0.66594,-1.5255 0.98331,-2.2966 z m -0.98331,2.2966 c -0.48023,1.0688 -0.96605,2.1366 -1.4919,3.1799 5.4619,1.7408 15.823,7.0738 16.581,5.3705 0.75507,-1.6965 -10.094,-5.6991 -15.089,-8.5504 z m -1.4919,3.1799 c -1.7559,-0.55966 -3.0246,-0.75004 -3.289,-0.21199 -0.26115,0.53141 0.64728,1.3846 2.1362,2.4026 0.39316,-0.72281 0.78189,-1.4546 1.1528,-2.1906 z m -1.1528,2.1906 c -0.56636,1.0412 -1.1519,2.0611 -1.7632,3.0739 5.2747,2.0899 15.348,8.3349 16.242,6.6778 0.88897,-1.648 -9.7709,-6.533 -14.478,-9.7517 z m -1.7632,3.0739 c -1.6298,-0.64574 -2.7901,-0.88991 -3.0856,-0.38867 -0.28973,0.49152 0.48355,1.3674 1.7971,2.4379 0.43591,-0.67499 0.87284,-1.3606 1.2885,-2.0493 z m -1.2885,2.0493 c -0.64982,1.0062 -1.3407,1.9932 -2.0344,2.9679 5.0502,2.3982 14.839,9.5542 15.869,7.9497 1.0234,-1.5947 -9.4726,-7.3629 -13.834,-10.918 z m -2.0344,2.9679 c -1.4809,-0.70324 -2.5625,-1.0241 -2.8821,-0.56532 -0.31749,0.45569 0.32534,1.3866 1.4919,2.5086 0.48225,-0.63628 0.92702,-1.2925 1.3902,-1.9433 z m -1.3902,1.9433 c -0.73365,0.96799 -1.4613,1.9288 -2.2379,2.8619 4.8324,2.7318 14.171,10.681 15.326,9.151 1.151,-1.5254 -9.0752,-8.1532 -13.088,-12.013 z m -2.2379,2.8619 c -1.3847,-0.78279 -2.3992,-1.1677 -2.7465,-0.74196 -0.34026,0.41706 0.20736,1.3212 1.2207,2.4732 0.50675,-0.57462 1.0357,-1.1423 1.5258,-1.7313 z m -1.5258,1.7313 c -0.8058,0.91369 -1.6286,1.809 -2.4753,2.6852 4.5693,2.9959 13.4,11.842 14.682,10.388 1.2751,-1.4474 -8.5759,-8.9449 -12.207,-13.073 z m -2.4753,2.6852 c -1.2454,-0.81658 -2.2128,-1.1958 -2.577,-0.81266 -0.36197,0.38082 0.10515,1.2569 0.98333,2.4379 0.54491,-0.53281 1.0642,-1.0773 1.5936,-1.6252 z m -1.5936,1.6252 c -0.89166,0.87188 -1.8146,1.7137 -2.7465,2.5439 4.3018,3.261 12.605,12.883 14.004,11.518 1.3908,-1.3564 -8.0106,-9.6962 -11.257,-14.062 z m -2.7465,2.5439 c -1.1313,-0.85755 -1.9922,-1.2987 -2.3735,-0.95396 -0.37389,0.33803 -0.007,1.2005 0.71206,2.3672 0.5557,-0.46874 1.119,-0.93001 1.6615,-1.4133 z m -1.6615,1.4133 c -0.96131,0.8109 -1.9503,1.6011 -2.95,2.3673 4.0103,3.4734 11.642,13.917 13.156,12.649 1.5065,-1.2618 -7.4018,-10.468 -10.206,-15.016 z m -2.95,2.3673 c -1.0041,-0.86972 -1.8131,-1.3276 -2.204,-1.0246 -0.39217,0.30398 -0.13183,1.1799 0.4747,2.3672 0.58957,-0.42707 1.1521,-0.90024 1.7293,-1.3426 z m -1.7293,1.3426 c -1.018,0.73742 -2.0659,1.4294 -3.1195,2.1199 3.7236,3.7177 10.628,14.82901 12.241,13.67401 1.6047,-1.14961 -6.7086,-11.07101 -9.1211,-15.79301 z m -3.1195,2.1199 c -0.91212,-0.91066 -1.6288,-1.3986 -2.0344,-1.1306 -0.40201,0.26557 -0.24215,1.1236 0.23735,2.2966 0.61057,-0.37726 1.1976,-0.77308 1.7971,-1.166 z m -1.7971,1.166 c -1.0815,0.66828 -2.1739,1.2898 -3.289,1.908 3.4197,3.9218 9.4836,15.70001 11.189,14.66301 1.6999,-1.0332 -5.9212,-11.72901 -7.9005,-16.57101 z m -3.289,1.908 c -0.81353,-0.93297 -1.4839,-1.3971 -1.8988,-1.166 -0.40945,0.22813 -0.32782,1.0025 0.0339,2.1552 0.62448,-0.32506 1.2503,-0.64861 1.8649,-0.98928 z m -1.8649,0.98928 c -1.1324,0.5895 -2.295,1.159 -3.4586,1.6959 3.1054,4.098 8.3482,16.45703 10.138,15.54603 1.7854,-0.9081 -5.1342,-12.31703 -6.6798,-17.24203 z m -3.4586,1.6959 c -0.71663,-0.94569 -1.3424,-1.431 -1.7632,-1.2366 -0.4161,0.19227 -0.39079,0.95079 -0.13564,2.0846 0.63849,-0.27483 1.2691,-0.55735 1.8988,-0.84797 z m -1.8988,0.84797 c -1.1935,0.51379 -2.4053,0.99118 -3.6281,1.4486 2.7831,4.2288 7.118,17.10196 8.9855,16.32296 1.859,-0.7752 -4.2363,-12.79096 -5.3574,-17.77196 z m -3.6281,1.4486 c -0.61783,-0.93876 -1.1389,-1.4644 -1.5597,-1.3073 -0.41946,0.15665 -0.49275,0.90868 -0.33907,2.0139 0.639,-0.22088 1.2673,-0.47041 1.8988,-0.70666 z m -1.8988,0.70666 c -1.2181,0.42105 -2.4519,0.80193 -3.6959,1.166 2.4563,4.3723 5.8017,17.633 7.7309,16.995 1.9228,-0.6366 -3.3399,-13.162 -4.035,-18.161 z m -3.6959,1.166 c -0.53312,-0.94899 -1.0343,-1.5007 -1.458,-1.3779 -0.42072,0.12189 -0.56788,0.87078 -0.5086,1.9432 0.65902,-0.17484 1.3144,-0.37444 1.9666,-0.56531 z m -1.9666,0.56531 c -1.2613,0.33466 -2.5466,0.60852 -3.8316,0.88327 2.127,4.46142 4.4921,18.09002 6.4763,17.59502 1.9752,-0.4923 -2.3693,-13.495 -2.6448,-18.47902 z m -3.8316,0.88327 c -0.4459,-0.93525 -0.86884,-1.5012 -1.2885,-1.4132 -0.41764,0.0875 -0.61427,0.77556 -0.64424,1.802 0.64652,-0.12198 1.2918,-0.2516 1.9327,-0.38877 z m -1.9327,0.38877 c -1.2908,0.24328 -2.5875,0.45426 -3.8994,0.63588 1.788,4.57657 3.0992,18.43497 5.12,18.08997 2.0182,-0.3443 -1.3644,-13.802 -1.2207,-18.72597 z m -3.8994,0.63588 c -0.36984,-0.94666 -0.76634,-1.5041 -1.1868,-1.4486 -0.41771,0.0554 -0.63631,0.69735 -0.74598,1.696 0.65124,-0.0742 1.2863,-0.15797 1.9327,-0.24745 z m -1.9327,0.24745 c -1.3121,0.14946 -2.6367,0.26672 -3.9672,0.35312 1.4435,4.6673 1.7179,18.636 3.7638,18.444 2.044,-0.1922 -0.33086,-13.932 0.20344,-18.79697 z m -3.9672,0.35312 c -0.29468,-0.95279 -0.63225,-1.54277 -1.0511,-1.51917 -0.42054,0.0236 -0.69635,0.67387 -0.8816,1.66047 0.64621,-0.027 1.2906,-0.1 1.9327,-0.1413 z m -1.9327,0.1413 c -1.1382,0.048 -2.2747,0.07 -3.4246,0.07 h -0.54254 c 1.0797,4.776 0.28709,18.658 2.3396,18.62 2.0529,-0.039 0.72451,-13.881 1.6275,-18.691 z m -3.9672,0.07 c -0.22172,-0.98073 -0.52789,-1.61707 -0.9494,-1.62517 -0.41901,-0.01 -0.72426,0.59349 -0.98333,1.55457 0.64452,0.02 1.2845,0.066 1.9327,0.07 z m -1.9327,-0.07 c -1.3401,-0.039 -2.6782,-0.1077 -4.001,-0.212 0.7154,4.8395 -1.1347,18.644 0.91548,18.761 2.052,0.1174 1.815,-13.835 3.0856,-18.549 z m -4.001,-0.212 c -0.14666,-0.99183 -0.39476,-1.62077 -0.8138,-1.66047 -0.41813,-0.0401 -0.78695,0.5411 -1.1189,1.484 0.63854,0.0653 1.2898,0.12617 1.9327,0.17657 z m -1.9327,-0.17651 c -1.3174,-0.13501 -2.6355,-0.2623 -3.9333,-0.45936 0.30978,4.94517 -2.5333,18.45697 -0.50862,18.72597 2.0363,0.271 2.8181,-13.655 4.4419,-18.26697 z m -3.9333,-0.45936 c -0.0654,-1.0452 -0.25269,-1.7289 -0.67816,-1.8019 -0.42202,-0.0729 -0.84416,0.49994 -1.2546,1.4486 0.64952,0.11498 1.2779,0.25384 1.9327,0.35332 z m -1.9327,-0.35332 c -1.3035,-0.23054 -2.5846,-0.48548 -3.8655,-0.77732 -0.0719,4.96181 -3.9299,18.19901 -1.9327,18.62001 2.0018,0.4216 3.8373,-13.31 5.7982,-17.84301 z m -3.8655,-0.77732 c 0.0151,-1.0526 -0.12255,-1.7674 -0.54251,-1.8726 -0.42702,-0.10697 -0.92774,0.44942 -1.4241,1.4133 0.64949,0.16508 1.3109,0.30991 1.9666,0.45927 z m -1.9666,-0.45927 c -1.2619,-0.3208 -2.5266,-0.64574 -3.7637,-1.0246 -0.50939,5.02108 -5.268,17.73598 -3.3229,18.30198 1.9525,0.568 4.7935,-12.824 7.0867,-17.27698 z m -3.7637,-1.0246 c 0.11263,-1.1101 0.052,-1.8374 -0.37298,-1.9786 -0.42212,-0.14022 -0.95028,0.3647 -1.5258,1.3073 0.6366,0.21255 1.2552,0.47418 1.8988,0.6713 z m -1.8988,-0.6713 c -1.2471,-0.4164 -2.4771,-0.86833 -3.6959,-1.3426 -0.89058,4.9692 -6.6094,17.30898 -4.7131,18.01898 1.8993,0.7115 5.7842,-12.378 8.4091,-16.67698 z m -3.6959,-1.3426 c 0.19703,-1.0994 0.20981,-1.8411 -0.20345,-2.0139 -0.42771,-0.17888 -1.0142,0.33595 -1.6954,1.3073 0.62125,0.25989 1.2697,0.46183 1.8988,0.70664 z m -1.8988,-0.70664 c -1.1842,-0.4954 -2.3381,-1.041 -3.4925,-1.59 -1.3382,4.9564 -7.8921,16.64502 -6.0694,17.48902 1.8226,0.8444 6.6104,-11.69002 9.5619,-15.89902 z m -3.4925,-1.59 c 0.3088,-1.1438 0.34508,-1.9447 -0.0678,-2.1553 -0.41571,-0.21196 -1.0372,0.30721 -1.7971,1.2366 0.61453,0.31187 1.2413,0.62209 1.8649,0.91864 z m -1.8649,-0.91864 c -1.1475,-0.58232 -2.2761,-1.2029 -3.3908,-1.8373 -1.7554,4.877 -9.103,15.91396 -7.3579,16.88896 1.7505,0.9775 7.4832,-11.05696 10.749,-15.05096 z m -3.3908,-1.8373 c 0.41514,-1.1533 0.50537,-1.9801 0.10166,-2.2259 -0.41059,-0.25007 -1.1026,0.24309 -1.9666,1.166 0.61369,0.37128 1.2405,0.70461 1.8649,1.06 z m -1.8649,-1.06 c -1.0818,-0.65448 -2.14,-1.3112 -3.1873,-2.0139 -2.2115,4.7917 -10.195,15.08896 -8.5447,16.18196 1.6592,1.0986 8.1549,-10.34696 11.732,-14.16796 z m -3.1873,-2.0139 c 0.54872,-1.1889 0.73799,-2.0456 0.33909,-2.3319 -0.39906,-0.28643 -1.1407,0.16648 -2.1023,1.06 0.58985,0.4193 1.1615,0.8682 1.7632,1.272 z m -1.7632,-1.272 c -1.0236,-0.72756 -2.031,-1.4881 -3.0178,-2.2612 -2.5943,4.6255 -11.29,14.229 -9.7315,15.44 1.5626,1.2136 8.8754,-9.5793 12.749,-13.179 z m -3.0178,-2.2612 c 0.65198,-1.1625 0.92238,-2.0484 0.54252,-2.3672 -0.38539,-0.32342 -1.2076,0.12922 -2.2718,0.98931 0.5662,0.46912 1.15,0.92407 1.7293,1.3779 z m -1.7293,-1.3779 c -0.95598,-0.79212 -1.8969,-1.6035 -2.8143,-2.4379 -3.0257,4.4568 -12.232,13.209 -10.783,14.521 1.4569,1.3197 9.4381,-8.7226 13.597,-12.084 z m -2.8143,-2.4379 c 0.79465,-1.1705 1.181,-2.0791 0.81377,-2.4379 -0.37256,-0.36408 -1.2875,0.0824 -2.4752,0.91863 0.54082,0.52022 1.1061,1.0141 1.6615,1.5193 z m -1.6615,-1.5193 c -0.88518,-0.8515 -1.7321,-1.7239 -2.577,-2.6146 -3.4357,4.2447 -13.134,12.162 -11.8,13.568 1.34,1.4125 9.9367,-7.8275 14.377,-10.953 z m -2.577,-2.6146 c 0.93939,-1.1606 1.4348,-2.0754 1.085,-2.4732 -0.35415,-0.4028 -1.3392,-0.0147 -2.6448,0.77731 0.51273,0.57255 1.0308,1.1383 1.5597,1.6959 z m -1.5597,-1.6959 c -0.81768,-0.91307 -1.6323,-1.8415 -2.4074,-2.7912 -3.8346,3.9962 -13.859,11.055 -12.647,12.543 1.2189,1.4971 10.355,-6.9001 15.055,-9.7516 z m -2.4074,-2.7912 c 1.0956,-1.1418 1.7199,-2.0354 1.3902,-2.4732 -0.33147,-0.44017 -1.3933,-0.0975 -2.8143,0.63598 0.47352,0.61597 0.93306,1.2356 1.4241,1.8373 z m -1.4241,-1.8373 c -0.73424,-0.9552 -1.4792,-1.909 -2.1701,-2.8972 -4.2155,3.7105 -14.545,9.8893 -13.461,11.448 1.0931,1.5724 10.692,-6.0005 15.631,-8.5503 z m -2.1701,-2.8972 c 1.2616,-1.1105 2.0016,-1.995 1.6954,-2.4732 -0.30668,-0.47888 -1.4699,-0.21096 -3.0178,0.45933 0.43934,0.67076 0.86339,1.3572 1.3224,2.0139 z m -1.3224,-2.0139 c -0.65079,-0.99362 -1.2584,-2.0151 -1.8649,-3.0386 -4.5214,3.3853 -15.165,8.689 -14.207,10.317 0.96144,1.6347 10.909,-5.0426 16.072,-7.2784 z m -1.8649,-3.0386 c 1.3797,-1.033 2.2058,-1.8951 1.9327,-2.4026 -0.27832,-0.5172 -1.4746,-0.27716 -3.1534,0.31799 0.39064,0.70779 0.8091,1.39 1.2207,2.0846 z m -1.2207,-2.0846 c -0.57127,-1.035 -1.1361,-2.0823 -1.6615,-3.1445 -4.8653,3.0333 -15.603,7.4431 -14.784,9.1157 0.82611,1.6854 11.079,-4.0689 16.445,-5.9711 z m -1.6615,-3.1445 c 1.5713,-0.97961 2.5158,-1.8175 2.2718,-2.3672 -0.24489,-0.55171 -1.5267,-0.3938 -3.3229,0.10598 0.34482,0.75649 0.68327,1.5175 1.0511,2.2612 z m -1.0511,-2.2612 c -0.48354,-1.0608 -0.95303,-2.1303 -1.3902,-3.2152 -5.1337,2.6522 -16.011,6.1984 -15.326,7.9144 0.68955,1.7275 11.181,-3.1588 16.716,-4.6991 z m -1.3902,-3.2152 c 1.7112,-0.88408 2.7834,-1.6811 2.577,-2.2612 -0.2085,-0.58608 -1.5727,-0.50009 -3.4925,-0.10599 0.29147,0.79692 0.59924,1.5824 0.91551,2.3673 z m -0.91551,-2.3672 c -0.39903,-1.091 -0.76744,-2.1733 -1.1189,-3.2859 -5.3762,2.246 -16.28,4.9668 -15.733,6.7131 0.55068,1.7567 11.17,-2.2608 16.852,-3.4272 z m -1.1189,-3.2859 c 1.8591,-0.77667 3.0483,-1.5093 2.8821,-2.1199 -0.16681,-0.6128 -1.5834,-0.61914 -3.5942,-0.35333 0.23138,0.82592 0.45462,1.6584 0.71206,2.4732 z m -0.71206,-2.4732 c -0.30771,-1.0984 -0.62096,-2.2043 -0.8816,-3.3212 -5.5698,1.8225 -16.448,3.6713 -16.038,5.4411 0.41259,1.7802 11.134,-1.355 16.92,-2.1199 z m -0.8816,-3.3212 c 1.9798,-0.64782 3.3091,-1.3092 3.1873,-1.9433 -0.12275,-0.63917 -1.6227,-0.72941 -3.7298,-0.60065 0.16909,0.85159 0.34612,1.7023 0.54251,2.5439 z m -0.54251,-2.5439 c -0.22064,-1.1109 -0.43708,-2.2296 -0.61034,-3.3566 -5.7258,1.3834 -16.548,2.3861 -16.276,4.1692 0.27365,1.7905 11.022,-0.45432 16.886,-0.81265 z m -0.61034,-3.3566 c 2.0898,-0.50489 3.5332,-1.0772 3.4586,-1.7313 -0.0751,-0.6588 -1.6221,-0.86008 -3.7976,-0.88329 0.10439,0.8714 0.20647,1.752 0.33909,2.6146 z m -0.33909,-2.6146 c -0.13601,-1.136 -0.25207,-2.277 -0.33907,-3.4272 -5.8417,0.93312 -16.547,1.1809 -16.411,2.9679 0.13586,1.7937 10.848,0.39635 16.75,0.45931 z m -0.33907,-3.4272 c 2.1836,-0.34879 3.755,-0.77927 3.7298,-1.4486 -0.0253,-0.67583 -1.6611,-0.98143 -3.8994,-1.166 0.0372,0.87766 0.10375,1.7445 0.16954,2.6146 z m -0.16954,-2.6146 c -0.0477,-1.1242 -0.0678,-2.2561 -0.0678,-3.3919 0,-0.0119 -10e-6,-0.0236 0,-0.0353 -5.9223,0.47201 -16.478,-0.0832 -16.479,1.6959 -8.9e-4,1.7855 10.638,1.2442 16.547,1.7313 z m -0.0678,-3.4272 c 2.2726,-0.18114 3.9409,-0.48377 3.9672,-1.166 0.0265,-0.68547 -1.6298,-1.1294 -3.8994,-1.484 -0.0296,0.88035 -0.0674,1.7626 -0.0678,2.6499 z m 0.0678,-2.6499 c 0.0382,-1.1379 0.0833,-2.2665 0.16954,-3.3919 -5.9622,0.006 -16.276,-1.3026 -16.411,0.45932 -0.13586,1.7707 10.367,2.0148 16.242,2.9326 z m 0.16954,-3.3919 c 2.3444,-0.002 4.0912,-0.19302 4.1706,-0.88331 0.0804,-0.69853 -1.6056,-1.267 -3.9333,-1.8019 -0.0989,0.89073 -0.16851,1.7859 -0.23735,2.6852 z m 0.23735,-2.6852 c 0.12323,-1.1102 0.27181,-2.2252 0.44081,-3.3212 -5.9649,-0.46416 -16.009,-2.5106 -16.276,-0.77731 -0.26748,1.7384 10.013,2.7605 15.835,4.0985 z m 0.44081,-3.3212 c 2.4094,0.18747 4.2059,0.13026 4.3402,-0.56533 0.13501,-0.69948 -1.5365,-1.402 -3.8655,-2.1199 -0.17091,0.89801 -0.33464,1.7769 -0.4747,2.6852 z m 0.4747,-2.6852 c 0.21056,-1.1063 0.45566,-2.1963 0.71205,-3.2859 -5.9236,-0.92755 -15.677,-3.6774 -16.072,-1.9786 -0.39678,1.7044 9.6396,3.5011 15.36,5.2645 z m 0.71205,-3.2859 c 2.4391,0.38193 4.2865,0.48088 4.4758,-0.212 0.19013,-0.69578 -1.4841,-1.5344 -3.7976,-2.4379 -0.2387,0.87603 -0.46925,1.7622 -0.67815,2.6499 z m 0.67815,-2.6499 c 0.29398,-1.0789 0.61116,-2.155 0.94942,-3.2152 -5.8439,-1.385 -15.246,-4.8357 -15.767,-3.1799 -0.5226,1.6613 9.2322,4.2138 14.818,6.3951 z m 0.94942,-3.2152 c 2.4479,0.58019 4.3331,0.82624 4.5775,0.14134 0.24631,-0.69023 -1.3992,-1.6956 -3.6959,-2.7912 -0.31304,0.87262 -0.59881,1.7636 -0.88158,2.6499 z m 0.88158,-2.6499 c 0.37305,-1.0398 0.80562,-2.0545 1.2207,-3.0739 -5.7264,-1.832 -14.785,-5.916 -15.428,-4.3105 -0.64312,1.6066 8.7846,4.7974 14.207,7.3844 z m 1.2207,-3.0739 c 2.431,0.77776 4.3128,1.1655 4.6114,0.49465 0.30389,-0.68278 -1.2484,-1.8125 -3.5264,-3.1092 -0.38645,0.85916 -0.72884,1.7397 -1.085,2.6146 z m 1.085,-2.6146 c 0.44835,-0.99676 0.90209,-1.9932 1.3902,-2.9679 -5.5762,-2.2787 -14.162,-6.9465 -14.919,-5.4058 -0.75713,1.5408 8.2969,5.3954 13.529,8.3737 z m 1.3902,-2.9679 c 2.4286,0.99248 4.3242,1.5768 4.6792,0.91865 0.35975,-0.66692 -1.1349,-1.9367 -3.3568,-3.4272 -0.45201,0.82707 -0.89953,1.6642 -1.3224,2.5086 z m 1.3224,-2.5086 c 0.52611,-0.96272 1.0968,-1.8882 1.6615,-2.8266 -5.3875,-2.6956 -13.574,-7.9783 -14.445,-6.5011 -0.86692,1.4707 7.7781,5.9702 12.783,9.3277 z m 1.6615,-2.8266 c 2.3576,1.1796 4.24,1.9036 4.6453,1.272 0.41352,-0.64434 -1.016,-2.0342 -3.1534,-3.7099 -0.5228,0.80172 -0.99783,1.6168 -1.4919,2.4379 z m 1.4919,-2.4379 c 0.58841,-0.90229 1.2071,-1.7731 1.831,-2.6499 -5.1671,-3.1155 -12.829,-8.9912 -13.8,-7.5964 -0.97189,1.395 7.2238,6.5259 11.969,10.246 z m 1.831,-2.6499 c 2.3284,1.4039 4.15,2.2368 4.6114,1.6253 0.46424,-0.61523 -0.85705,-2.145 -2.8822,-3.9925 -0.59327,0.77231 -1.1643,1.5732 -1.7293,2.3672 z m 1.7293,-2.3672 c 0.65455,-0.85207 1.3125,-1.7192 2.0005,-2.5439 -4.9135,-3.507 -12.053,-9.8258 -13.122,-8.515 -1.0721,1.3141 6.6664,6.9944 11.122,11.059 z m 2.0005,-2.5439 c 2.2515,1.607 4.0311,2.631 4.5436,2.0493 0.51261,-0.58185 -0.74775,-2.1941 -2.6448,-4.2045 -0.64071,0.71422 -1.2841,1.4184 -1.8988,2.1552 z m 1.8988,-2.1552 c 0.72818,-0.81177 1.4771,-1.5856 2.2379,-2.3672 -4.6328,-3.8668 -11.278,-10.696 -12.444,-9.469 -1.1659,1.2265 6.0672,7.4499 10.206,11.836 z m 2.2379,-2.3672 c 2.1233,1.7722 3.8526,2.9442 4.408,2.4026 0.55839,-0.54459 -0.58458,-2.288 -2.3396,-4.4518 -0.70188,0.67076 -1.3915,1.3538 -2.0684,2.0492 z m 2.0684,-2.0492 c 0.77956,-0.74505 1.5641,-1.5122 2.3735,-2.2259 -4.3214,-4.2144 -10.381,-11.446 -11.63,-10.317 -1.2526,1.1324 5.4579,7.8592 9.2567,12.543 z m 2.3735,-2.2259 c 2.0136,1.9637 3.6367,3.2952 4.2384,2.7912 0.60355,-0.50546 -0.42429,-2.3451 -2.0344,-4.6638 -0.74567,0.6114 -1.4814,1.2354 -2.204,1.8726 z m 2.204,-1.8726 c 0.83882,-0.68775 1.7099,-1.3244 2.577,-1.9786 -3.9886,-4.5274 -9.4849,-12.197 -10.816,-11.165 -1.3296,1.0306 4.7994,8.1895 8.2395,13.144 z m 2.577,-1.9786 c 1.8584,2.1095 3.3967,3.5311 4.035,3.0739 0.64018,-0.4586 -0.26628,-2.3368 -1.6954,-4.7698 -0.78983,0.55279 -1.5712,1.1161 -2.3396,1.6959 z m 2.3396,-1.6959 c 0.88881,-0.62204 1.7976,-1.2151 2.7126,-1.8019 -3.6314,-4.817 -8.4969,-12.87 -9.901,-11.942 -1.4042,0.92769 4.1347,8.5455 7.1884,13.744 z m 2.7126,-1.8019 c 1.6999,2.2549 3.1243,3.8011 3.7976,3.3919 0.67804,-0.41204 -0.066,-2.4219 -1.3224,-4.9818 -0.84574,0.49984 -1.65,1.0606 -2.4752,1.5899 z m 2.4752,-1.5899 c 0.93048,-0.54994 1.86,-1.0766 2.8143,-1.59 -3.2494,-5.0826 -7.4532,-13.359 -8.9177,-12.543 -1.4657,0.8166 3.4471,8.7203 6.1034,14.133 z m 2.8143,-1.59 c 1.541,2.4103 2.8855,4.1057 3.5942,3.7452 0.70995,-0.36113 0.0818,-2.427 -0.98332,-5.0878 -0.8736,0.42994 -1.7557,0.88261 -2.6109,1.3426 z m 2.6109,-1.3426 c 0.95769,-0.47132 1.9371,-0.90835 2.916,-1.3426 -2.8513,-5.3146 -6.4146,-13.881 -7.9344,-13.179 -1.521,0.70282 2.7779,8.9251 5.0183,14.521 z m 2.916,-1.3426 c 1.3593,2.5336 2.5862,4.3351 3.3229,4.0279 0.73458,-0.30637 0.28165,-2.4294 -0.57643,-5.1585 -0.92212,0.36895 -1.8418,0.72928 -2.7465,1.1306 z m 2.7465,-1.1306 c 0.99948,-0.39989 1.9988,-0.80503 3.0178,-1.166 -2.438,-5.5147 -5.3495,-14.294 -6.9171,-13.709 -1.5709,0.58664 2.0918,9.1258 3.8994,14.875 z m 3.0178,-1.166 c 1.1649,2.635 2.2247,4.5265 2.9839,4.2752 0.75865,-0.25116 0.44999,-2.3923 -0.20345,-5.1938 -0.93683,0.29382 -1.8588,0.5922 -2.7804,0.91863 z m 2.7804,-0.91863 c 1.0176,-0.31914 2.0509,-0.63882 3.0856,-0.91863 -2.0115,-5.6826 -4.2248,-14.669 -5.8321,-14.203 -1.6081,0.46591 1.3777,9.2536 2.7465,15.122 z m 3.0856,-0.91863 c 0.96174,2.7169 1.9023,4.7161 2.6787,4.5225 0.77794,-0.19393 0.64531,-2.3736 0.20345,-5.2291 -0.96393,0.22308 -1.9319,0.44967 -2.8822,0.70664 z m 2.8822,-0.70664 c 1.0459,-0.24209 2.0922,-0.46999 3.1534,-0.67132 -1.5715,-5.8187 -3.1112,-14.935 -4.747,-14.592 -1.6365,0.3431 0.67219,9.3086 1.5937,15.263 z m 3.1534,-0.67132 c 0.75451,2.7936 1.5146,4.8342 2.3057,4.6992 0.79325,-0.13535 0.83651,-2.3334 0.61034,-5.2292 -0.98783,0.14997 -1.9404,0.34486 -2.916,0.52998 z m 2.916,-0.52998 c 1.0523,-0.15974 2.122,-0.26958 3.1873,-0.38865 -1.1249,-5.9202 -1.9367,-15.129 -3.5942,-14.91 -1.6551,0.21862 -0.0623,9.2914 0.4069,15.299 z m 3.1873,-0.38865 c 0.53989,2.8413 1.1677,4.9155 1.9666,4.8405 0.7955,-0.0747 0.98569,-2.2147 0.98333,-5.0878 -0.98422,0.0741 -1.976,0.13846 -2.95,0.24733 z m 2.95,-0.24733 c 1.0671,-0.0806 2.1429,-0.17305 3.2212,-0.21198 -0.67184,-5.9878 -0.8057,-15.216 -2.4752,-15.122 -1.6728,0.0937 -0.75092,9.3098 -0.74596,15.334 z m 3.2212,-0.21198 c 0.32234,2.8729 0.7576,4.9965 1.5598,4.9818 0.8022,-0.0147 1.1745,-2.1695 1.3902,-5.0525 -0.99057,3.8e-4 -1.9681,0.0351 -2.95,0.0707 z"
             inkscape:connector-curvature="0"
             style="fill:url(#radialGradient4295);stroke:#9f9999;stroke-width:0.470445;stroke-linecap:round;stroke-linejoin:round"
             id="path4529" />
        </g>
        <path
           d="m 430.58,947.04 c -22.306,0 -40.388,17.943 -40.388,40.067 0,22.124 18.082,40.048 40.388,40.048 22.306,0 40.388,-17.924 40.388,-40.048 0,-22.123 -18.082,-40.067 -40.388,-40.067 z m 0,2.9728 c 20.651,0 37.397,16.612 37.397,37.094 0,20.482 -16.746,37.075 -37.397,37.075 -20.651,0 -37.397,-16.593 -37.397,-37.075 0,-20.482 16.746,-37.094 37.397,-37.094 z"
           inkscape:connector-curvature="0"
           style="fill:url(#linearGradient4980);stroke-width:0"
           id="path4531" />
        <path
           d="m 237.14,859.82 c -4.7361,0.17699 -11.54,4.1577 -10.625,7.8438 3.211,12.942 10.609,33.844 10.609,33.844 l 24.75,2.0156 c 0,0 -16.498,-33.03 -20.703,-41.922 -0.63534,-1.3434 -2.178,-1.8505 -4.0312,-1.7812 z m 0.73437,5.9375 c 1.3922,-0.052 2.554,0.33453 3.0312,1.3438 3.1588,6.6794 15.547,31.484 15.547,31.484 l -18.594,-1.5156 c 0,0 -5.5566,-15.7 -7.9688,-25.422 -0.68703,-2.769 4.4266,-5.7577 7.9844,-5.8906 z"
           inkscape:connector-curvature="0"
           style="fill:url(#linearGradient4394);stroke:#6b0606;stroke-width:1.3081px"
           id="path4559" />
        <path
           d="m 256.97,898.59 -18.592,-1.5177 c 0,0 -5.5557,-15.699 -7.9679,-25.421 -0.95586,-3.8526 9.3063,-8.1414 11.003,-4.5531 3.1588,6.6794 15.556,31.492 15.556,31.492 z"
           inkscape:connector-curvature="0"
           style="opacity:0.75122;fill:url(#radialGradient4976);stroke:#e47b7b;stroke-width:0.982671px"
           sodipodi:nodetypes="ccssc"
           id="path4565" />
        <path
           d="m 33.585,913.63 c 0,0 -2.5336,-6.886 -9.5964,-14.647 -2.743,-3.0143 5.7885,-6.1236 9.5964,-7.5762 20.802,-7.9349 66.67,-4.0406 66.67,-4.0406 l 7.0711,13.637 c -23.86,-5.3017 -59.719,4.5553 -73.741,12.627 z"
           inkscape:connector-curvature="0"
           style="fill:url(#linearGradient4396);stroke-width:0"
           sodipodi:nodetypes="sssccs"
           id="path4576" />
        <rect
           x="263.57999"
           y="930.87"
           width="1.429"
           height="21.635"
           ry="1.6859"
           style="fill:url(#linearGradient4966);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4626" />
        <rect
           x="267.44"
           y="930.87"
           width="1.429"
           height="21.635"
           ry="1.6859"
           style="fill:url(#linearGradient4963);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4636" />
        <rect
           x="271.29999"
           y="930.87"
           width="1.429"
           height="21.635"
           ry="1.6859"
           style="fill:url(#linearGradient4960);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4640" />
        <rect
           x="274.39001"
           y="930.87"
           width="1.429"
           height="21.635"
           ry="1.6859"
           style="fill:url(#linearGradient4957);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4644" />
        <rect
           x="276.70999"
           y="930.87"
           width="1.429"
           height="21.635"
           ry="1.6859"
           style="fill:url(#linearGradient4954);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4648" />
        <rect
           x="279.79999"
           y="930.87"
           width="1.429"
           height="21.635"
           ry="1.6859"
           style="fill:url(#linearGradient4951);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4652" />
        <rect
           x="293.60001"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4948);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4656" />
        <rect
           x="297.5"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4945);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4658" />
        <rect
           x="301.39001"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4942);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4660" />
        <rect
           x="304.51001"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4939);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4662" />
        <rect
           x="306.85001"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4936);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4664" />
        <rect
           x="309.95999"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4933);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4666" />
        <rect
           x="304.89001"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4930);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4680" />
        <rect
           x="308.78"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4927);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4682" />
        <rect
           x="312.67999"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4924);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4684" />
        <rect
           x="315.79999"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4921);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4686" />
        <rect
           x="318.13"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4918);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4688" />
        <rect
           x="321.25"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4915);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4690" />
        <rect
           x="325.20999"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4912);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4704" />
        <rect
           x="329.10999"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4909);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4706" />
        <rect
           x="333"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4906);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4708" />
        <rect
           x="336.12"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4903);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4710" />
        <rect
           x="338.45999"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4900);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4712" />
        <rect
           x="341.57001"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4897);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4714" />
        <rect
           x="-315.16"
           y="1024.1"
           width="832.03003"
           height="10.312"
           ry="0.085992999"
           transform="matrix(0.53638,0,0,0.80255,192.16,214.4)"
           style="opacity:0.41951;fill:#2d2d2d;stroke:#660b0b;stroke-width:0.454094;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter4892)"
           id="rect4886" />
        <rect
           x="112.37"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.5595,0,0,0.38169,200.71,623.44)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.33245;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5350)"
           id="rect5110" />
        <rect
           x="119.27"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.5595,0,0,0.38169,200.71,623.44)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.33245;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5354)"
           id="rect5112" />
        <rect
           x="126.18"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.5595,0,0,0.38169,200.71,623.44)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.33245;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5358)"
           id="rect5114" />
        <rect
           x="131.7"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.5595,0,0,0.38169,200.71,623.44)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.33245;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5362)"
           id="rect5116" />
        <rect
           x="135.84"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.5595,0,0,0.38169,200.71,623.44)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.33245;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5366)"
           id="rect5118" />
        <rect
           x="141.36"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.5595,0,0,0.38169,200.71,623.44)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.33245;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5370)"
           id="rect5120" />
        <rect
           x="172.37"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5374)"
           id="rect5122" />
        <rect
           x="221.36"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5418)"
           id="rect5144" />
        <rect
           x="228.37"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5422)"
           id="rect5146" />
        <rect
           x="235.27"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5426)"
           id="rect5148" />
        <rect
           x="242.17999"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5430)"
           id="rect5150" />
        <rect
           x="247.7"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5434)"
           id="rect5152" />
        <rect
           x="251.84"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5438)"
           id="rect5154" />
        <rect
           x="257.35999"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5442)"
           id="rect5156" />
        <rect
           x="344.39999"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient5468);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect5446" />
        <rect
           x="348.29999"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient5470);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect5448" />
        <rect
           x="352.19"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient5472);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect5450" />
        <rect
           x="355.31"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient5474);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect5452" />
        <rect
           x="357.64999"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient5476);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect5454" />
        <rect
           x="360.76001"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient5478);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect5456" />
        <rect
           x="269.26999"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5426)"
           id="rect5458" />
        <rect
           x="276.17999"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5430)"
           id="rect5460" />
        <rect
           x="281.70001"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5434)"
           id="rect5462" />
        <rect
           x="285.84"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5438)"
           id="rect5464" />
        <rect
           x="291.35999"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5442)"
           id="rect5466" />
        <ellipse
           transform="matrix(0.78205,0,0,0.22222,185.13,737.72)"
           style="opacity:0.4878;fill:#ffffff;stroke-width:0;filter:url(#filter5499)"
           id="path5497"
           cx="178"
           cy="777.36218"
           rx="78"
           ry="9" />
        <path
           d="m 153.33,983.47 135,-1 3,-64 -9,-14"
           inkscape:connector-curvature="0"
           style="fill:none;stroke:#875c5c;stroke-width:1.3081px"
           id="path5503" />
        <path
           d="m 254.33,985.47 -14,-50 13,-3 v -8 l -13,-8"
           inkscape:connector-curvature="0"
           style="fill:none;stroke:#7a5252;stroke-width:1.3081px"
           id="path5505" />
        <path
           d="M 84.894,918.4 C 68.869,918.05255 52.773,923.1585 40.347,934.212 27.921,945.2655 19.225,962.265 17.956,985.4 l 2.75,0.14062 c 1.2354,-22.526 9.5943,-38.716 21.453,-49.266 11.859,-10.549 27.271,-15.459 42.672,-15.125 30.802,0.66785 61.365,22.149 63.125,61.391 l 2.75,-0.125 c -1.8219,-40.627 -33.762,-63.321 -65.813,-64.016 z"
           inkscape:connector-curvature="0"
           style="color:#000000;text-indent:0;text-transform:none;fill:url(#linearGradient4398);stroke-width:2.6162"
           id="path5507" />
        <path
           inkscape:connector-curvature="0"
           transform="matrix(0.5,0,0,0.5,207.33,521.29)"
           style="color:#000000;text-indent:0;text-transform:none;opacity:0.33659;fill:url(#linearGradient5531);stroke-width:2.6162;filter:url(#filter5537)"
           d="m -244.88,796.22 c -32.05,-0.6949 -64.242,9.517 -89.094,31.625 -24.852,22.108 -42.244,56.106 -44.781,102.38 l 5.5,0.28125 c 2.4708,-45.051 19.189,-77.433 42.906,-98.531 23.718,-21.099 54.541,-30.918 85.344,-30.25 61.605,1.3357 122.73,44.297 126.25,122.78 l 5.5,-0.25 c -3.6437,-81.254 -67.525,-126.64 -131.62,-128.03 z"
           id="path5521" />
        <path
           d="m 145.4,957.47 h 148.86 c 0.0397,0 38.106,5.0519 38.072,5.0717 l -38,21.857 c -0.0344,0.0198 -3.0329,-3.9368 -3.0717,-3.9283 -42.637,9.309 -95.829,4.5445 -145.86,4 -0.0397,-4.3e-4 -0.0717,-0.032 -0.0717,-0.0717 v -26.857 c 0,-0.0397 0.032,-0.0717 0.0717,-0.0717 z"
           inkscape:connector-curvature="0"
           style="opacity:0.31707;fill:url(#linearGradient4400);stroke-width:0"
           sodipodi:nodetypes="sssssssss"
           id="rect5541" />
        <path
           transform="matrix(0.5,0,0,0.5,207.33,521.29)"
           d="m 471.97,790.97 c -19.187,0.41332 -46.698,9.485 -69.562,18.406 -22.865,8.9212 -41.031,17.719 -41.031,17.719 l 1.25,2.5312 c 0,0 18.062,-8.7797 40.812,-17.656 22.75,-8.8766 50.242,-17.823 68.594,-18.219 9.2497,-0.19925 23.241,3.2464 34.781,6.7812 11.54,3.5348 20.688,7.125 20.688,7.125 l 1,-2.5938 c 0,0 -9.2241,-3.6187 -20.875,-7.1875 -11.6509,-3.5688 -25.669,-7.1214 -35.656,-6.9062 z"
           inkscape:connector-curvature="0"
           style="color:#000000;text-indent:0;text-transform:none;fill:url(#linearGradient5576);stroke-width:2.6162;filter:url(#filter5562)"
           id="path5560" />
        <ellipse
           transform="matrix(0.94285,0,0,0.51087,276.37,512.29)"
           style="opacity:0.6439;fill:url(#linearGradient5602);stroke-width:0"
           id="path5578"
           cx="-183"
           cy="781.36218"
           rx="31"
           ry="21" />
      </g>
      <g
         id="g2261-7"
         transform="matrix(1.2155564,0,0,1.2164629,587.74071,-189.61417)"
         style="stroke-width:2.6162">
        <path
           d="m 107.83,901 -4.5457,-11.112 c 5.5446,-5.4099 10.837,-11.325 24.244,-1.0102 0,0 5.2576,28.551 15.152,37.881 6.9724,6.5743 17.693,8.8746 27.274,9.0914 12.947,0.29289 25.694,-5.0155 37.376,-10.607 4.4664,-2.1377 7.416,-10.127 12.122,-8.5863 2.0239,0.66256 2.3983,3.9651 2.0203,6.0609 -17.718,40.015 -80.384,30.6 -79.802,16.668 2.1582,-7.9633 -6.367,-35.888 -33.84,-38.386 z"
           inkscape:connector-curvature="0"
           style="fill:url(#linearGradient3536);stroke-width:0"
           sodipodi:nodetypes="cccssaaccc"
           id="path4578-5" />
        <path
           inkscape:connector-curvature="0"
           style="fill:#0e232e;stroke:#000000;stroke-width:0.551495px"
           sodipodi:nodetypes="sssss"
           d="m 240.69,907.69 c -2.89,-1.8389 -9.8058,-4.8782 -15.637,-6.9591 -0.52945,-0.18892 -1.7035,1.2078 -1.1728,1.3918 3.3511,1.1618 11.327,5.1371 16.224,7.4231 0.51805,0.24185 1.077,-1.5436 0.58641,-1.8558 z"
           id="path4616-9" />
        <path
           d="m 233.3,876.16 c -4.3057,6.267 -11.422,21.264 -16.295,33.91 -0.44237,1.1481 2.8282,3.6941 3.2589,2.5433 2.7202,-7.2668 12.028,-24.564 17.381,-35.182 0.5663,-1.1234 -3.6143,-2.3356 -4.3452,-1.2716 z"
           inkscape:connector-curvature="0"
           style="fill:#0e232e;stroke:#000000;stroke-width:1.24269px"
           sodipodi:nodetypes="sssss"
           id="path4588-6" />
        <path
           d="m 0.25,992.42 23.739,1.0102 118.19,1.5152 c 0,0 57.245,-1.545 85.863,-1.0102 10.28,0.19214 20.537,1.9616 30.81,1.5152 44.117,-1.2672 61.545,-1.5717 110.66,-1.0508 l 101.47,-65.114 -26.769,-10.607 -78.792,18.688 c 0,0 -119.84,53.501 -180.31,41.416 -29.824,-5.9599 -74.246,-53.033 -74.246,-53.033 H 35.104 Z"
           inkscape:connector-curvature="0"
           style="fill:#2d2d2d;stroke:#000000;stroke-width:1.3081px"
           sodipodi:nodetypes="cccscccccsccc"
           id="path4557-0" />
        <ellipse
           transform="matrix(0.44138,0,0,0.48031,37.916,558.7)"
           style="fill:url(#linearGradient3538);stroke-width:0"
           id="path4263-8"
           cx="104.55079"
           cy="887.20227"
           rx="73.236061"
           ry="64.144684" />
        <ellipse
           transform="matrix(0.5251,0,0,0.5251,29.94,528.22)"
           style="fill:#2d2d2d;stroke-width:0;filter:url(#filter4259-4)"
           id="path4245-8"
           cx="104.55079"
           cy="870.02966"
           rx="19.697975"
           ry="18.687822" />
        <ellipse
           transform="matrix(0.34075,0,0,0.34075,85.276,700.17)"
           style="fill:#9eabb0;stroke-width:0"
           id="path4241-4"
           cx="-2.0203052"
           cy="834.67432"
           rx="19.192898"
           ry="18.687822" />
        <path
           d="m 84.094,951.83 c -18.234,0 -33.015,14.667 -33.015,32.752 0,18.085 14.781,32.736 33.015,32.736 18.234,0 33.015,-14.652 33.015,-32.736 0,-18.085 -14.781,-32.752 -33.015,-32.752 z m 0,2.4301 c 16.88,0 30.569,13.579 30.569,30.322 0,16.742 -13.689,30.306 -30.569,30.306 -16.88,0 -30.569,-13.564 -30.569,-30.306 0,-16.742 13.689,-30.322 30.569,-30.322 z"
           inkscape:connector-curvature="0"
           style="fill:url(#radialGradient5103-3);stroke:#817e7e;stroke-width:1.84657;stroke-linecap:round;stroke-linejoin:round"
           id="path4039-2" />
        <path
           inkscape:connector-curvature="0"
           style="fill:url(#radialGradient5100-1);stroke:#817e7e;stroke-width:1.95618;stroke-linecap:round;stroke-linejoin:round"
           d="m 84.094,949.89 c -19.316,0 -34.975,15.538 -34.975,34.696 0,19.158 15.659,34.68 34.975,34.68 19.316,0 34.975,-15.522 34.975,-34.68 0,-19.158 -15.659,-34.696 -34.975,-34.696 z m 0,2.5744 c 17.883,0 32.384,14.386 32.384,32.122 0,17.736 -14.502,32.106 -32.384,32.106 -17.883,0 -32.384,-14.369 -32.384,-32.106 0,-17.736 14.502,-32.122 32.384,-32.122 z"
           id="path4050-0" />
        <path
           d="m 84.094,947.46 c -20.67,0 -37.425,16.627 -37.425,37.127 0,20.5 16.756,37.11 37.425,37.11 20.67,0 37.425,-16.609 37.425,-37.11 0,-20.5 -16.756,-37.127 -37.425,-37.127 z m 0,2.7547 c 19.136,0 34.653,15.393 34.653,34.372 0,18.979 -15.518,34.355 -34.653,34.355 -19.136,0 -34.653,-15.376 -34.653,-34.355 0,-18.979 15.518,-34.372 34.653,-34.372 z"
           inkscape:connector-curvature="0"
           style="fill:#666666;stroke:#817e7e;stroke-width:2.09325;stroke-linecap:round;stroke-linejoin:round"
           id="path4054-1" />
        <g
           transform="matrix(-0.49937,0,0,0.49632,-25.588,625.19)"
           style="stroke:#333333;stroke-width:3.9243"
           id="g3167-1">
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -214.15,715.98 -14.142,-51.518"
             id="path3169-6" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -211.12,780.63 -17.173,-48.487"
             id="path3171-4" />
          <path
             d="m -212.77,718.6 -0.32647,-53.423"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path3173-1" />
          <path
             d="m -226.57,781.83 -4.038,-51.28"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path3175-5" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -211.8,721.08 13.511,-51.687"
             id="path3177-3" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -241.5,778.58 9.3718,-50.578"
             id="path3179-8" />
          <path
             d="m -211.33,723.27 26.429,-46.429"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path3181-1" />
          <path
             d="m -254.9,771.13 22.143,-46.429"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path3183-6" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -211.43,725.52 37.545,-38.006"
             id="path3185-6" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -265.91,760.47 33.405,-39.116"
             id="path3187-7" />
          <path
             d="m -212.12,727.66 46.102,-26.994"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path3189-8" />
          <path
             d="m -273.78,747.32 42.391,-29.137"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path3191-0" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -213.33,729.54 51.52,-14.14"
             id="path3193-6" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -277.98,732.57 48.487,-17.173"
             id="path3195-9" />
          <path
             d="m -214.99,731.05 53.423,-0.32649"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path3197-2" />
          <path
             d="m -278.22,717.25 51.28,-4.038"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path3199-4" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -217.47,732.02 51.687,13.511"
             id="path3201-8" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -274.97,702.32 50.578,9.3718"
             id="path3203-7" />
          <path
             d="m -219.66,732.5 46.429,26.429"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path3205-2" />
          <path
             d="m -267.52,688.93 46.429,22.143"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path3207-5" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -221.91,732.39 38.006,37.545"
             id="path3209-4" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -256.86,677.92 39.116,33.405"
             id="path3211-7" />
        </g>
        <g
           transform="matrix(0.47794,0,0,0.48362,188.55,633.61)"
           style="stroke:#4d3636;stroke-width:3.92482"
           id="g3051-7">
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -214.15,715.98 -14.142,-51.518"
             id="path3007-4" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -211.12,780.63 -17.173,-48.487"
             id="path3009-9" />
          <path
             d="m -212.77,718.6 -0.32647,-53.423"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path3011-4" />
          <path
             d="m -226.57,781.83 -4.038,-51.28"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path3013-9" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -211.8,721.08 13.511,-51.687"
             id="path3015-4" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -241.5,778.58 9.3718,-50.578"
             id="path3017-0" />
          <path
             d="m -211.33,723.27 26.429,-46.429"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path3019-0" />
          <path
             d="m -254.9,771.13 22.143,-46.429"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path3021-3" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -211.43,725.52 37.545,-38.006"
             id="path3023-2" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -265.91,760.47 33.405,-39.116"
             id="path3025-7" />
          <path
             d="m -212.12,727.66 46.102,-26.994"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path3027-3" />
          <path
             d="m -273.78,747.32 42.391,-29.137"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path3029-4" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -213.33,729.54 51.52,-14.14"
             id="path3031-6" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -277.98,732.57 48.487,-17.173"
             id="path3033-3" />
          <path
             d="m -214.99,731.05 53.423,-0.32649"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path3035-6" />
          <path
             d="m -278.22,717.25 51.28,-4.038"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path3037-4" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -217.47,732.02 51.687,13.511"
             id="path3039-9" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -274.97,702.32 50.578,9.3718"
             id="path3041-8" />
          <path
             d="m -219.66,732.5 46.429,26.429"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path3043-7" />
          <path
             d="m -267.52,688.93 46.429,22.143"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path3045-9" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -221.91,732.39 38.006,37.545"
             id="path3047-7" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -256.86,677.92 39.116,33.405"
             id="path3049-2" />
        </g>
        <ellipse
           transform="matrix(0.2962,0,0,0.2962,85.186,737.36)"
           style="fill:url(#linearGradient3540);stroke:#333333;stroke-width:3.9243;stroke-linecap:round;stroke-linejoin:round"
           id="path4229-8"
           cx="-2.0203052"
           cy="834.67432"
           rx="19.192898"
           ry="18.687822" />
        <g
           transform="matrix(0.5599,0,0,0.55238,222.05,473.09)"
           style="stroke:#554f4f;stroke-width:2.61562"
           id="g4289-2">
          <path
             inkscape:connector-curvature="0"
             style="fill:url(#radialGradient2267-1);stroke:#554f4f;stroke-width:2.61562;stroke-linecap:round;stroke-linejoin:round"
             d="m -245.98,826.55 c -55.038,0 -99.649,44.13 -99.649,98.548 0,54.418 44.611,98.519 99.649,98.519 55.038,0 99.677,-44.102 99.677,-98.519 0,-54.418 -44.639,-98.548 -99.677,-98.548 z m 0,26.945 c 39.99,0 72.414,32.064 72.414,71.603 0,39.539 -32.424,71.575 -72.414,71.575 -39.99,0 -72.387,-32.035 -72.387,-71.575 0,-39.539 32.397,-71.603 72.387,-71.603 z"
             id="path4058-8" />
          <path
             inkscape:connector-curvature="0"
             style="fill:url(#radialGradient2269-7);stroke:#554f4f;stroke-width:2.61562;stroke-linecap:round;stroke-linejoin:round"
             d="m -244.61,826.11 c -1.6718,-0.0324 -1.4493,9.1844 -1.8988,15.193 h 0.0339 c 1.0817,0 2.1499,0.029 3.2212,0.0707 -0.21479,-6.0212 0.31566,-15.231 -1.3563,-15.263 z m 1.3563,15.263 c 0.10313,2.8885 0.35196,5.0421 1.1529,5.0878 0.80081,0.0456 1.329,-2.0538 1.7632,-4.9112 -0.96597,-0.072 -1.9409,-0.13863 -2.9161,-0.17665 z m 2.9161,0.17665 c 1.08,0.0806 2.1536,0.19581 3.2212,0.318 0.24291,-6.0201 1.5296,-15.176 -0.13562,-15.334 -1.6648,-0.15802 -2.1804,9.0588 -3.0856,15.016 z m 3.2212,0.318 c -0.11639,2.8848 -0.0146,5.0527 0.77989,5.1585 0.79573,0.10599 1.4855,-1.985 2.1362,-4.8052 -0.97056,-0.14693 -1.9344,-0.24097 -2.9161,-0.35331 z m 2.9161,0.35331 c 1.0702,0.16202 2.1315,0.3621 3.1873,0.56531 0.70049,-5.9846 2.6651,-15.016 1.0172,-15.299 -1.6474,-0.2827 -2.8498,8.861 -4.2045,14.733 z m 3.1873,0.56531 c -0.33604,2.871 -0.37794,5.0638 0.40689,5.2292 0.78191,0.16473 1.6161,-1.849 2.4752,-4.5932 -0.95558,-0.22034 -1.9138,-0.44961 -2.8821,-0.63599 z m 2.8821,0.63599 c 1.0556,0.2434 2.1142,0.49329 3.1534,0.77731 1.1507,-5.9147 3.7941,-14.786 2.1701,-15.193 -1.6246,-0.40672 -3.5227,8.6637 -5.3235,14.416 z m 3.1534,0.77731 c -0.54956,2.8248 -0.76767,5.0059 0,5.2291 0.7684,0.22344 1.7471,-1.7381 2.8143,-4.4165 -0.92615,-0.2895 -1.874,-0.55565 -2.8143,-0.81262 z m 2.8143,0.81262 c 1.0302,0.32204 2.0399,0.66323 3.0517,1.0246 1.597,-5.8112 4.9114,-14.453 3.3229,-14.981 -1.5878,-0.52739 -4.143,8.3555 -6.3746,13.956 z m 3.0517,1.0246 c -0.76267,2.7752 -1.1215,4.9135 -0.37299,5.1938 0.74738,0.27995 1.8894,-1.5534 3.1534,-4.1338 -0.91975,-0.36741 -1.8442,-0.72553 -2.7804,-1.06 z m 2.7804,1.06 c 1.007,0.40228 1.9976,0.83106 2.9838,1.272 2.0314,-5.6747 5.988,-14.087 4.4419,-14.733 -1.545,-0.64609 -4.773,8.0462 -7.4257,13.462 z m 2.9838,1.272 c -0.96564,2.6975 -1.5365,4.753 -0.81377,5.0878 0.72165,0.33431 2.0078,-1.3857 3.4586,-3.8512 -0.87151,-0.42808 -1.7557,-0.83918 -2.6448,-1.2366 z m 2.6448,1.2366 c 0.96152,0.4723 1.9429,0.93991 2.8822,1.4486 2.4572,-5.5056 7.0533,-13.584 5.5608,-14.345 -1.4936,-0.76163 -5.3841,7.6979 -8.443,12.896 z m 2.8822,1.4486 c -1.1683,2.6178 -1.8482,4.6642 -1.1529,5.0525 0.69397,0.38753 2.0625,-1.2557 3.6959,-3.6039 -0.8429,-0.49709 -1.6806,-0.98145 -2.543,-1.4486 z m 2.543,1.4486 c 0.93425,0.55097 1.8372,1.1445 2.7465,1.7313 2.8619,-5.3066 8.1152,-13.082 6.6798,-13.956 -1.4317,-0.87197 -5.9809,7.2718 -9.4263,12.225 z m 2.7465,1.7313 c -1.3457,2.4952 -2.1847,4.4396 -1.5258,4.8758 0.65669,0.43479 2.1472,-1.0638 3.9333,-3.2505 -0.79343,-0.55349 -1.5932,-1.0998 -2.4074,-1.6253 z m 2.4074,1.6253 c 0.89266,0.62269 1.7794,1.2508 2.6448,1.9079 3.2542,-5.0768 9.0979,-12.445 7.7309,-13.426 -1.367,-0.98127 -6.559,6.8453 -10.376,11.518 z m 2.6448,1.9079 c -1.5229,2.3758 -2.4867,4.2515 -1.8649,4.7345 0.61993,0.48153 2.2312,-0.89633 4.1706,-2.9326 -0.75602,-0.61873 -1.5268,-1.2104 -2.3057,-1.8019 z m 2.3057,1.8019 c 0.8283,0.67785 1.6415,1.375 2.4414,2.0846 3.6257,-4.819 10.074,-11.741 8.782,-12.826 -1.2902,-1.0828 -7.0611,6.371 -11.223,10.741 z m 2.4414,2.0846 c -1.6829,2.2367 -2.7501,4.0324 -2.1701,4.5578 0.57819,0.52375 2.2344,-0.71318 4.3062,-2.5792 -0.69861,-0.66606 -1.4136,-1.3377 -2.1362,-1.9786 z m 2.1362,1.9786 c 0.78547,0.74887 1.5515,1.5169 2.3057,2.2966 3.9737,-4.5351 10.976,-11.007 9.7654,-12.19 -1.2075,-1.1799 -7.5864,5.8539 -12.071,9.893 z m 2.3057,2.2966 c -1.8212,2.0786 -3.009,3.7478 -2.4752,4.3105 0.53371,0.56256 2.2515,-0.50216 4.4419,-2.1906 -0.64738,-0.7195 -1.2936,-1.4242 -1.9666,-2.1199 z m 1.9666,2.1199 c 0.72923,0.81043 1.4744,1.5983 2.1701,2.4379 4.307,-4.2231 11.764,-10.177 10.647,-11.448 -1.1172,-1.2707 -8.0368,5.3249 -12.817,9.0097 z m 2.1701,2.4379 c -1.974,1.9356 -3.3038,3.4973 -2.8143,4.0985 0.48671,0.59779 2.2832,-0.33317 4.5775,-1.8373 -0.58076,-0.75413 -1.1561,-1.5286 -1.7632,-2.2613 z m 1.7632,2.2613 c 0.66776,0.86706 1.3007,1.7554 1.9327,2.6499 4.6043,-3.8919 12.585,-9.3118 11.562,-10.67 -1.02,-1.3545 -8.4477,4.7114 -13.495,8.0204 z m 1.9327,2.6499 c -2.0735,1.7527 -3.4548,3.1872 -3.0178,3.8159 0.43555,0.62652 2.2772,-0.14489 4.6453,-1.4486 -0.52538,-0.80374 -1.074,-1.5838 -1.6276,-2.3672 z m 1.6276,2.3672 c 0.59415,0.9089 1.1722,1.822 1.7293,2.7559 4.8809,-3.5371 13.329,-8.3514 12.41,-9.787 -0.91817,-1.4338 -8.8546,4.1216 -14.139,7.0311 z m 1.7293,2.7559 c -2.1782,1.5785 -3.6407,2.8776 -3.2551,3.5332 0.38091,0.64765 2.2726,-0.004 4.6792,-1.0953 -0.45464,-0.82804 -0.94092,-1.6279 -1.4241,-2.4379 z m 1.4241,2.4379 c 0.52864,0.96283 1.0368,1.9463 1.5258,2.9326 5.1266,-3.1644 13.967,-7.3615 13.156,-8.8683 -0.81198,-1.5088 -9.1931,3.4488 -14.682,5.9358 z m 1.5258,2.9326 c -2.2573,1.3933 -3.824,2.5741 -3.4925,3.2506 0.32625,0.66563 2.2448,0.17614 4.6792,-0.70664 -0.3857,-0.85292 -0.77189,-1.7072 -1.1868,-2.5439 z m 1.1868,2.5439 c 0.45698,1.0106 0.87345,2.0415 1.2885,3.0739 5.3246,-2.7834 14.604,-6.3333 13.902,-7.9144 -0.69893,-1.5745 -9.5324,2.7887 -15.191,4.8405 z m 1.2885,3.0739 c -2.2592,1.181 -3.9002,2.251 -3.6281,2.9325 0.27248,0.68268 2.1814,0.33166 4.6453,-0.35331 -0.31478,-0.87086 -0.67289,-1.7227 -1.0172,-2.5792 z m 1.0172,2.5792 c 0.37906,1.0487 0.74983,2.1114 1.085,3.1799 5.5142,-2.3782 15.093,-5.2564 14.512,-6.8898 -0.57927,-1.6283 -9.8033,2.0991 -15.597,3.7099 z m 1.085,3.1799 c -2.3205,1.0008 -4.0158,1.9185 -3.7976,2.6146 0.21623,0.68984 2.0966,0.47846 4.5436,0 -0.24103,-0.87328 -0.47582,-1.7535 -0.74595,-2.6146 z m 0.74595,2.6146 c 0.29585,1.0719 0.5627,2.1612 0.81378,3.2506 5.6584,-1.9682 15.581,-4.1096 15.123,-5.7945 -0.45697,-1.6788 -10.047,1.3924 -15.937,2.5439 z m 0.81378,3.2506 c -2.3221,0.80773 -3.9933,1.5986 -3.8316,2.2966 0.16075,0.69353 1.9891,0.66848 4.408,0.38865 -0.17493,-0.90294 -0.37089,-1.7934 -0.57644,-2.6852 z m 0.57644,2.6852 c 0.21475,1.1085 0.44269,2.1969 0.61033,3.3212 5.7816,-1.5431 15.928,-2.9788 15.597,-4.6992 -0.33009,-1.719 -10.26,0.68988 -16.208,1.3779 z m 0.61033,3.3212 c -2.3447,0.62579 -4.0406,1.241 -3.9333,1.9433 0.1061,0.69399 1.8912,0.83119 4.2724,0.74197 -0.10228,-0.89597 -0.20689,-1.7986 -0.33908,-2.6852 z m 0.33908,2.6852 c 0.12708,1.1128 0.22506,2.2303 0.30516,3.3565 5.8559,-1.1212 16.239,-1.7428 16.038,-3.4979 -0.19956,-1.7486 -10.374,-0.0823 -16.343,0.14133 z m 0.30516,3.3565 c -2.3058,0.44149 -3.9519,0.96708 -3.8994,1.6606 0.0521,0.68644 1.7269,0.92912 4.035,1.0246 -0.0335,-0.89992 -0.0722,-1.7932 -0.13562,-2.6852 z m 0.13562,2.6852 c 0.0365,0.98504 0.0678,1.974 0.0678,2.9679 0,0.14188 7.7e-4,0.28227 0,0.42397 5.9003,-0.69454 16.444,-0.51878 16.377,-2.2966 -0.0666,-1.7722 -10.499,-0.84927 -16.445,-1.0953 z m 0.0678,3.3919 c -2.2644,0.26655 -3.899,0.65985 -3.8994,1.3426 -3.3e-4,0.67641 1.6013,1.074 3.8315,1.3426 0.0346,-0.89088 0.063,-1.7871 0.0678,-2.6852 z m -0.0678,2.6852 c -0.0443,1.139 -0.11112,2.2656 -0.20344,3.3919 5.9106,-0.26845 16.58,0.69526 16.649,-1.0953 0.0688,-1.7841 -10.559,-1.5875 -16.445,-2.2966 z m -0.20344,3.3919 c -2.2098,0.10047 -3.8142,0.35665 -3.8655,1.0246 -0.0509,0.6623 1.4556,1.1595 3.5942,1.59 0.1004,-0.86818 0.19941,-1.7381 0.27126,-2.6146 z m -0.27126,2.6146 c -0.13102,1.1332 -0.26209,2.2737 -0.44079,3.3919 5.8937,0.15957 16.612,1.9668 16.818,0.17666 0.20562,-1.7859 -10.59,-2.4035 -16.377,-3.5685 z m -0.44079,3.3919 c -2.1584,-0.0585 -3.6973,0.0897 -3.7976,0.74197 -0.0992,0.6442 1.2887,1.2574 3.3229,1.8373 0.16616,-0.8552 0.3365,-1.7145 0.4747,-2.5792 z m -0.4747,2.5792 c -0.21901,1.1272 -0.47933,2.2468 -0.74598,3.3565 5.8254,0.5657 16.575,3.2718 16.92,1.484 0.34315,-1.7776 -10.524,-3.23 -16.174,-4.8405 z m -0.74598,3.3565 c -2.0524,-0.19928 -3.5166,-0.16549 -3.662,0.45932 -0.14419,0.61937 1.1149,1.3021 3.0178,2.0139 0.22518,-0.81852 0.44512,-1.6444 0.64424,-2.4733 z m -0.64424,2.4733 c -0.3099,1.1266 -0.62513,2.2504 -0.98333,3.3566 5.738,0.97369 16.403,4.5248 16.886,2.7559 0.48151,-1.7621 -10.437,-4.0678 -15.903,-6.1124 z m -0.98333,3.3566 c -1.9679,-0.33395 -3.3715,-0.38801 -3.5603,0.212 -0.18781,0.597 0.95379,1.3538 2.7465,2.1906 0.28785,-0.79903 0.55129,-1.592 0.81376,-2.4026 z m -0.81376,2.4026 c -0.39653,1.1007 -0.84488,2.1728 -1.2885,3.2505 5.6095,1.359 16.196,5.8068 16.818,4.0632 0.6175,-1.7304 -10.267,-4.8575 -15.53,-7.3137 z m -1.2885,3.2505 c -1.8542,-0.44918 -3.1633,-0.56817 -3.3907,0 -0.22554,0.56342 0.775,1.3646 2.4074,2.2966 0.34094,-0.75878 0.66594,-1.5255 0.98331,-2.2966 z m -0.98331,2.2966 c -0.48023,1.0688 -0.96605,2.1366 -1.4919,3.1799 5.4619,1.7408 15.823,7.0738 16.581,5.3705 0.75507,-1.6965 -10.094,-5.6991 -15.089,-8.5504 z m -1.4919,3.1799 c -1.7559,-0.55966 -3.0246,-0.75004 -3.289,-0.21199 -0.26115,0.53141 0.64728,1.3846 2.1362,2.4026 0.39316,-0.72281 0.78189,-1.4546 1.1528,-2.1906 z m -1.1528,2.1906 c -0.56636,1.0412 -1.1519,2.0611 -1.7632,3.0739 5.2747,2.0899 15.348,8.3349 16.242,6.6778 0.88897,-1.648 -9.7709,-6.533 -14.478,-9.7517 z m -1.7632,3.0739 c -1.6298,-0.64574 -2.7901,-0.88991 -3.0856,-0.38867 -0.28973,0.49152 0.48355,1.3674 1.7971,2.4379 0.43591,-0.67499 0.87284,-1.3606 1.2885,-2.0493 z m -1.2885,2.0493 c -0.64982,1.0062 -1.3407,1.9932 -2.0344,2.9679 5.0502,2.3982 14.839,9.5542 15.869,7.9497 1.0234,-1.5947 -9.4726,-7.3629 -13.834,-10.918 z m -2.0344,2.9679 c -1.4809,-0.70324 -2.5625,-1.0241 -2.8821,-0.56532 -0.31749,0.45569 0.32534,1.3866 1.4919,2.5086 0.48225,-0.63628 0.92702,-1.2925 1.3902,-1.9433 z m -1.3902,1.9433 c -0.73365,0.96799 -1.4613,1.9288 -2.2379,2.8619 4.8324,2.7318 14.171,10.681 15.326,9.151 1.151,-1.5254 -9.0752,-8.1532 -13.088,-12.013 z m -2.2379,2.8619 c -1.3847,-0.78279 -2.3992,-1.1677 -2.7465,-0.74196 -0.34026,0.41706 0.20736,1.3212 1.2207,2.4732 0.50675,-0.57462 1.0357,-1.1423 1.5258,-1.7313 z m -1.5258,1.7313 c -0.8058,0.91369 -1.6286,1.809 -2.4753,2.6852 4.5693,2.9959 13.4,11.842 14.682,10.388 1.2751,-1.4474 -8.5759,-8.9449 -12.207,-13.073 z m -2.4753,2.6852 c -1.2454,-0.81658 -2.2128,-1.1958 -2.577,-0.81266 -0.36197,0.38082 0.10515,1.2569 0.98333,2.4379 0.54491,-0.53281 1.0642,-1.0773 1.5936,-1.6252 z m -1.5936,1.6252 c -0.89166,0.87188 -1.8146,1.7137 -2.7465,2.5439 4.3018,3.261 12.605,12.883 14.004,11.518 1.3908,-1.3564 -8.0106,-9.6962 -11.257,-14.062 z m -2.7465,2.5439 c -1.1313,-0.85755 -1.9922,-1.2987 -2.3735,-0.95396 -0.37389,0.33803 -0.007,1.2005 0.71206,2.3672 0.5557,-0.46874 1.119,-0.93001 1.6615,-1.4133 z m -1.6615,1.4133 c -0.96131,0.8109 -1.9503,1.6011 -2.95,2.3673 4.0103,3.4734 11.642,13.917 13.156,12.649 1.5065,-1.2618 -7.4018,-10.468 -10.206,-15.016 z m -2.95,2.3673 c -1.0041,-0.86972 -1.8131,-1.3276 -2.204,-1.0246 -0.39217,0.30398 -0.13183,1.1799 0.4747,2.3672 0.58957,-0.42707 1.1521,-0.90024 1.7293,-1.3426 z m -1.7293,1.3426 c -1.018,0.73742 -2.0659,1.4294 -3.1195,2.1199 3.7236,3.7177 10.628,14.82901 12.241,13.67401 1.6047,-1.14961 -6.7086,-11.07101 -9.1211,-15.79301 z m -3.1195,2.1199 c -0.91212,-0.91066 -1.6288,-1.3986 -2.0344,-1.1306 -0.40201,0.26557 -0.24215,1.1236 0.23735,2.2966 0.61057,-0.37726 1.1976,-0.77308 1.7971,-1.166 z m -1.7971,1.166 c -1.0815,0.66828 -2.1739,1.2898 -3.289,1.908 3.4197,3.9218 9.4836,15.70001 11.189,14.66301 1.6999,-1.0332 -5.9212,-11.72901 -7.9005,-16.57101 z m -3.289,1.908 c -0.81353,-0.93297 -1.4839,-1.3971 -1.8988,-1.166 -0.40945,0.22813 -0.32782,1.0025 0.0339,2.1552 0.62448,-0.32506 1.2503,-0.64861 1.8649,-0.98928 z m -1.8649,0.98928 c -1.1324,0.5895 -2.295,1.159 -3.4586,1.6959 3.1054,4.098 8.3482,16.45703 10.138,15.54603 1.7854,-0.9081 -5.1342,-12.31703 -6.6798,-17.24203 z m -3.4586,1.6959 c -0.71663,-0.94569 -1.3424,-1.431 -1.7632,-1.2366 -0.4161,0.19227 -0.39079,0.95079 -0.13564,2.0846 0.63849,-0.27483 1.2691,-0.55735 1.8988,-0.84797 z m -1.8988,0.84797 c -1.1935,0.51379 -2.4053,0.99118 -3.6281,1.4486 2.7831,4.2288 7.118,17.10196 8.9855,16.32296 1.859,-0.7752 -4.2363,-12.79096 -5.3574,-17.77196 z m -3.6281,1.4486 c -0.61783,-0.93876 -1.1389,-1.4644 -1.5597,-1.3073 -0.41946,0.15665 -0.49275,0.90868 -0.33907,2.0139 0.639,-0.22088 1.2673,-0.47041 1.8988,-0.70666 z m -1.8988,0.70666 c -1.2181,0.42105 -2.4519,0.80193 -3.6959,1.166 2.4563,4.3723 5.8017,17.633 7.7309,16.995 1.9228,-0.6366 -3.3399,-13.162 -4.035,-18.161 z m -3.6959,1.166 c -0.53312,-0.94899 -1.0343,-1.5007 -1.458,-1.3779 -0.42072,0.12189 -0.56788,0.87078 -0.5086,1.9432 0.65902,-0.17484 1.3144,-0.37444 1.9666,-0.56531 z m -1.9666,0.56531 c -1.2613,0.33466 -2.5466,0.60852 -3.8316,0.88327 2.127,4.46142 4.4921,18.09002 6.4763,17.59502 1.9752,-0.4923 -2.3693,-13.495 -2.6448,-18.47902 z m -3.8316,0.88327 c -0.4459,-0.93525 -0.86884,-1.5012 -1.2885,-1.4132 -0.41764,0.0875 -0.61427,0.77556 -0.64424,1.802 0.64652,-0.12198 1.2918,-0.2516 1.9327,-0.38877 z m -1.9327,0.38877 c -1.2908,0.24328 -2.5875,0.45426 -3.8994,0.63588 1.788,4.57657 3.0992,18.43497 5.12,18.08997 2.0182,-0.3443 -1.3644,-13.802 -1.2207,-18.72597 z m -3.8994,0.63588 c -0.36984,-0.94666 -0.76634,-1.5041 -1.1868,-1.4486 -0.41771,0.0554 -0.63631,0.69735 -0.74598,1.696 0.65124,-0.0742 1.2863,-0.15797 1.9327,-0.24745 z m -1.9327,0.24745 c -1.3121,0.14946 -2.6367,0.26672 -3.9672,0.35312 1.4435,4.6673 1.7179,18.636 3.7638,18.444 2.044,-0.1922 -0.33086,-13.932 0.20344,-18.79697 z m -3.9672,0.35312 c -0.29468,-0.95279 -0.63225,-1.54277 -1.0511,-1.51917 -0.42054,0.0236 -0.69635,0.67387 -0.8816,1.66047 0.64621,-0.027 1.2906,-0.1 1.9327,-0.1413 z m -1.9327,0.1413 c -1.1382,0.048 -2.2747,0.07 -3.4246,0.07 h -0.54254 c 1.0797,4.776 0.28709,18.658 2.3396,18.62 2.0529,-0.039 0.72451,-13.881 1.6275,-18.691 z m -3.9672,0.07 c -0.22172,-0.98073 -0.52789,-1.61707 -0.9494,-1.62517 -0.41901,-0.01 -0.72426,0.59349 -0.98333,1.55457 0.64452,0.02 1.2845,0.066 1.9327,0.07 z m -1.9327,-0.07 c -1.3401,-0.039 -2.6782,-0.1077 -4.001,-0.212 0.7154,4.8395 -1.1347,18.644 0.91548,18.761 2.052,0.1174 1.815,-13.835 3.0856,-18.549 z m -4.001,-0.212 c -0.14666,-0.99183 -0.39476,-1.62077 -0.8138,-1.66047 -0.41813,-0.0401 -0.78695,0.5411 -1.1189,1.484 0.63854,0.0653 1.2898,0.12617 1.9327,0.17657 z m -1.9327,-0.17651 c -1.3174,-0.13501 -2.6355,-0.2623 -3.9333,-0.45936 0.30978,4.94517 -2.5333,18.45697 -0.50862,18.72597 2.0363,0.271 2.8181,-13.655 4.4419,-18.26697 z m -3.9333,-0.45936 c -0.0654,-1.0452 -0.25269,-1.7289 -0.67816,-1.8019 -0.42202,-0.0729 -0.84416,0.49994 -1.2546,1.4486 0.64952,0.11498 1.2779,0.25384 1.9327,0.35332 z m -1.9327,-0.35332 c -1.3035,-0.23054 -2.5846,-0.48548 -3.8655,-0.77732 -0.0719,4.96181 -3.9299,18.19901 -1.9327,18.62001 2.0018,0.4216 3.8373,-13.31 5.7982,-17.84301 z m -3.8655,-0.77732 c 0.0151,-1.0526 -0.12255,-1.7674 -0.54251,-1.8726 -0.42702,-0.10697 -0.92774,0.44942 -1.4241,1.4133 0.64949,0.16508 1.3109,0.30991 1.9666,0.45927 z m -1.9666,-0.45927 c -1.2619,-0.3208 -2.5266,-0.64574 -3.7637,-1.0246 -0.50939,5.02108 -5.268,17.73598 -3.3229,18.30198 1.9525,0.568 4.7935,-12.824 7.0867,-17.27698 z m -3.7637,-1.0246 c 0.11263,-1.1101 0.052,-1.8374 -0.37298,-1.9786 -0.42212,-0.14022 -0.95028,0.3647 -1.5258,1.3073 0.6366,0.21255 1.2552,0.47418 1.8988,0.6713 z m -1.8988,-0.6713 c -1.2471,-0.4164 -2.4771,-0.86833 -3.6959,-1.3426 -0.89058,4.9692 -6.6094,17.30898 -4.7131,18.01898 1.8993,0.7115 5.7842,-12.378 8.4091,-16.67698 z m -3.6959,-1.3426 c 0.19703,-1.0994 0.20981,-1.8411 -0.20345,-2.0139 -0.42771,-0.17888 -1.0142,0.33595 -1.6954,1.3073 0.62125,0.25989 1.2697,0.46183 1.8988,0.70664 z m -1.8988,-0.70664 c -1.1842,-0.4954 -2.3381,-1.041 -3.4925,-1.59 -1.3382,4.9564 -7.8921,16.64502 -6.0694,17.48902 1.8226,0.8444 6.6104,-11.69002 9.5619,-15.89902 z m -3.4925,-1.59 c 0.3088,-1.1438 0.34508,-1.9447 -0.0678,-2.1553 -0.41571,-0.21196 -1.0372,0.30721 -1.7971,1.2366 0.61453,0.31187 1.2413,0.62209 1.8649,0.91864 z m -1.8649,-0.91864 c -1.1475,-0.58232 -2.2761,-1.2029 -3.3908,-1.8373 -1.7554,4.877 -9.103,15.91396 -7.3579,16.88896 1.7505,0.9775 7.4832,-11.05696 10.749,-15.05096 z m -3.3908,-1.8373 c 0.41514,-1.1533 0.50537,-1.9801 0.10166,-2.2259 -0.41059,-0.25007 -1.1026,0.24309 -1.9666,1.166 0.61369,0.37128 1.2405,0.70461 1.8649,1.06 z m -1.8649,-1.06 c -1.0818,-0.65448 -2.14,-1.3112 -3.1873,-2.0139 -2.2115,4.7917 -10.195,15.08896 -8.5447,16.18196 1.6592,1.0986 8.1549,-10.34696 11.732,-14.16796 z m -3.1873,-2.0139 c 0.54872,-1.1889 0.73799,-2.0456 0.33909,-2.3319 -0.39906,-0.28643 -1.1407,0.16648 -2.1023,1.06 0.58985,0.4193 1.1615,0.8682 1.7632,1.272 z m -1.7632,-1.272 c -1.0236,-0.72756 -2.031,-1.4881 -3.0178,-2.2612 -2.5943,4.6255 -11.29,14.229 -9.7315,15.44 1.5626,1.2136 8.8754,-9.5793 12.749,-13.179 z m -3.0178,-2.2612 c 0.65198,-1.1625 0.92238,-2.0484 0.54252,-2.3672 -0.38539,-0.32342 -1.2076,0.12922 -2.2718,0.98931 0.5662,0.46912 1.15,0.92407 1.7293,1.3779 z m -1.7293,-1.3779 c -0.95598,-0.79212 -1.8969,-1.6035 -2.8143,-2.4379 -3.0257,4.4568 -12.232,13.209 -10.783,14.521 1.4569,1.3197 9.4381,-8.7226 13.597,-12.084 z m -2.8143,-2.4379 c 0.79465,-1.1705 1.181,-2.0791 0.81377,-2.4379 -0.37256,-0.36408 -1.2875,0.0824 -2.4752,0.91863 0.54082,0.52022 1.1061,1.0141 1.6615,1.5193 z m -1.6615,-1.5193 c -0.88518,-0.8515 -1.7321,-1.7239 -2.577,-2.6146 -3.4357,4.2447 -13.134,12.162 -11.8,13.568 1.34,1.4125 9.9367,-7.8275 14.377,-10.953 z m -2.577,-2.6146 c 0.93939,-1.1606 1.4348,-2.0754 1.085,-2.4732 -0.35415,-0.4028 -1.3392,-0.0147 -2.6448,0.77731 0.51273,0.57255 1.0308,1.1383 1.5597,1.6959 z m -1.5597,-1.6959 c -0.81768,-0.91307 -1.6323,-1.8415 -2.4074,-2.7912 -3.8346,3.9962 -13.859,11.055 -12.647,12.543 1.2189,1.4971 10.355,-6.9001 15.055,-9.7516 z m -2.4074,-2.7912 c 1.0956,-1.1418 1.7199,-2.0354 1.3902,-2.4732 -0.33147,-0.44017 -1.3933,-0.0975 -2.8143,0.63598 0.47352,0.61597 0.93306,1.2356 1.4241,1.8373 z m -1.4241,-1.8373 c -0.73424,-0.9552 -1.4792,-1.909 -2.1701,-2.8972 -4.2155,3.7105 -14.545,9.8893 -13.461,11.448 1.0931,1.5724 10.692,-6.0005 15.631,-8.5503 z m -2.1701,-2.8972 c 1.2616,-1.1105 2.0016,-1.995 1.6954,-2.4732 -0.30668,-0.47888 -1.4699,-0.21096 -3.0178,0.45933 0.43934,0.67076 0.86339,1.3572 1.3224,2.0139 z m -1.3224,-2.0139 c -0.65079,-0.99362 -1.2584,-2.0151 -1.8649,-3.0386 -4.5214,3.3853 -15.165,8.689 -14.207,10.317 0.96144,1.6347 10.909,-5.0426 16.072,-7.2784 z m -1.8649,-3.0386 c 1.3797,-1.033 2.2058,-1.8951 1.9327,-2.4026 -0.27832,-0.5172 -1.4746,-0.27716 -3.1534,0.31799 0.39064,0.70779 0.8091,1.39 1.2207,2.0846 z m -1.2207,-2.0846 c -0.57127,-1.035 -1.1361,-2.0823 -1.6615,-3.1445 -4.8653,3.0333 -15.603,7.4431 -14.784,9.1157 0.82611,1.6854 11.079,-4.0689 16.445,-5.9711 z m -1.6615,-3.1445 c 1.5713,-0.97961 2.5158,-1.8175 2.2718,-2.3672 -0.24489,-0.55171 -1.5267,-0.3938 -3.3229,0.10598 0.34482,0.75649 0.68327,1.5175 1.0511,2.2612 z m -1.0511,-2.2612 c -0.48354,-1.0608 -0.95303,-2.1303 -1.3902,-3.2152 -5.1337,2.6522 -16.011,6.1984 -15.326,7.9144 0.68955,1.7275 11.181,-3.1588 16.716,-4.6991 z m -1.3902,-3.2152 c 1.7112,-0.88408 2.7834,-1.6811 2.577,-2.2612 -0.2085,-0.58608 -1.5727,-0.50009 -3.4925,-0.10599 0.29147,0.79692 0.59924,1.5824 0.91551,2.3673 z m -0.91551,-2.3672 c -0.39903,-1.091 -0.76744,-2.1733 -1.1189,-3.2859 -5.3762,2.246 -16.28,4.9668 -15.733,6.7131 0.55068,1.7567 11.17,-2.2608 16.852,-3.4272 z m -1.1189,-3.2859 c 1.8591,-0.77667 3.0483,-1.5093 2.8821,-2.1199 -0.16681,-0.6128 -1.5834,-0.61914 -3.5942,-0.35333 0.23138,0.82592 0.45462,1.6584 0.71206,2.4732 z m -0.71206,-2.4732 c -0.30771,-1.0984 -0.62096,-2.2043 -0.8816,-3.3212 -5.5698,1.8225 -16.448,3.6713 -16.038,5.4411 0.41259,1.7802 11.134,-1.355 16.92,-2.1199 z m -0.8816,-3.3212 c 1.9798,-0.64782 3.3091,-1.3092 3.1873,-1.9433 -0.12275,-0.63917 -1.6227,-0.72941 -3.7298,-0.60065 0.16909,0.85159 0.34612,1.7023 0.54251,2.5439 z m -0.54251,-2.5439 c -0.22064,-1.1109 -0.43708,-2.2296 -0.61034,-3.3566 -5.7258,1.3834 -16.548,2.3861 -16.276,4.1692 0.27365,1.7905 11.022,-0.45432 16.886,-0.81265 z m -0.61034,-3.3566 c 2.0898,-0.50489 3.5332,-1.0772 3.4586,-1.7313 -0.0751,-0.6588 -1.6221,-0.86008 -3.7976,-0.88329 0.10439,0.8714 0.20647,1.752 0.33909,2.6146 z m -0.33909,-2.6146 c -0.13601,-1.136 -0.25207,-2.277 -0.33907,-3.4272 -5.8417,0.93312 -16.547,1.1809 -16.411,2.9679 0.13586,1.7937 10.848,0.39635 16.75,0.45931 z m -0.33907,-3.4272 c 2.1836,-0.34879 3.755,-0.77927 3.7298,-1.4486 -0.0253,-0.67583 -1.6611,-0.98143 -3.8994,-1.166 0.0372,0.87766 0.10375,1.7445 0.16954,2.6146 z m -0.16954,-2.6146 c -0.0477,-1.1242 -0.0678,-2.2561 -0.0678,-3.3919 0,-0.0119 -10e-6,-0.0236 0,-0.0353 -5.9223,0.47201 -16.478,-0.0832 -16.479,1.6959 -8.9e-4,1.7855 10.638,1.2442 16.547,1.7313 z m -0.0678,-3.4272 c 2.2726,-0.18114 3.9409,-0.48377 3.9672,-1.166 0.0265,-0.68547 -1.6298,-1.1294 -3.8994,-1.484 -0.0296,0.88035 -0.0674,1.7626 -0.0678,2.6499 z m 0.0678,-2.6499 c 0.0382,-1.1379 0.0833,-2.2665 0.16954,-3.3919 -5.9622,0.006 -16.276,-1.3026 -16.411,0.45932 -0.13586,1.7707 10.367,2.0148 16.242,2.9326 z m 0.16954,-3.3919 c 2.3444,-0.002 4.0912,-0.19302 4.1706,-0.88331 0.0804,-0.69853 -1.6056,-1.267 -3.9333,-1.8019 -0.0989,0.89073 -0.16851,1.7859 -0.23735,2.6852 z m 0.23735,-2.6852 c 0.12323,-1.1102 0.27181,-2.2252 0.44081,-3.3212 -5.9649,-0.46416 -16.009,-2.5106 -16.276,-0.77731 -0.26748,1.7384 10.013,2.7605 15.835,4.0985 z m 0.44081,-3.3212 c 2.4094,0.18747 4.2059,0.13026 4.3402,-0.56533 0.13501,-0.69948 -1.5365,-1.402 -3.8655,-2.1199 -0.17091,0.89801 -0.33464,1.7769 -0.4747,2.6852 z m 0.4747,-2.6852 c 0.21056,-1.1063 0.45566,-2.1963 0.71205,-3.2859 -5.9236,-0.92755 -15.677,-3.6774 -16.072,-1.9786 -0.39678,1.7044 9.6396,3.5011 15.36,5.2645 z m 0.71205,-3.2859 c 2.4391,0.38193 4.2865,0.48088 4.4758,-0.212 0.19013,-0.69578 -1.4841,-1.5344 -3.7976,-2.4379 -0.2387,0.87603 -0.46925,1.7622 -0.67815,2.6499 z m 0.67815,-2.6499 c 0.29398,-1.0789 0.61116,-2.155 0.94942,-3.2152 -5.8439,-1.385 -15.246,-4.8357 -15.767,-3.1799 -0.5226,1.6613 9.2322,4.2138 14.818,6.3951 z m 0.94942,-3.2152 c 2.4479,0.58019 4.3331,0.82624 4.5775,0.14134 0.24631,-0.69023 -1.3992,-1.6956 -3.6959,-2.7912 -0.31304,0.87262 -0.59881,1.7636 -0.88158,2.6499 z m 0.88158,-2.6499 c 0.37305,-1.0398 0.80562,-2.0545 1.2207,-3.0739 -5.7264,-1.832 -14.785,-5.916 -15.428,-4.3105 -0.64312,1.6066 8.7846,4.7974 14.207,7.3844 z m 1.2207,-3.0739 c 2.431,0.77776 4.3128,1.1655 4.6114,0.49465 0.30389,-0.68278 -1.2484,-1.8125 -3.5264,-3.1092 -0.38645,0.85916 -0.72884,1.7397 -1.085,2.6146 z m 1.085,-2.6146 c 0.44835,-0.99676 0.90209,-1.9932 1.3902,-2.9679 -5.5762,-2.2787 -14.162,-6.9465 -14.919,-5.4058 -0.75713,1.5408 8.2969,5.3954 13.529,8.3737 z m 1.3902,-2.9679 c 2.4286,0.99248 4.3242,1.5768 4.6792,0.91865 0.35975,-0.66692 -1.1349,-1.9367 -3.3568,-3.4272 -0.45201,0.82707 -0.89953,1.6642 -1.3224,2.5086 z m 1.3224,-2.5086 c 0.52611,-0.96272 1.0968,-1.8882 1.6615,-2.8266 -5.3875,-2.6956 -13.574,-7.9783 -14.445,-6.5011 -0.86692,1.4707 7.7781,5.9702 12.783,9.3277 z m 1.6615,-2.8266 c 2.3576,1.1796 4.24,1.9036 4.6453,1.272 0.41352,-0.64434 -1.016,-2.0342 -3.1534,-3.7099 -0.5228,0.80172 -0.99783,1.6168 -1.4919,2.4379 z m 1.4919,-2.4379 c 0.58841,-0.90229 1.2071,-1.7731 1.831,-2.6499 -5.1671,-3.1155 -12.829,-8.9912 -13.8,-7.5964 -0.97189,1.395 7.2238,6.5259 11.969,10.246 z m 1.831,-2.6499 c 2.3284,1.4039 4.15,2.2368 4.6114,1.6253 0.46424,-0.61523 -0.85705,-2.145 -2.8822,-3.9925 -0.59327,0.77231 -1.1643,1.5732 -1.7293,2.3672 z m 1.7293,-2.3672 c 0.65455,-0.85207 1.3125,-1.7192 2.0005,-2.5439 -4.9135,-3.507 -12.053,-9.8258 -13.122,-8.515 -1.0721,1.3141 6.6664,6.9944 11.122,11.059 z m 2.0005,-2.5439 c 2.2515,1.607 4.0311,2.631 4.5436,2.0493 0.51261,-0.58185 -0.74775,-2.1941 -2.6448,-4.2045 -0.64071,0.71422 -1.2841,1.4184 -1.8988,2.1552 z m 1.8988,-2.1552 c 0.72818,-0.81177 1.4771,-1.5856 2.2379,-2.3672 -4.6328,-3.8668 -11.278,-10.696 -12.444,-9.469 -1.1659,1.2265 6.0672,7.4499 10.206,11.836 z m 2.2379,-2.3672 c 2.1233,1.7722 3.8526,2.9442 4.408,2.4026 0.55839,-0.54459 -0.58458,-2.288 -2.3396,-4.4518 -0.70188,0.67076 -1.3915,1.3538 -2.0684,2.0492 z m 2.0684,-2.0492 c 0.77956,-0.74505 1.5641,-1.5122 2.3735,-2.2259 -4.3214,-4.2144 -10.381,-11.446 -11.63,-10.317 -1.2526,1.1324 5.4579,7.8592 9.2567,12.543 z m 2.3735,-2.2259 c 2.0136,1.9637 3.6367,3.2952 4.2384,2.7912 0.60355,-0.50546 -0.42429,-2.3451 -2.0344,-4.6638 -0.74567,0.6114 -1.4814,1.2354 -2.204,1.8726 z m 2.204,-1.8726 c 0.83882,-0.68775 1.7099,-1.3244 2.577,-1.9786 -3.9886,-4.5274 -9.4849,-12.197 -10.816,-11.165 -1.3296,1.0306 4.7994,8.1895 8.2395,13.144 z m 2.577,-1.9786 c 1.8584,2.1095 3.3967,3.5311 4.035,3.0739 0.64018,-0.4586 -0.26628,-2.3368 -1.6954,-4.7698 -0.78983,0.55279 -1.5712,1.1161 -2.3396,1.6959 z m 2.3396,-1.6959 c 0.88881,-0.62204 1.7976,-1.2151 2.7126,-1.8019 -3.6314,-4.817 -8.4969,-12.87 -9.901,-11.942 -1.4042,0.92769 4.1347,8.5455 7.1884,13.744 z m 2.7126,-1.8019 c 1.6999,2.2549 3.1243,3.8011 3.7976,3.3919 0.67804,-0.41204 -0.066,-2.4219 -1.3224,-4.9818 -0.84574,0.49984 -1.65,1.0606 -2.4752,1.5899 z m 2.4752,-1.5899 c 0.93048,-0.54994 1.86,-1.0766 2.8143,-1.59 -3.2494,-5.0826 -7.4532,-13.359 -8.9177,-12.543 -1.4657,0.8166 3.4471,8.7203 6.1034,14.133 z m 2.8143,-1.59 c 1.541,2.4103 2.8855,4.1057 3.5942,3.7452 0.70995,-0.36113 0.0818,-2.427 -0.98332,-5.0878 -0.8736,0.42994 -1.7557,0.88261 -2.6109,1.3426 z m 2.6109,-1.3426 c 0.95769,-0.47132 1.9371,-0.90835 2.916,-1.3426 -2.8513,-5.3146 -6.4146,-13.881 -7.9344,-13.179 -1.521,0.70282 2.7779,8.9251 5.0183,14.521 z m 2.916,-1.3426 c 1.3593,2.5336 2.5862,4.3351 3.3229,4.0279 0.73458,-0.30637 0.28165,-2.4294 -0.57643,-5.1585 -0.92212,0.36895 -1.8418,0.72928 -2.7465,1.1306 z m 2.7465,-1.1306 c 0.99948,-0.39989 1.9988,-0.80503 3.0178,-1.166 -2.438,-5.5147 -5.3495,-14.294 -6.9171,-13.709 -1.5709,0.58664 2.0918,9.1258 3.8994,14.875 z m 3.0178,-1.166 c 1.1649,2.635 2.2247,4.5265 2.9839,4.2752 0.75865,-0.25116 0.44999,-2.3923 -0.20345,-5.1938 -0.93683,0.29382 -1.8588,0.5922 -2.7804,0.91863 z m 2.7804,-0.91863 c 1.0176,-0.31914 2.0509,-0.63882 3.0856,-0.91863 -2.0115,-5.6826 -4.2248,-14.669 -5.8321,-14.203 -1.6081,0.46591 1.3777,9.2536 2.7465,15.122 z m 3.0856,-0.91863 c 0.96174,2.7169 1.9023,4.7161 2.6787,4.5225 0.77794,-0.19393 0.64531,-2.3736 0.20345,-5.2291 -0.96393,0.22308 -1.9319,0.44967 -2.8822,0.70664 z m 2.8822,-0.70664 c 1.0459,-0.24209 2.0922,-0.46999 3.1534,-0.67132 -1.5715,-5.8187 -3.1112,-14.935 -4.747,-14.592 -1.6365,0.3431 0.67219,9.3086 1.5937,15.263 z m 3.1534,-0.67132 c 0.75451,2.7936 1.5146,4.8342 2.3057,4.6992 0.79325,-0.13535 0.83651,-2.3334 0.61034,-5.2292 -0.98783,0.14997 -1.9404,0.34486 -2.916,0.52998 z m 2.916,-0.52998 c 1.0523,-0.15974 2.122,-0.26958 3.1873,-0.38865 -1.1249,-5.9202 -1.9367,-15.129 -3.5942,-14.91 -1.6551,0.21862 -0.0623,9.2914 0.4069,15.299 z m 3.1873,-0.38865 c 0.53989,2.8413 1.1677,4.9155 1.9666,4.8405 0.7955,-0.0747 0.98569,-2.2147 0.98333,-5.0878 -0.98422,0.0741 -1.976,0.13846 -2.95,0.24733 z m 2.95,-0.24733 c 1.0671,-0.0806 2.1429,-0.17305 3.2212,-0.21198 -0.67184,-5.9878 -0.8057,-15.216 -2.4752,-15.122 -1.6728,0.0937 -0.75092,9.3098 -0.74596,15.334 z m 3.2212,-0.21198 c 0.32234,2.8729 0.7576,4.9965 1.5598,4.9818 0.8022,-0.0147 1.1745,-2.1695 1.3902,-5.0525 -0.99057,3.8e-4 -1.9681,0.0351 -2.95,0.0707 z"
             id="path4275-0" />
        </g>
        <path
           inkscape:connector-curvature="0"
           style="fill:url(#linearGradient3542);stroke-width:0"
           d="m 84.094,944.52 c -22.306,0 -40.388,17.943 -40.388,40.067 0,22.123 18.082,40.047 40.388,40.047 22.306,0 40.388,-17.924 40.388,-40.047 0,-22.123 -18.082,-40.067 -40.388,-40.067 z m 0,2.9728 c 20.651,0 37.397,16.612 37.397,37.094 0,20.481 -16.746,37.075 -37.397,37.075 -20.651,0 -37.397,-16.593 -37.397,-37.075 0,-20.482 16.746,-37.094 37.397,-37.094 z"
           id="path4297-1" />
        <path
           d="M 25.504,987.37 1.26,990.9 c -16.81,-104.78 142.46,-117.57 140.92,-50 31.247,15.249 62.051,10.964 78.287,-20.203 h 21.718 l -6.0609,-19.698 c 55.161,6.6009 108.75,5.3578 162.63,5.5558 l 9.0914,13.132 c 48.173,-13.472 60.032,-0.79671 75.761,9.0914 l -14.142,2.0203 c -39.099,-30.71 -114.36,40.544 -174.76,51.518 -73.263,13.312 -101.43,4.7764 -149,7.0711 -6.3093,-86.321 -118.3,-81.669 -120.21,-2.0203 z"
           inkscape:connector-curvature="0"
           style="fill:url(#linearGradient3544);stroke-width:0"
           sodipodi:nodetypes="ccccccccccscc"
           id="path4417-3" />
        <ellipse
           transform="matrix(0.44138,0,0,0.48031,384.4,561.23)"
           style="fill:url(#linearGradient2263-2);stroke-width:0"
           id="path4419-1"
           cx="104.55079"
           cy="887.20227"
           rx="73.236061"
           ry="64.144684" />
        <ellipse
           transform="matrix(0.5251,0,0,0.5251,376.42,530.74)"
           style="fill:#2d2d2d;stroke-width:0;filter:url(#filter4259-4)"
           id="path4421-8"
           cx="104.55079"
           cy="870.02966"
           rx="19.697975"
           ry="18.687822" />
        <ellipse
           transform="matrix(0.34075,0,0,0.34075,431.76,702.7)"
           style="fill:#9eabb0;stroke-width:0"
           id="path4423-3"
           cx="-2.0203052"
           cy="834.67432"
           rx="19.192898"
           ry="18.687822" />
        <path
           inkscape:connector-curvature="0"
           style="fill:url(#radialGradient5037-8);stroke:#817e7e;stroke-width:1.84657;stroke-linecap:round;stroke-linejoin:round"
           d="m 430.58,954.36 c -18.234,0 -33.015,14.667 -33.015,32.752 0,18.085 14.781,32.736 33.015,32.736 18.234,0 33.015,-14.652 33.015,-32.736 0,-18.085 -14.781,-32.752 -33.015,-32.752 z m 0,2.4301 c 16.88,0 30.569,13.579 30.569,30.322 0,16.742 -13.689,30.306 -30.569,30.306 -16.88,0 -30.569,-13.564 -30.569,-30.306 0,-16.742 13.689,-30.322 30.569,-30.322 z"
           id="path4425-4" />
        <path
           d="m 430.58,952.41 c -19.316,0 -34.975,15.538 -34.975,34.696 0,19.158 15.659,34.68 34.975,34.68 19.316,0 34.975,-15.522 34.975,-34.68 0,-19.158 -15.659,-34.696 -34.975,-34.696 z m 0,2.5744 c 17.883,0 32.384,14.386 32.384,32.122 0,17.736 -14.502,32.106 -32.384,32.106 -17.883,0 -32.384,-14.369 -32.384,-32.106 0,-17.736 14.502,-32.122 32.384,-32.122 z"
           inkscape:connector-curvature="0"
           style="fill:url(#radialGradient5034-1);stroke:#817e7e;stroke-width:1.95618;stroke-linecap:round;stroke-linejoin:round"
           id="path4427-3" />
        <path
           inkscape:connector-curvature="0"
           style="fill:#666666;stroke:#817e7e;stroke-width:2.09325;stroke-linecap:round;stroke-linejoin:round"
           d="m 430.58,949.98 c -20.67,0 -37.425,16.627 -37.425,37.127 0,20.501 16.756,37.11 37.425,37.11 20.67,0 37.425,-16.609 37.425,-37.11 0,-20.5 -16.756,-37.127 -37.425,-37.127 z m 0,2.7547 c 19.136,0 34.653,15.393 34.653,34.372 0,18.979 -15.518,34.355 -34.653,34.355 -19.136,0 -34.653,-15.376 -34.653,-34.355 0,-18.979 15.518,-34.372 34.653,-34.372 z"
           id="path4429-4" />
        <g
           transform="matrix(-0.49937,0,0,0.49632,320.89,627.71)"
           style="stroke:#333333;stroke-width:3.9243"
           id="g4431-0">
          <path
             d="m -214.15,715.98 -14.142,-51.518"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path4433-5" />
          <path
             d="m -211.12,780.63 -17.173,-48.487"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path4435-1" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -212.77,718.6 -0.32647,-53.423"
             id="path4437-4" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -226.57,781.83 -4.038,-51.28"
             id="path4439-4" />
          <path
             d="m -211.8,721.08 13.511,-51.687"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path4441-9" />
          <path
             d="m -241.5,778.58 9.3718,-50.578"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path4443-7" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -211.33,723.27 26.429,-46.429"
             id="path4445-7" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -254.9,771.13 22.143,-46.429"
             id="path4447-5" />
          <path
             d="m -211.43,725.52 37.545,-38.006"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path4449-7" />
          <path
             d="m -265.91,760.47 33.405,-39.116"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path4451-2" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -212.12,727.66 46.102,-26.994"
             id="path4453-4" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -273.78,747.32 42.391,-29.137"
             id="path4455-3" />
          <path
             d="m -213.33,729.54 51.52,-14.14"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path4457-7" />
          <path
             d="m -277.98,732.57 48.487,-17.173"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path4459-4" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -214.99,731.05 53.423,-0.32649"
             id="path4461-6" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -278.22,717.25 51.28,-4.038"
             id="path4463-1" />
          <path
             d="m -217.47,732.02 51.687,13.511"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path4465-2" />
          <path
             d="m -274.97,702.32 50.578,9.3718"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path4467-0" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -219.66,732.5 46.429,26.429"
             id="path4469-3" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -267.52,688.93 46.429,22.143"
             id="path4471-2" />
          <path
             d="m -221.91,732.39 38.006,37.545"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path4473-1" />
          <path
             d="m -256.86,677.92 39.116,33.405"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path4475-6" />
        </g>
        <g
           transform="matrix(0.47794,0,0,0.48362,535.03,636.13)"
           style="stroke:#4d3636;stroke-width:3.92482"
           id="g4477-2">
          <path
             d="m -214.15,715.98 -14.142,-51.518"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path4479-6" />
          <path
             d="m -211.12,780.63 -17.173,-48.487"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path4481-8" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -212.77,718.6 -0.32647,-53.423"
             id="path4483-6" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -226.57,781.83 -4.038,-51.28"
             id="path4485-1" />
          <path
             d="m -211.8,721.08 13.511,-51.687"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path4487-2" />
          <path
             d="m -241.5,778.58 9.3718,-50.578"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path4489-8" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -211.33,723.27 26.429,-46.429"
             id="path4491-0" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -254.9,771.13 22.143,-46.429"
             id="path4493-2" />
          <path
             d="m -211.43,725.52 37.545,-38.006"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path4495-1" />
          <path
             d="m -265.91,760.47 33.405,-39.116"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path4497-8" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -212.12,727.66 46.102,-26.994"
             id="path4499-8" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -273.78,747.32 42.391,-29.137"
             id="path4501-7" />
          <path
             d="m -213.33,729.54 51.52,-14.14"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path4503-0" />
          <path
             d="m -277.98,732.57 48.487,-17.173"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path4505-4" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -214.99,731.05 53.423,-0.32649"
             id="path4507-0" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -278.22,717.25 51.28,-4.038"
             id="path4509-0" />
          <path
             d="m -217.47,732.02 51.687,13.511"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path4511-6" />
          <path
             d="m -274.97,702.32 50.578,9.3718"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path4513-0" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -219.66,732.5 46.429,26.429"
             id="path4515-2" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -267.52,688.93 46.429,22.143"
             id="path4517-8" />
          <path
             d="m -221.91,732.39 38.006,37.545"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path4519-0" />
          <path
             d="m -256.86,677.92 39.116,33.405"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path4521-6" />
        </g>
        <ellipse
           transform="matrix(0.2962,0,0,0.2962,431.67,739.89)"
           style="fill:url(#linearGradient2265-0);stroke:#333333;stroke-width:3.9243;stroke-linecap:round;stroke-linejoin:round"
           id="path4523-8"
           cx="-2.0203052"
           cy="834.67432"
           rx="19.192898"
           ry="18.687822" />
        <g
           transform="matrix(0.5599,0,0,0.55238,568.54,475.62)"
           style="stroke:#9f9999;stroke-width:0.470445"
           id="g4525-7">
          <path
             d="m -245.98,826.55 c -55.038,0 -99.649,44.13 -99.649,98.548 0,54.418 44.611,98.519 99.649,98.519 55.038,0 99.677,-44.102 99.677,-98.519 0,-54.418 -44.639,-98.548 -99.677,-98.548 z m 0,26.945 c 39.99,0 72.414,32.064 72.414,71.603 0,39.539 -32.424,71.575 -72.414,71.575 -39.99,0 -72.387,-32.035 -72.387,-71.575 0,-39.539 32.397,-71.603 72.387,-71.603 z"
             inkscape:connector-curvature="0"
             style="fill:url(#radialGradient4293-6);stroke:#9f9999;stroke-width:0.470445;stroke-linecap:round;stroke-linejoin:round"
             id="path4527-5" />
          <path
             d="m -244.61,826.11 c -1.6718,-0.0324 -1.4493,9.1844 -1.8988,15.193 h 0.0339 c 1.0817,0 2.1499,0.029 3.2212,0.0707 -0.21479,-6.0212 0.31566,-15.231 -1.3563,-15.263 z m 1.3563,15.263 c 0.10313,2.8885 0.35196,5.0421 1.1529,5.0878 0.80081,0.0456 1.329,-2.0538 1.7632,-4.9112 -0.96597,-0.072 -1.9409,-0.13863 -2.9161,-0.17665 z m 2.9161,0.17665 c 1.08,0.0806 2.1536,0.19581 3.2212,0.318 0.24291,-6.0201 1.5296,-15.176 -0.13562,-15.334 -1.6648,-0.15802 -2.1804,9.0588 -3.0856,15.016 z m 3.2212,0.318 c -0.11639,2.8848 -0.0146,5.0527 0.77989,5.1585 0.79573,0.10599 1.4855,-1.985 2.1362,-4.8052 -0.97056,-0.14693 -1.9344,-0.24097 -2.9161,-0.35331 z m 2.9161,0.35331 c 1.0702,0.16202 2.1315,0.3621 3.1873,0.56531 0.70049,-5.9846 2.6651,-15.016 1.0172,-15.299 -1.6474,-0.2827 -2.8498,8.861 -4.2045,14.733 z m 3.1873,0.56531 c -0.33604,2.871 -0.37794,5.0638 0.40689,5.2292 0.78191,0.16473 1.6161,-1.849 2.4752,-4.5932 -0.95558,-0.22034 -1.9138,-0.44961 -2.8821,-0.63599 z m 2.8821,0.63599 c 1.0556,0.2434 2.1142,0.49329 3.1534,0.77731 1.1507,-5.9147 3.7941,-14.786 2.1701,-15.193 -1.6246,-0.40672 -3.5227,8.6637 -5.3235,14.416 z m 3.1534,0.77731 c -0.54956,2.8248 -0.76767,5.0059 0,5.2291 0.7684,0.22344 1.7471,-1.7381 2.8143,-4.4165 -0.92615,-0.2895 -1.874,-0.55565 -2.8143,-0.81262 z m 2.8143,0.81262 c 1.0302,0.32204 2.0399,0.66323 3.0517,1.0246 1.597,-5.8112 4.9114,-14.453 3.3229,-14.981 -1.5878,-0.52739 -4.143,8.3555 -6.3746,13.956 z m 3.0517,1.0246 c -0.76267,2.7752 -1.1215,4.9135 -0.37299,5.1938 0.74738,0.27995 1.8894,-1.5534 3.1534,-4.1338 -0.91975,-0.36741 -1.8442,-0.72553 -2.7804,-1.06 z m 2.7804,1.06 c 1.007,0.40228 1.9976,0.83106 2.9838,1.272 2.0314,-5.6747 5.988,-14.087 4.4419,-14.733 -1.545,-0.64609 -4.773,8.0462 -7.4257,13.462 z m 2.9838,1.272 c -0.96564,2.6975 -1.5365,4.753 -0.81377,5.0878 0.72165,0.33431 2.0078,-1.3857 3.4586,-3.8512 -0.87151,-0.42808 -1.7557,-0.83918 -2.6448,-1.2366 z m 2.6448,1.2366 c 0.96152,0.4723 1.9429,0.93991 2.8822,1.4486 2.4572,-5.5056 7.0533,-13.584 5.5608,-14.345 -1.4936,-0.76163 -5.3841,7.6979 -8.443,12.896 z m 2.8822,1.4486 c -1.1683,2.6178 -1.8482,4.6642 -1.1529,5.0525 0.69397,0.38753 2.0625,-1.2557 3.6959,-3.6039 -0.8429,-0.49709 -1.6806,-0.98145 -2.543,-1.4486 z m 2.543,1.4486 c 0.93425,0.55097 1.8372,1.1445 2.7465,1.7313 2.8619,-5.3066 8.1152,-13.082 6.6798,-13.956 -1.4317,-0.87197 -5.9809,7.2718 -9.4263,12.225 z m 2.7465,1.7313 c -1.3457,2.4952 -2.1847,4.4396 -1.5258,4.8758 0.65669,0.43479 2.1472,-1.0638 3.9333,-3.2505 -0.79343,-0.55349 -1.5932,-1.0998 -2.4074,-1.6253 z m 2.4074,1.6253 c 0.89266,0.62269 1.7794,1.2508 2.6448,1.9079 3.2542,-5.0768 9.0979,-12.445 7.7309,-13.426 -1.367,-0.98127 -6.559,6.8453 -10.376,11.518 z m 2.6448,1.9079 c -1.5229,2.3758 -2.4867,4.2515 -1.8649,4.7345 0.61993,0.48153 2.2312,-0.89633 4.1706,-2.9326 -0.75602,-0.61873 -1.5268,-1.2104 -2.3057,-1.8019 z m 2.3057,1.8019 c 0.8283,0.67785 1.6415,1.375 2.4414,2.0846 3.6257,-4.819 10.074,-11.741 8.782,-12.826 -1.2902,-1.0828 -7.0611,6.371 -11.223,10.741 z m 2.4414,2.0846 c -1.6829,2.2367 -2.7501,4.0324 -2.1701,4.5578 0.57819,0.52375 2.2344,-0.71318 4.3062,-2.5792 -0.69861,-0.66606 -1.4136,-1.3377 -2.1362,-1.9786 z m 2.1362,1.9786 c 0.78547,0.74887 1.5515,1.5169 2.3057,2.2966 3.9737,-4.5351 10.976,-11.007 9.7654,-12.19 -1.2075,-1.1799 -7.5864,5.8539 -12.071,9.893 z m 2.3057,2.2966 c -1.8212,2.0786 -3.009,3.7478 -2.4752,4.3105 0.53371,0.56256 2.2515,-0.50216 4.4419,-2.1906 -0.64738,-0.7195 -1.2936,-1.4242 -1.9666,-2.1199 z m 1.9666,2.1199 c 0.72923,0.81043 1.4744,1.5983 2.1701,2.4379 4.307,-4.2231 11.764,-10.177 10.647,-11.448 -1.1172,-1.2707 -8.0368,5.3249 -12.817,9.0097 z m 2.1701,2.4379 c -1.974,1.9356 -3.3038,3.4973 -2.8143,4.0985 0.48671,0.59779 2.2832,-0.33317 4.5775,-1.8373 -0.58076,-0.75413 -1.1561,-1.5286 -1.7632,-2.2613 z m 1.7632,2.2613 c 0.66776,0.86706 1.3007,1.7554 1.9327,2.6499 4.6043,-3.8919 12.585,-9.3118 11.562,-10.67 -1.02,-1.3545 -8.4477,4.7114 -13.495,8.0204 z m 1.9327,2.6499 c -2.0735,1.7527 -3.4548,3.1872 -3.0178,3.8159 0.43555,0.62652 2.2772,-0.14489 4.6453,-1.4486 -0.52538,-0.80374 -1.074,-1.5838 -1.6276,-2.3672 z m 1.6276,2.3672 c 0.59415,0.9089 1.1722,1.822 1.7293,2.7559 4.8809,-3.5371 13.329,-8.3514 12.41,-9.787 -0.91817,-1.4338 -8.8546,4.1216 -14.139,7.0311 z m 1.7293,2.7559 c -2.1782,1.5785 -3.6407,2.8776 -3.2551,3.5332 0.38091,0.64765 2.2726,-0.004 4.6792,-1.0953 -0.45464,-0.82804 -0.94092,-1.6279 -1.4241,-2.4379 z m 1.4241,2.4379 c 0.52864,0.96283 1.0368,1.9463 1.5258,2.9326 5.1266,-3.1644 13.967,-7.3615 13.156,-8.8683 -0.81198,-1.5088 -9.1931,3.4488 -14.682,5.9358 z m 1.5258,2.9326 c -2.2573,1.3933 -3.824,2.5741 -3.4925,3.2506 0.32625,0.66563 2.2448,0.17614 4.6792,-0.70664 -0.3857,-0.85292 -0.77189,-1.7072 -1.1868,-2.5439 z m 1.1868,2.5439 c 0.45698,1.0106 0.87345,2.0415 1.2885,3.0739 5.3246,-2.7834 14.604,-6.3333 13.902,-7.9144 -0.69893,-1.5745 -9.5324,2.7887 -15.191,4.8405 z m 1.2885,3.0739 c -2.2592,1.181 -3.9002,2.251 -3.6281,2.9325 0.27248,0.68268 2.1814,0.33166 4.6453,-0.35331 -0.31478,-0.87086 -0.67289,-1.7227 -1.0172,-2.5792 z m 1.0172,2.5792 c 0.37906,1.0487 0.74983,2.1114 1.085,3.1799 5.5142,-2.3782 15.093,-5.2564 14.512,-6.8898 -0.57927,-1.6283 -9.8033,2.0991 -15.597,3.7099 z m 1.085,3.1799 c -2.3205,1.0008 -4.0158,1.9185 -3.7976,2.6146 0.21623,0.68984 2.0966,0.47846 4.5436,0 -0.24103,-0.87328 -0.47582,-1.7535 -0.74595,-2.6146 z m 0.74595,2.6146 c 0.29585,1.0719 0.5627,2.1612 0.81378,3.2506 5.6584,-1.9682 15.581,-4.1096 15.123,-5.7945 -0.45697,-1.6788 -10.047,1.3924 -15.937,2.5439 z m 0.81378,3.2506 c -2.3221,0.80773 -3.9933,1.5986 -3.8316,2.2966 0.16075,0.69353 1.9891,0.66848 4.408,0.38865 -0.17493,-0.90294 -0.37089,-1.7934 -0.57644,-2.6852 z m 0.57644,2.6852 c 0.21475,1.1085 0.44269,2.1969 0.61033,3.3212 5.7816,-1.5431 15.928,-2.9788 15.597,-4.6992 -0.33009,-1.719 -10.26,0.68988 -16.208,1.3779 z m 0.61033,3.3212 c -2.3447,0.62579 -4.0406,1.241 -3.9333,1.9433 0.1061,0.69399 1.8912,0.83119 4.2724,0.74197 -0.10228,-0.89597 -0.20689,-1.7986 -0.33908,-2.6852 z m 0.33908,2.6852 c 0.12708,1.1128 0.22506,2.2303 0.30516,3.3565 5.8559,-1.1212 16.239,-1.7428 16.038,-3.4979 -0.19956,-1.7486 -10.374,-0.0823 -16.343,0.14133 z m 0.30516,3.3565 c -2.3058,0.44149 -3.9519,0.96708 -3.8994,1.6606 0.0521,0.68644 1.7269,0.92912 4.035,1.0246 -0.0335,-0.89992 -0.0722,-1.7932 -0.13562,-2.6852 z m 0.13562,2.6852 c 0.0365,0.98504 0.0678,1.974 0.0678,2.9679 0,0.14188 7.7e-4,0.28227 0,0.42397 5.9003,-0.69454 16.444,-0.51878 16.377,-2.2966 -0.0666,-1.7722 -10.499,-0.84927 -16.445,-1.0953 z m 0.0678,3.3919 c -2.2644,0.26655 -3.899,0.65985 -3.8994,1.3426 -3.3e-4,0.67641 1.6013,1.074 3.8315,1.3426 0.0346,-0.89088 0.063,-1.7871 0.0678,-2.6852 z m -0.0678,2.6852 c -0.0443,1.139 -0.11112,2.2656 -0.20344,3.3919 5.9106,-0.26845 16.58,0.69526 16.649,-1.0953 0.0688,-1.7841 -10.559,-1.5875 -16.445,-2.2966 z m -0.20344,3.3919 c -2.2098,0.10047 -3.8142,0.35665 -3.8655,1.0246 -0.0509,0.6623 1.4556,1.1595 3.5942,1.59 0.1004,-0.86818 0.19941,-1.7381 0.27126,-2.6146 z m -0.27126,2.6146 c -0.13102,1.1332 -0.26209,2.2737 -0.44079,3.3919 5.8937,0.15957 16.612,1.9668 16.818,0.17666 0.20562,-1.7859 -10.59,-2.4035 -16.377,-3.5685 z m -0.44079,3.3919 c -2.1584,-0.0585 -3.6973,0.0897 -3.7976,0.74197 -0.0992,0.6442 1.2887,1.2574 3.3229,1.8373 0.16616,-0.8552 0.3365,-1.7145 0.4747,-2.5792 z m -0.4747,2.5792 c -0.21901,1.1272 -0.47933,2.2468 -0.74598,3.3565 5.8254,0.5657 16.575,3.2718 16.92,1.484 0.34315,-1.7776 -10.524,-3.23 -16.174,-4.8405 z m -0.74598,3.3565 c -2.0524,-0.19928 -3.5166,-0.16549 -3.662,0.45932 -0.14419,0.61937 1.1149,1.3021 3.0178,2.0139 0.22518,-0.81852 0.44512,-1.6444 0.64424,-2.4733 z m -0.64424,2.4733 c -0.3099,1.1266 -0.62513,2.2504 -0.98333,3.3566 5.738,0.97369 16.403,4.5248 16.886,2.7559 0.48151,-1.7621 -10.437,-4.0678 -15.903,-6.1124 z m -0.98333,3.3566 c -1.9679,-0.33395 -3.3715,-0.38801 -3.5603,0.212 -0.18781,0.597 0.95379,1.3538 2.7465,2.1906 0.28785,-0.79903 0.55129,-1.592 0.81376,-2.4026 z m -0.81376,2.4026 c -0.39653,1.1007 -0.84488,2.1728 -1.2885,3.2505 5.6095,1.359 16.196,5.8068 16.818,4.0632 0.6175,-1.7304 -10.267,-4.8575 -15.53,-7.3137 z m -1.2885,3.2505 c -1.8542,-0.44918 -3.1633,-0.56817 -3.3907,0 -0.22554,0.56342 0.775,1.3646 2.4074,2.2966 0.34094,-0.75878 0.66594,-1.5255 0.98331,-2.2966 z m -0.98331,2.2966 c -0.48023,1.0688 -0.96605,2.1366 -1.4919,3.1799 5.4619,1.7408 15.823,7.0738 16.581,5.3705 0.75507,-1.6965 -10.094,-5.6991 -15.089,-8.5504 z m -1.4919,3.1799 c -1.7559,-0.55966 -3.0246,-0.75004 -3.289,-0.21199 -0.26115,0.53141 0.64728,1.3846 2.1362,2.4026 0.39316,-0.72281 0.78189,-1.4546 1.1528,-2.1906 z m -1.1528,2.1906 c -0.56636,1.0412 -1.1519,2.0611 -1.7632,3.0739 5.2747,2.0899 15.348,8.3349 16.242,6.6778 0.88897,-1.648 -9.7709,-6.533 -14.478,-9.7517 z m -1.7632,3.0739 c -1.6298,-0.64574 -2.7901,-0.88991 -3.0856,-0.38867 -0.28973,0.49152 0.48355,1.3674 1.7971,2.4379 0.43591,-0.67499 0.87284,-1.3606 1.2885,-2.0493 z m -1.2885,2.0493 c -0.64982,1.0062 -1.3407,1.9932 -2.0344,2.9679 5.0502,2.3982 14.839,9.5542 15.869,7.9497 1.0234,-1.5947 -9.4726,-7.3629 -13.834,-10.918 z m -2.0344,2.9679 c -1.4809,-0.70324 -2.5625,-1.0241 -2.8821,-0.56532 -0.31749,0.45569 0.32534,1.3866 1.4919,2.5086 0.48225,-0.63628 0.92702,-1.2925 1.3902,-1.9433 z m -1.3902,1.9433 c -0.73365,0.96799 -1.4613,1.9288 -2.2379,2.8619 4.8324,2.7318 14.171,10.681 15.326,9.151 1.151,-1.5254 -9.0752,-8.1532 -13.088,-12.013 z m -2.2379,2.8619 c -1.3847,-0.78279 -2.3992,-1.1677 -2.7465,-0.74196 -0.34026,0.41706 0.20736,1.3212 1.2207,2.4732 0.50675,-0.57462 1.0357,-1.1423 1.5258,-1.7313 z m -1.5258,1.7313 c -0.8058,0.91369 -1.6286,1.809 -2.4753,2.6852 4.5693,2.9959 13.4,11.842 14.682,10.388 1.2751,-1.4474 -8.5759,-8.9449 -12.207,-13.073 z m -2.4753,2.6852 c -1.2454,-0.81658 -2.2128,-1.1958 -2.577,-0.81266 -0.36197,0.38082 0.10515,1.2569 0.98333,2.4379 0.54491,-0.53281 1.0642,-1.0773 1.5936,-1.6252 z m -1.5936,1.6252 c -0.89166,0.87188 -1.8146,1.7137 -2.7465,2.5439 4.3018,3.261 12.605,12.883 14.004,11.518 1.3908,-1.3564 -8.0106,-9.6962 -11.257,-14.062 z m -2.7465,2.5439 c -1.1313,-0.85755 -1.9922,-1.2987 -2.3735,-0.95396 -0.37389,0.33803 -0.007,1.2005 0.71206,2.3672 0.5557,-0.46874 1.119,-0.93001 1.6615,-1.4133 z m -1.6615,1.4133 c -0.96131,0.8109 -1.9503,1.6011 -2.95,2.3673 4.0103,3.4734 11.642,13.917 13.156,12.649 1.5065,-1.2618 -7.4018,-10.468 -10.206,-15.016 z m -2.95,2.3673 c -1.0041,-0.86972 -1.8131,-1.3276 -2.204,-1.0246 -0.39217,0.30398 -0.13183,1.1799 0.4747,2.3672 0.58957,-0.42707 1.1521,-0.90024 1.7293,-1.3426 z m -1.7293,1.3426 c -1.018,0.73742 -2.0659,1.4294 -3.1195,2.1199 3.7236,3.7177 10.628,14.82901 12.241,13.67401 1.6047,-1.14961 -6.7086,-11.07101 -9.1211,-15.79301 z m -3.1195,2.1199 c -0.91212,-0.91066 -1.6288,-1.3986 -2.0344,-1.1306 -0.40201,0.26557 -0.24215,1.1236 0.23735,2.2966 0.61057,-0.37726 1.1976,-0.77308 1.7971,-1.166 z m -1.7971,1.166 c -1.0815,0.66828 -2.1739,1.2898 -3.289,1.908 3.4197,3.9218 9.4836,15.70001 11.189,14.66301 1.6999,-1.0332 -5.9212,-11.72901 -7.9005,-16.57101 z m -3.289,1.908 c -0.81353,-0.93297 -1.4839,-1.3971 -1.8988,-1.166 -0.40945,0.22813 -0.32782,1.0025 0.0339,2.1552 0.62448,-0.32506 1.2503,-0.64861 1.8649,-0.98928 z m -1.8649,0.98928 c -1.1324,0.5895 -2.295,1.159 -3.4586,1.6959 3.1054,4.098 8.3482,16.45703 10.138,15.54603 1.7854,-0.9081 -5.1342,-12.31703 -6.6798,-17.24203 z m -3.4586,1.6959 c -0.71663,-0.94569 -1.3424,-1.431 -1.7632,-1.2366 -0.4161,0.19227 -0.39079,0.95079 -0.13564,2.0846 0.63849,-0.27483 1.2691,-0.55735 1.8988,-0.84797 z m -1.8988,0.84797 c -1.1935,0.51379 -2.4053,0.99118 -3.6281,1.4486 2.7831,4.2288 7.118,17.10196 8.9855,16.32296 1.859,-0.7752 -4.2363,-12.79096 -5.3574,-17.77196 z m -3.6281,1.4486 c -0.61783,-0.93876 -1.1389,-1.4644 -1.5597,-1.3073 -0.41946,0.15665 -0.49275,0.90868 -0.33907,2.0139 0.639,-0.22088 1.2673,-0.47041 1.8988,-0.70666 z m -1.8988,0.70666 c -1.2181,0.42105 -2.4519,0.80193 -3.6959,1.166 2.4563,4.3723 5.8017,17.633 7.7309,16.995 1.9228,-0.6366 -3.3399,-13.162 -4.035,-18.161 z m -3.6959,1.166 c -0.53312,-0.94899 -1.0343,-1.5007 -1.458,-1.3779 -0.42072,0.12189 -0.56788,0.87078 -0.5086,1.9432 0.65902,-0.17484 1.3144,-0.37444 1.9666,-0.56531 z m -1.9666,0.56531 c -1.2613,0.33466 -2.5466,0.60852 -3.8316,0.88327 2.127,4.46142 4.4921,18.09002 6.4763,17.59502 1.9752,-0.4923 -2.3693,-13.495 -2.6448,-18.47902 z m -3.8316,0.88327 c -0.4459,-0.93525 -0.86884,-1.5012 -1.2885,-1.4132 -0.41764,0.0875 -0.61427,0.77556 -0.64424,1.802 0.64652,-0.12198 1.2918,-0.2516 1.9327,-0.38877 z m -1.9327,0.38877 c -1.2908,0.24328 -2.5875,0.45426 -3.8994,0.63588 1.788,4.57657 3.0992,18.43497 5.12,18.08997 2.0182,-0.3443 -1.3644,-13.802 -1.2207,-18.72597 z m -3.8994,0.63588 c -0.36984,-0.94666 -0.76634,-1.5041 -1.1868,-1.4486 -0.41771,0.0554 -0.63631,0.69735 -0.74598,1.696 0.65124,-0.0742 1.2863,-0.15797 1.9327,-0.24745 z m -1.9327,0.24745 c -1.3121,0.14946 -2.6367,0.26672 -3.9672,0.35312 1.4435,4.6673 1.7179,18.636 3.7638,18.444 2.044,-0.1922 -0.33086,-13.932 0.20344,-18.79697 z m -3.9672,0.35312 c -0.29468,-0.95279 -0.63225,-1.54277 -1.0511,-1.51917 -0.42054,0.0236 -0.69635,0.67387 -0.8816,1.66047 0.64621,-0.027 1.2906,-0.1 1.9327,-0.1413 z m -1.9327,0.1413 c -1.1382,0.048 -2.2747,0.07 -3.4246,0.07 h -0.54254 c 1.0797,4.776 0.28709,18.658 2.3396,18.62 2.0529,-0.039 0.72451,-13.881 1.6275,-18.691 z m -3.9672,0.07 c -0.22172,-0.98073 -0.52789,-1.61707 -0.9494,-1.62517 -0.41901,-0.01 -0.72426,0.59349 -0.98333,1.55457 0.64452,0.02 1.2845,0.066 1.9327,0.07 z m -1.9327,-0.07 c -1.3401,-0.039 -2.6782,-0.1077 -4.001,-0.212 0.7154,4.8395 -1.1347,18.644 0.91548,18.761 2.052,0.1174 1.815,-13.835 3.0856,-18.549 z m -4.001,-0.212 c -0.14666,-0.99183 -0.39476,-1.62077 -0.8138,-1.66047 -0.41813,-0.0401 -0.78695,0.5411 -1.1189,1.484 0.63854,0.0653 1.2898,0.12617 1.9327,0.17657 z m -1.9327,-0.17651 c -1.3174,-0.13501 -2.6355,-0.2623 -3.9333,-0.45936 0.30978,4.94517 -2.5333,18.45697 -0.50862,18.72597 2.0363,0.271 2.8181,-13.655 4.4419,-18.26697 z m -3.9333,-0.45936 c -0.0654,-1.0452 -0.25269,-1.7289 -0.67816,-1.8019 -0.42202,-0.0729 -0.84416,0.49994 -1.2546,1.4486 0.64952,0.11498 1.2779,0.25384 1.9327,0.35332 z m -1.9327,-0.35332 c -1.3035,-0.23054 -2.5846,-0.48548 -3.8655,-0.77732 -0.0719,4.96181 -3.9299,18.19901 -1.9327,18.62001 2.0018,0.4216 3.8373,-13.31 5.7982,-17.84301 z m -3.8655,-0.77732 c 0.0151,-1.0526 -0.12255,-1.7674 -0.54251,-1.8726 -0.42702,-0.10697 -0.92774,0.44942 -1.4241,1.4133 0.64949,0.16508 1.3109,0.30991 1.9666,0.45927 z m -1.9666,-0.45927 c -1.2619,-0.3208 -2.5266,-0.64574 -3.7637,-1.0246 -0.50939,5.02108 -5.268,17.73598 -3.3229,18.30198 1.9525,0.568 4.7935,-12.824 7.0867,-17.27698 z m -3.7637,-1.0246 c 0.11263,-1.1101 0.052,-1.8374 -0.37298,-1.9786 -0.42212,-0.14022 -0.95028,0.3647 -1.5258,1.3073 0.6366,0.21255 1.2552,0.47418 1.8988,0.6713 z m -1.8988,-0.6713 c -1.2471,-0.4164 -2.4771,-0.86833 -3.6959,-1.3426 -0.89058,4.9692 -6.6094,17.30898 -4.7131,18.01898 1.8993,0.7115 5.7842,-12.378 8.4091,-16.67698 z m -3.6959,-1.3426 c 0.19703,-1.0994 0.20981,-1.8411 -0.20345,-2.0139 -0.42771,-0.17888 -1.0142,0.33595 -1.6954,1.3073 0.62125,0.25989 1.2697,0.46183 1.8988,0.70664 z m -1.8988,-0.70664 c -1.1842,-0.4954 -2.3381,-1.041 -3.4925,-1.59 -1.3382,4.9564 -7.8921,16.64502 -6.0694,17.48902 1.8226,0.8444 6.6104,-11.69002 9.5619,-15.89902 z m -3.4925,-1.59 c 0.3088,-1.1438 0.34508,-1.9447 -0.0678,-2.1553 -0.41571,-0.21196 -1.0372,0.30721 -1.7971,1.2366 0.61453,0.31187 1.2413,0.62209 1.8649,0.91864 z m -1.8649,-0.91864 c -1.1475,-0.58232 -2.2761,-1.2029 -3.3908,-1.8373 -1.7554,4.877 -9.103,15.91396 -7.3579,16.88896 1.7505,0.9775 7.4832,-11.05696 10.749,-15.05096 z m -3.3908,-1.8373 c 0.41514,-1.1533 0.50537,-1.9801 0.10166,-2.2259 -0.41059,-0.25007 -1.1026,0.24309 -1.9666,1.166 0.61369,0.37128 1.2405,0.70461 1.8649,1.06 z m -1.8649,-1.06 c -1.0818,-0.65448 -2.14,-1.3112 -3.1873,-2.0139 -2.2115,4.7917 -10.195,15.08896 -8.5447,16.18196 1.6592,1.0986 8.1549,-10.34696 11.732,-14.16796 z m -3.1873,-2.0139 c 0.54872,-1.1889 0.73799,-2.0456 0.33909,-2.3319 -0.39906,-0.28643 -1.1407,0.16648 -2.1023,1.06 0.58985,0.4193 1.1615,0.8682 1.7632,1.272 z m -1.7632,-1.272 c -1.0236,-0.72756 -2.031,-1.4881 -3.0178,-2.2612 -2.5943,4.6255 -11.29,14.229 -9.7315,15.44 1.5626,1.2136 8.8754,-9.5793 12.749,-13.179 z m -3.0178,-2.2612 c 0.65198,-1.1625 0.92238,-2.0484 0.54252,-2.3672 -0.38539,-0.32342 -1.2076,0.12922 -2.2718,0.98931 0.5662,0.46912 1.15,0.92407 1.7293,1.3779 z m -1.7293,-1.3779 c -0.95598,-0.79212 -1.8969,-1.6035 -2.8143,-2.4379 -3.0257,4.4568 -12.232,13.209 -10.783,14.521 1.4569,1.3197 9.4381,-8.7226 13.597,-12.084 z m -2.8143,-2.4379 c 0.79465,-1.1705 1.181,-2.0791 0.81377,-2.4379 -0.37256,-0.36408 -1.2875,0.0824 -2.4752,0.91863 0.54082,0.52022 1.1061,1.0141 1.6615,1.5193 z m -1.6615,-1.5193 c -0.88518,-0.8515 -1.7321,-1.7239 -2.577,-2.6146 -3.4357,4.2447 -13.134,12.162 -11.8,13.568 1.34,1.4125 9.9367,-7.8275 14.377,-10.953 z m -2.577,-2.6146 c 0.93939,-1.1606 1.4348,-2.0754 1.085,-2.4732 -0.35415,-0.4028 -1.3392,-0.0147 -2.6448,0.77731 0.51273,0.57255 1.0308,1.1383 1.5597,1.6959 z m -1.5597,-1.6959 c -0.81768,-0.91307 -1.6323,-1.8415 -2.4074,-2.7912 -3.8346,3.9962 -13.859,11.055 -12.647,12.543 1.2189,1.4971 10.355,-6.9001 15.055,-9.7516 z m -2.4074,-2.7912 c 1.0956,-1.1418 1.7199,-2.0354 1.3902,-2.4732 -0.33147,-0.44017 -1.3933,-0.0975 -2.8143,0.63598 0.47352,0.61597 0.93306,1.2356 1.4241,1.8373 z m -1.4241,-1.8373 c -0.73424,-0.9552 -1.4792,-1.909 -2.1701,-2.8972 -4.2155,3.7105 -14.545,9.8893 -13.461,11.448 1.0931,1.5724 10.692,-6.0005 15.631,-8.5503 z m -2.1701,-2.8972 c 1.2616,-1.1105 2.0016,-1.995 1.6954,-2.4732 -0.30668,-0.47888 -1.4699,-0.21096 -3.0178,0.45933 0.43934,0.67076 0.86339,1.3572 1.3224,2.0139 z m -1.3224,-2.0139 c -0.65079,-0.99362 -1.2584,-2.0151 -1.8649,-3.0386 -4.5214,3.3853 -15.165,8.689 -14.207,10.317 0.96144,1.6347 10.909,-5.0426 16.072,-7.2784 z m -1.8649,-3.0386 c 1.3797,-1.033 2.2058,-1.8951 1.9327,-2.4026 -0.27832,-0.5172 -1.4746,-0.27716 -3.1534,0.31799 0.39064,0.70779 0.8091,1.39 1.2207,2.0846 z m -1.2207,-2.0846 c -0.57127,-1.035 -1.1361,-2.0823 -1.6615,-3.1445 -4.8653,3.0333 -15.603,7.4431 -14.784,9.1157 0.82611,1.6854 11.079,-4.0689 16.445,-5.9711 z m -1.6615,-3.1445 c 1.5713,-0.97961 2.5158,-1.8175 2.2718,-2.3672 -0.24489,-0.55171 -1.5267,-0.3938 -3.3229,0.10598 0.34482,0.75649 0.68327,1.5175 1.0511,2.2612 z m -1.0511,-2.2612 c -0.48354,-1.0608 -0.95303,-2.1303 -1.3902,-3.2152 -5.1337,2.6522 -16.011,6.1984 -15.326,7.9144 0.68955,1.7275 11.181,-3.1588 16.716,-4.6991 z m -1.3902,-3.2152 c 1.7112,-0.88408 2.7834,-1.6811 2.577,-2.2612 -0.2085,-0.58608 -1.5727,-0.50009 -3.4925,-0.10599 0.29147,0.79692 0.59924,1.5824 0.91551,2.3673 z m -0.91551,-2.3672 c -0.39903,-1.091 -0.76744,-2.1733 -1.1189,-3.2859 -5.3762,2.246 -16.28,4.9668 -15.733,6.7131 0.55068,1.7567 11.17,-2.2608 16.852,-3.4272 z m -1.1189,-3.2859 c 1.8591,-0.77667 3.0483,-1.5093 2.8821,-2.1199 -0.16681,-0.6128 -1.5834,-0.61914 -3.5942,-0.35333 0.23138,0.82592 0.45462,1.6584 0.71206,2.4732 z m -0.71206,-2.4732 c -0.30771,-1.0984 -0.62096,-2.2043 -0.8816,-3.3212 -5.5698,1.8225 -16.448,3.6713 -16.038,5.4411 0.41259,1.7802 11.134,-1.355 16.92,-2.1199 z m -0.8816,-3.3212 c 1.9798,-0.64782 3.3091,-1.3092 3.1873,-1.9433 -0.12275,-0.63917 -1.6227,-0.72941 -3.7298,-0.60065 0.16909,0.85159 0.34612,1.7023 0.54251,2.5439 z m -0.54251,-2.5439 c -0.22064,-1.1109 -0.43708,-2.2296 -0.61034,-3.3566 -5.7258,1.3834 -16.548,2.3861 -16.276,4.1692 0.27365,1.7905 11.022,-0.45432 16.886,-0.81265 z m -0.61034,-3.3566 c 2.0898,-0.50489 3.5332,-1.0772 3.4586,-1.7313 -0.0751,-0.6588 -1.6221,-0.86008 -3.7976,-0.88329 0.10439,0.8714 0.20647,1.752 0.33909,2.6146 z m -0.33909,-2.6146 c -0.13601,-1.136 -0.25207,-2.277 -0.33907,-3.4272 -5.8417,0.93312 -16.547,1.1809 -16.411,2.9679 0.13586,1.7937 10.848,0.39635 16.75,0.45931 z m -0.33907,-3.4272 c 2.1836,-0.34879 3.755,-0.77927 3.7298,-1.4486 -0.0253,-0.67583 -1.6611,-0.98143 -3.8994,-1.166 0.0372,0.87766 0.10375,1.7445 0.16954,2.6146 z m -0.16954,-2.6146 c -0.0477,-1.1242 -0.0678,-2.2561 -0.0678,-3.3919 0,-0.0119 -10e-6,-0.0236 0,-0.0353 -5.9223,0.47201 -16.478,-0.0832 -16.479,1.6959 -8.9e-4,1.7855 10.638,1.2442 16.547,1.7313 z m -0.0678,-3.4272 c 2.2726,-0.18114 3.9409,-0.48377 3.9672,-1.166 0.0265,-0.68547 -1.6298,-1.1294 -3.8994,-1.484 -0.0296,0.88035 -0.0674,1.7626 -0.0678,2.6499 z m 0.0678,-2.6499 c 0.0382,-1.1379 0.0833,-2.2665 0.16954,-3.3919 -5.9622,0.006 -16.276,-1.3026 -16.411,0.45932 -0.13586,1.7707 10.367,2.0148 16.242,2.9326 z m 0.16954,-3.3919 c 2.3444,-0.002 4.0912,-0.19302 4.1706,-0.88331 0.0804,-0.69853 -1.6056,-1.267 -3.9333,-1.8019 -0.0989,0.89073 -0.16851,1.7859 -0.23735,2.6852 z m 0.23735,-2.6852 c 0.12323,-1.1102 0.27181,-2.2252 0.44081,-3.3212 -5.9649,-0.46416 -16.009,-2.5106 -16.276,-0.77731 -0.26748,1.7384 10.013,2.7605 15.835,4.0985 z m 0.44081,-3.3212 c 2.4094,0.18747 4.2059,0.13026 4.3402,-0.56533 0.13501,-0.69948 -1.5365,-1.402 -3.8655,-2.1199 -0.17091,0.89801 -0.33464,1.7769 -0.4747,2.6852 z m 0.4747,-2.6852 c 0.21056,-1.1063 0.45566,-2.1963 0.71205,-3.2859 -5.9236,-0.92755 -15.677,-3.6774 -16.072,-1.9786 -0.39678,1.7044 9.6396,3.5011 15.36,5.2645 z m 0.71205,-3.2859 c 2.4391,0.38193 4.2865,0.48088 4.4758,-0.212 0.19013,-0.69578 -1.4841,-1.5344 -3.7976,-2.4379 -0.2387,0.87603 -0.46925,1.7622 -0.67815,2.6499 z m 0.67815,-2.6499 c 0.29398,-1.0789 0.61116,-2.155 0.94942,-3.2152 -5.8439,-1.385 -15.246,-4.8357 -15.767,-3.1799 -0.5226,1.6613 9.2322,4.2138 14.818,6.3951 z m 0.94942,-3.2152 c 2.4479,0.58019 4.3331,0.82624 4.5775,0.14134 0.24631,-0.69023 -1.3992,-1.6956 -3.6959,-2.7912 -0.31304,0.87262 -0.59881,1.7636 -0.88158,2.6499 z m 0.88158,-2.6499 c 0.37305,-1.0398 0.80562,-2.0545 1.2207,-3.0739 -5.7264,-1.832 -14.785,-5.916 -15.428,-4.3105 -0.64312,1.6066 8.7846,4.7974 14.207,7.3844 z m 1.2207,-3.0739 c 2.431,0.77776 4.3128,1.1655 4.6114,0.49465 0.30389,-0.68278 -1.2484,-1.8125 -3.5264,-3.1092 -0.38645,0.85916 -0.72884,1.7397 -1.085,2.6146 z m 1.085,-2.6146 c 0.44835,-0.99676 0.90209,-1.9932 1.3902,-2.9679 -5.5762,-2.2787 -14.162,-6.9465 -14.919,-5.4058 -0.75713,1.5408 8.2969,5.3954 13.529,8.3737 z m 1.3902,-2.9679 c 2.4286,0.99248 4.3242,1.5768 4.6792,0.91865 0.35975,-0.66692 -1.1349,-1.9367 -3.3568,-3.4272 -0.45201,0.82707 -0.89953,1.6642 -1.3224,2.5086 z m 1.3224,-2.5086 c 0.52611,-0.96272 1.0968,-1.8882 1.6615,-2.8266 -5.3875,-2.6956 -13.574,-7.9783 -14.445,-6.5011 -0.86692,1.4707 7.7781,5.9702 12.783,9.3277 z m 1.6615,-2.8266 c 2.3576,1.1796 4.24,1.9036 4.6453,1.272 0.41352,-0.64434 -1.016,-2.0342 -3.1534,-3.7099 -0.5228,0.80172 -0.99783,1.6168 -1.4919,2.4379 z m 1.4919,-2.4379 c 0.58841,-0.90229 1.2071,-1.7731 1.831,-2.6499 -5.1671,-3.1155 -12.829,-8.9912 -13.8,-7.5964 -0.97189,1.395 7.2238,6.5259 11.969,10.246 z m 1.831,-2.6499 c 2.3284,1.4039 4.15,2.2368 4.6114,1.6253 0.46424,-0.61523 -0.85705,-2.145 -2.8822,-3.9925 -0.59327,0.77231 -1.1643,1.5732 -1.7293,2.3672 z m 1.7293,-2.3672 c 0.65455,-0.85207 1.3125,-1.7192 2.0005,-2.5439 -4.9135,-3.507 -12.053,-9.8258 -13.122,-8.515 -1.0721,1.3141 6.6664,6.9944 11.122,11.059 z m 2.0005,-2.5439 c 2.2515,1.607 4.0311,2.631 4.5436,2.0493 0.51261,-0.58185 -0.74775,-2.1941 -2.6448,-4.2045 -0.64071,0.71422 -1.2841,1.4184 -1.8988,2.1552 z m 1.8988,-2.1552 c 0.72818,-0.81177 1.4771,-1.5856 2.2379,-2.3672 -4.6328,-3.8668 -11.278,-10.696 -12.444,-9.469 -1.1659,1.2265 6.0672,7.4499 10.206,11.836 z m 2.2379,-2.3672 c 2.1233,1.7722 3.8526,2.9442 4.408,2.4026 0.55839,-0.54459 -0.58458,-2.288 -2.3396,-4.4518 -0.70188,0.67076 -1.3915,1.3538 -2.0684,2.0492 z m 2.0684,-2.0492 c 0.77956,-0.74505 1.5641,-1.5122 2.3735,-2.2259 -4.3214,-4.2144 -10.381,-11.446 -11.63,-10.317 -1.2526,1.1324 5.4579,7.8592 9.2567,12.543 z m 2.3735,-2.2259 c 2.0136,1.9637 3.6367,3.2952 4.2384,2.7912 0.60355,-0.50546 -0.42429,-2.3451 -2.0344,-4.6638 -0.74567,0.6114 -1.4814,1.2354 -2.204,1.8726 z m 2.204,-1.8726 c 0.83882,-0.68775 1.7099,-1.3244 2.577,-1.9786 -3.9886,-4.5274 -9.4849,-12.197 -10.816,-11.165 -1.3296,1.0306 4.7994,8.1895 8.2395,13.144 z m 2.577,-1.9786 c 1.8584,2.1095 3.3967,3.5311 4.035,3.0739 0.64018,-0.4586 -0.26628,-2.3368 -1.6954,-4.7698 -0.78983,0.55279 -1.5712,1.1161 -2.3396,1.6959 z m 2.3396,-1.6959 c 0.88881,-0.62204 1.7976,-1.2151 2.7126,-1.8019 -3.6314,-4.817 -8.4969,-12.87 -9.901,-11.942 -1.4042,0.92769 4.1347,8.5455 7.1884,13.744 z m 2.7126,-1.8019 c 1.6999,2.2549 3.1243,3.8011 3.7976,3.3919 0.67804,-0.41204 -0.066,-2.4219 -1.3224,-4.9818 -0.84574,0.49984 -1.65,1.0606 -2.4752,1.5899 z m 2.4752,-1.5899 c 0.93048,-0.54994 1.86,-1.0766 2.8143,-1.59 -3.2494,-5.0826 -7.4532,-13.359 -8.9177,-12.543 -1.4657,0.8166 3.4471,8.7203 6.1034,14.133 z m 2.8143,-1.59 c 1.541,2.4103 2.8855,4.1057 3.5942,3.7452 0.70995,-0.36113 0.0818,-2.427 -0.98332,-5.0878 -0.8736,0.42994 -1.7557,0.88261 -2.6109,1.3426 z m 2.6109,-1.3426 c 0.95769,-0.47132 1.9371,-0.90835 2.916,-1.3426 -2.8513,-5.3146 -6.4146,-13.881 -7.9344,-13.179 -1.521,0.70282 2.7779,8.9251 5.0183,14.521 z m 2.916,-1.3426 c 1.3593,2.5336 2.5862,4.3351 3.3229,4.0279 0.73458,-0.30637 0.28165,-2.4294 -0.57643,-5.1585 -0.92212,0.36895 -1.8418,0.72928 -2.7465,1.1306 z m 2.7465,-1.1306 c 0.99948,-0.39989 1.9988,-0.80503 3.0178,-1.166 -2.438,-5.5147 -5.3495,-14.294 -6.9171,-13.709 -1.5709,0.58664 2.0918,9.1258 3.8994,14.875 z m 3.0178,-1.166 c 1.1649,2.635 2.2247,4.5265 2.9839,4.2752 0.75865,-0.25116 0.44999,-2.3923 -0.20345,-5.1938 -0.93683,0.29382 -1.8588,0.5922 -2.7804,0.91863 z m 2.7804,-0.91863 c 1.0176,-0.31914 2.0509,-0.63882 3.0856,-0.91863 -2.0115,-5.6826 -4.2248,-14.669 -5.8321,-14.203 -1.6081,0.46591 1.3777,9.2536 2.7465,15.122 z m 3.0856,-0.91863 c 0.96174,2.7169 1.9023,4.7161 2.6787,4.5225 0.77794,-0.19393 0.64531,-2.3736 0.20345,-5.2291 -0.96393,0.22308 -1.9319,0.44967 -2.8822,0.70664 z m 2.8822,-0.70664 c 1.0459,-0.24209 2.0922,-0.46999 3.1534,-0.67132 -1.5715,-5.8187 -3.1112,-14.935 -4.747,-14.592 -1.6365,0.3431 0.67219,9.3086 1.5937,15.263 z m 3.1534,-0.67132 c 0.75451,2.7936 1.5146,4.8342 2.3057,4.6992 0.79325,-0.13535 0.83651,-2.3334 0.61034,-5.2292 -0.98783,0.14997 -1.9404,0.34486 -2.916,0.52998 z m 2.916,-0.52998 c 1.0523,-0.15974 2.122,-0.26958 3.1873,-0.38865 -1.1249,-5.9202 -1.9367,-15.129 -3.5942,-14.91 -1.6551,0.21862 -0.0623,9.2914 0.4069,15.299 z m 3.1873,-0.38865 c 0.53989,2.8413 1.1677,4.9155 1.9666,4.8405 0.7955,-0.0747 0.98569,-2.2147 0.98333,-5.0878 -0.98422,0.0741 -1.976,0.13846 -2.95,0.24733 z m 2.95,-0.24733 c 1.0671,-0.0806 2.1429,-0.17305 3.2212,-0.21198 -0.67184,-5.9878 -0.8057,-15.216 -2.4752,-15.122 -1.6728,0.0937 -0.75092,9.3098 -0.74596,15.334 z m 3.2212,-0.21198 c 0.32234,2.8729 0.7576,4.9965 1.5598,4.9818 0.8022,-0.0147 1.1745,-2.1695 1.3902,-5.0525 -0.99057,3.8e-4 -1.9681,0.0351 -2.95,0.0707 z"
             inkscape:connector-curvature="0"
             style="fill:url(#radialGradient4295-7);stroke:#9f9999;stroke-width:0.470445;stroke-linecap:round;stroke-linejoin:round"
             id="path4529-9" />
        </g>
        <path
           d="m 430.58,947.04 c -22.306,0 -40.388,17.943 -40.388,40.067 0,22.124 18.082,40.048 40.388,40.048 22.306,0 40.388,-17.924 40.388,-40.048 0,-22.123 -18.082,-40.067 -40.388,-40.067 z m 0,2.9728 c 20.651,0 37.397,16.612 37.397,37.094 0,20.482 -16.746,37.075 -37.397,37.075 -20.651,0 -37.397,-16.593 -37.397,-37.075 0,-20.482 16.746,-37.094 37.397,-37.094 z"
           inkscape:connector-curvature="0"
           style="fill:url(#linearGradient3546);stroke-width:0"
           id="path4531-3" />
        <path
           d="m 237.14,859.82 c -4.7361,0.17699 -11.54,4.1577 -10.625,7.8438 3.211,12.942 10.609,33.844 10.609,33.844 l 24.75,2.0156 c 0,0 -16.498,-33.03 -20.703,-41.922 -0.63534,-1.3434 -2.178,-1.8505 -4.0312,-1.7812 z m 0.73437,5.9375 c 1.3922,-0.052 2.554,0.33453 3.0312,1.3438 3.1588,6.6794 15.547,31.484 15.547,31.484 l -18.594,-1.5156 c 0,0 -5.5566,-15.7 -7.9688,-25.422 -0.68703,-2.769 4.4266,-5.7577 7.9844,-5.8906 z"
           inkscape:connector-curvature="0"
           style="fill:url(#linearGradient3548);stroke:#6b0606;stroke-width:1.3081px"
           id="path4559-3" />
        <path
           d="m 256.97,898.59 -18.592,-1.5177 c 0,0 -5.5557,-15.699 -7.9679,-25.421 -0.95586,-3.8526 9.3063,-8.1414 11.003,-4.5531 3.1588,6.6794 15.556,31.492 15.556,31.492 z"
           inkscape:connector-curvature="0"
           style="opacity:0.75122;fill:url(#radialGradient4976-5);stroke:#e47b7b;stroke-width:0.982671px"
           sodipodi:nodetypes="ccssc"
           id="path4565-4" />
        <path
           d="m 33.585,913.63 c 0,0 -2.5336,-6.886 -9.5964,-14.647 -2.743,-3.0143 5.7885,-6.1236 9.5964,-7.5762 20.802,-7.9349 66.67,-4.0406 66.67,-4.0406 l 7.0711,13.637 c -23.86,-5.3017 -59.719,4.5553 -73.741,12.627 z"
           inkscape:connector-curvature="0"
           style="fill:url(#linearGradient3550);stroke-width:0"
           sodipodi:nodetypes="sssccs"
           id="path4576-3" />
        <rect
           x="263.57999"
           y="930.87"
           width="1.429"
           height="21.635"
           ry="1.6859"
           style="fill:url(#linearGradient3552);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4626-5" />
        <rect
           x="267.44"
           y="930.87"
           width="1.429"
           height="21.635"
           ry="1.6859"
           style="fill:url(#linearGradient3554);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4636-3" />
        <rect
           x="271.29999"
           y="930.87"
           width="1.429"
           height="21.635"
           ry="1.6859"
           style="fill:url(#linearGradient3556);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4640-2" />
        <rect
           x="274.39001"
           y="930.87"
           width="1.429"
           height="21.635"
           ry="1.6859"
           style="fill:url(#linearGradient3558);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4644-6" />
        <rect
           x="276.70999"
           y="930.87"
           width="1.429"
           height="21.635"
           ry="1.6859"
           style="fill:url(#linearGradient3560);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4648-1" />
        <rect
           x="279.79999"
           y="930.87"
           width="1.429"
           height="21.635"
           ry="1.6859"
           style="fill:url(#linearGradient3562);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4652-6" />
        <rect
           x="293.60001"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient3564);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4656-2" />
        <rect
           x="297.5"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient3566);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4658-9" />
        <rect
           x="301.39001"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient3568);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4660-0" />
        <rect
           x="304.51001"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient3570);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4662-2" />
        <rect
           x="306.85001"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient3572);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4664-9" />
        <rect
           x="309.95999"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient3574);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4666-3" />
        <rect
           x="304.89001"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient3576);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4680-2" />
        <rect
           x="308.78"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient3578);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4682-8" />
        <rect
           x="312.67999"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient3580);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4684-2" />
        <rect
           x="315.79999"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient3582);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4686-1" />
        <rect
           x="318.13"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient3584);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4688-3" />
        <rect
           x="321.25"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient3586);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4690-1" />
        <rect
           x="325.20999"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient3588);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4704-0" />
        <rect
           x="329.10999"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient3590);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4706-4" />
        <rect
           x="333"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient3592);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4708-1" />
        <rect
           x="336.12"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient3594);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4710-1" />
        <rect
           x="338.45999"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient3596);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4712-7" />
        <rect
           x="341.57001"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient3598);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4714-6" />
        <rect
           x="-315.16"
           y="1024.1"
           width="832.03003"
           height="10.312"
           ry="0.085992999"
           transform="matrix(0.53638,0,0,0.80255,192.16,214.4)"
           style="opacity:0.41951;fill:#2d2d2d;stroke:#660b0b;stroke-width:0.454094;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter4892-3)"
           id="rect4886-5" />
        <rect
           x="112.37"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.5595,0,0,0.38169,200.71,623.44)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.33245;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5350-2)"
           id="rect5110-5" />
        <rect
           x="119.27"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.5595,0,0,0.38169,200.71,623.44)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.33245;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5354-7)"
           id="rect5112-2" />
        <rect
           x="126.18"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.5595,0,0,0.38169,200.71,623.44)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.33245;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5358-4)"
           id="rect5114-8" />
        <rect
           x="131.7"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.5595,0,0,0.38169,200.71,623.44)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.33245;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5362-7)"
           id="rect5116-3" />
        <rect
           x="135.84"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.5595,0,0,0.38169,200.71,623.44)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.33245;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5366-7)"
           id="rect5118-5" />
        <rect
           x="141.36"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.5595,0,0,0.38169,200.71,623.44)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.33245;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5370-1)"
           id="rect5120-0" />
        <rect
           x="172.37"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5374-5)"
           id="rect5122-1" />
        <rect
           x="221.36"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5418-0)"
           id="rect5144-6" />
        <rect
           x="228.37"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5422-7)"
           id="rect5146-1" />
        <rect
           x="235.27"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5426-4)"
           id="rect5148-8" />
        <rect
           x="242.17999"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5430-9)"
           id="rect5150-2" />
        <rect
           x="247.7"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5434-8)"
           id="rect5152-7" />
        <rect
           x="251.84"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5438-5)"
           id="rect5154-8" />
        <rect
           x="257.35999"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5442-7)"
           id="rect5156-9" />
        <rect
           x="344.39999"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient3600);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect5446-3" />
        <rect
           x="348.29999"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient3602);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect5448-2" />
        <rect
           x="352.19"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient3604);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect5450-7" />
        <rect
           x="355.31"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient3606);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect5452-3" />
        <rect
           x="357.64999"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient3608);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect5454-5" />
        <rect
           x="360.76001"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient3610);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect5456-4" />
        <rect
           x="269.26999"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5426-4)"
           id="rect5458-7" />
        <rect
           x="276.17999"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5430-9)"
           id="rect5460-4" />
        <rect
           x="281.70001"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5434-8)"
           id="rect5462-2" />
        <rect
           x="285.84"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5438-5)"
           id="rect5464-7" />
        <rect
           x="291.35999"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5442-7)"
           id="rect5466-7" />
        <ellipse
           transform="matrix(0.78205,0,0,0.22222,185.13,737.72)"
           style="opacity:0.4878;fill:#ffffff;stroke-width:0;filter:url(#filter5499-4)"
           id="path5497-7"
           cx="178"
           cy="777.36218"
           rx="78"
           ry="9" />
        <path
           d="m 153.33,983.47 135,-1 3,-64 -9,-14"
           inkscape:connector-curvature="0"
           style="fill:none;stroke:#875c5c;stroke-width:1.3081px"
           id="path5503-9" />
        <path
           d="m 254.33,985.47 -14,-50 13,-3 v -8 l -13,-8"
           inkscape:connector-curvature="0"
           style="fill:none;stroke:#7a5252;stroke-width:1.3081px"
           id="path5505-2" />
        <path
           d="M 84.894,918.4 C 68.869,918.05255 52.773,923.1585 40.347,934.212 27.921,945.2655 19.225,962.265 17.956,985.4 l 2.75,0.14062 c 1.2354,-22.526 9.5943,-38.716 21.453,-49.266 11.859,-10.549 27.271,-15.459 42.672,-15.125 30.802,0.66785 61.365,22.149 63.125,61.391 l 2.75,-0.125 c -1.8219,-40.627 -33.762,-63.321 -65.813,-64.016 z"
           inkscape:connector-curvature="0"
           style="color:#000000;text-indent:0;text-transform:none;fill:url(#linearGradient3612);stroke-width:2.6162"
           id="path5507-1" />
        <path
           inkscape:connector-curvature="0"
           transform="matrix(0.5,0,0,0.5,207.33,521.29)"
           style="color:#000000;text-indent:0;text-transform:none;opacity:0.33659;fill:url(#linearGradient5531-5);stroke-width:2.6162;filter:url(#filter5537-9)"
           d="m -244.88,796.22 c -32.05,-0.6949 -64.242,9.517 -89.094,31.625 -24.852,22.108 -42.244,56.106 -44.781,102.38 l 5.5,0.28125 c 2.4708,-45.051 19.189,-77.433 42.906,-98.531 23.718,-21.099 54.541,-30.918 85.344,-30.25 61.605,1.3357 122.73,44.297 126.25,122.78 l 5.5,-0.25 c -3.6437,-81.254 -67.525,-126.64 -131.62,-128.03 z"
           id="path5521-0" />
        <path
           d="m 145.4,957.47 h 148.86 c 0.0397,0 38.106,5.0519 38.072,5.0717 l -38,21.857 c -0.0344,0.0198 -3.0329,-3.9368 -3.0717,-3.9283 -42.637,9.309 -95.829,4.5445 -145.86,4 -0.0397,-4.3e-4 -0.0717,-0.032 -0.0717,-0.0717 v -26.857 c 0,-0.0397 0.032,-0.0717 0.0717,-0.0717 z"
           inkscape:connector-curvature="0"
           style="opacity:0.31707;fill:url(#linearGradient3614);stroke-width:0"
           sodipodi:nodetypes="sssssssss"
           id="rect5541-5" />
        <path
           transform="matrix(0.5,0,0,0.5,207.33,521.29)"
           d="m 471.97,790.97 c -19.187,0.41332 -46.698,9.485 -69.562,18.406 -22.865,8.9212 -41.031,17.719 -41.031,17.719 l 1.25,2.5312 c 0,0 18.062,-8.7797 40.812,-17.656 22.75,-8.8766 50.242,-17.823 68.594,-18.219 9.2497,-0.19925 23.241,3.2464 34.781,6.7812 11.54,3.5348 20.688,7.125 20.688,7.125 l 1,-2.5938 c 0,0 -9.2241,-3.6187 -20.875,-7.1875 -11.6509,-3.5688 -25.669,-7.1214 -35.656,-6.9062 z"
           inkscape:connector-curvature="0"
           style="color:#000000;text-indent:0;text-transform:none;fill:url(#linearGradient5576-0);stroke-width:2.6162;filter:url(#filter5562-8)"
           id="path5560-6" />
        <ellipse
           transform="matrix(0.94285,0,0,0.51087,276.37,512.29)"
           style="opacity:0.6439;fill:url(#linearGradient5602-0);stroke-width:0"
           id="path5578-6"
           cx="-183"
           cy="781.36218"
           rx="31"
           ry="21" />
      </g>
      <g
         id="g2261-1"
         transform="matrix(1.2155558,0,0,1.2164629,587.74071,140.38583)"
         style="stroke-width:2.6162">
        <path
           d="m 107.83,901 -4.5457,-11.112 c 5.5446,-5.4099 10.837,-11.325 24.244,-1.0102 0,0 5.2576,28.551 15.152,37.881 6.9724,6.5743 17.693,8.8746 27.274,9.0914 12.947,0.29289 25.694,-5.0155 37.376,-10.607 4.4664,-2.1377 7.416,-10.127 12.122,-8.5863 2.0239,0.66256 2.3983,3.9651 2.0203,6.0609 -17.718,40.015 -80.384,30.6 -79.802,16.668 2.1582,-7.9633 -6.367,-35.888 -33.84,-38.386 z"
           inkscape:connector-curvature="0"
           style="fill:url(#linearGradient4310);stroke-width:0"
           sodipodi:nodetypes="cccssaaccc"
           id="path4578-1" />
        <path
           inkscape:connector-curvature="0"
           style="fill:#0e232e;stroke:#000000;stroke-width:0.551495px"
           sodipodi:nodetypes="sssss"
           d="m 240.69,907.69 c -2.89,-1.8389 -9.8058,-4.8782 -15.637,-6.9591 -0.52945,-0.18892 -1.7035,1.2078 -1.1728,1.3918 3.3511,1.1618 11.327,5.1371 16.224,7.4231 0.51805,0.24185 1.077,-1.5436 0.58641,-1.8558 z"
           id="path4616-5" />
        <path
           d="m 233.3,876.16 c -4.3057,6.267 -11.422,21.264 -16.295,33.91 -0.44237,1.1481 2.8282,3.6941 3.2589,2.5433 2.7202,-7.2668 12.028,-24.564 17.381,-35.182 0.5663,-1.1234 -3.6143,-2.3356 -4.3452,-1.2716 z"
           inkscape:connector-curvature="0"
           style="fill:#0e232e;stroke:#000000;stroke-width:1.24269px"
           sodipodi:nodetypes="sssss"
           id="path4588-2" />
        <path
           d="m 0.25,992.42 23.739,1.0102 118.19,1.5152 c 0,0 57.245,-1.545 85.863,-1.0102 10.28,0.19214 20.537,1.9616 30.81,1.5152 44.117,-1.2672 61.545,-1.5717 110.66,-1.0508 l 101.47,-65.114 -26.769,-10.607 -78.792,18.688 c 0,0 -119.84,53.501 -180.31,41.416 -29.824,-5.9599 -74.246,-53.033 -74.246,-53.033 H 35.104 Z"
           inkscape:connector-curvature="0"
           style="fill:#2d2d2d;stroke:#000000;stroke-width:1.3081px"
           sodipodi:nodetypes="cccscccccsccc"
           id="path4557-4" />
        <ellipse
           transform="matrix(0.44138,0,0,0.48031,37.916,558.7)"
           style="fill:url(#linearGradient4312);stroke-width:0"
           id="path4263-7"
           cx="104.55079"
           cy="887.20227"
           rx="73.236061"
           ry="64.144684" />
        <ellipse
           transform="matrix(0.5251,0,0,0.5251,29.94,528.22)"
           style="fill:#2d2d2d;stroke-width:0;filter:url(#filter4259-48)"
           id="path4245-5"
           cx="104.55079"
           cy="870.02966"
           rx="19.697975"
           ry="18.687822" />
        <ellipse
           transform="matrix(0.34075,0,0,0.34075,85.276,700.17)"
           style="fill:#9eabb0;stroke-width:0"
           id="path4241-2"
           cx="-2.0203052"
           cy="834.67432"
           rx="19.192898"
           ry="18.687822" />
        <path
           d="m 84.094,951.83 c -18.234,0 -33.015,14.667 -33.015,32.752 0,18.085 14.781,32.736 33.015,32.736 18.234,0 33.015,-14.652 33.015,-32.736 0,-18.085 -14.781,-32.752 -33.015,-32.752 z m 0,2.4301 c 16.88,0 30.569,13.579 30.569,30.322 0,16.742 -13.689,30.306 -30.569,30.306 -16.88,0 -30.569,-13.564 -30.569,-30.306 0,-16.742 13.689,-30.322 30.569,-30.322 z"
           inkscape:connector-curvature="0"
           style="fill:url(#radialGradient5103-0);stroke:#817e7e;stroke-width:1.84657;stroke-linecap:round;stroke-linejoin:round"
           id="path4039-9" />
        <path
           inkscape:connector-curvature="0"
           style="fill:url(#radialGradient5100-9);stroke:#817e7e;stroke-width:1.95618;stroke-linecap:round;stroke-linejoin:round"
           d="m 84.094,949.89 c -19.316,0 -34.975,15.538 -34.975,34.696 0,19.158 15.659,34.68 34.975,34.68 19.316,0 34.975,-15.522 34.975,-34.68 0,-19.158 -15.659,-34.696 -34.975,-34.696 z m 0,2.5744 c 17.883,0 32.384,14.386 32.384,32.122 0,17.736 -14.502,32.106 -32.384,32.106 -17.883,0 -32.384,-14.369 -32.384,-32.106 0,-17.736 14.502,-32.122 32.384,-32.122 z"
           id="path4050-1" />
        <path
           d="m 84.094,947.46 c -20.67,0 -37.425,16.627 -37.425,37.127 0,20.5 16.756,37.11 37.425,37.11 20.67,0 37.425,-16.609 37.425,-37.11 0,-20.5 -16.756,-37.127 -37.425,-37.127 z m 0,2.7547 c 19.136,0 34.653,15.393 34.653,34.372 0,18.979 -15.518,34.355 -34.653,34.355 -19.136,0 -34.653,-15.376 -34.653,-34.355 0,-18.979 15.518,-34.372 34.653,-34.372 z"
           inkscape:connector-curvature="0"
           style="fill:#666666;stroke:#817e7e;stroke-width:2.09325;stroke-linecap:round;stroke-linejoin:round"
           id="path4054-5" />
        <g
           transform="matrix(-0.49937,0,0,0.49632,-25.588,625.19)"
           style="stroke:#333333;stroke-width:3.9243"
           id="g3167-13">
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -214.15,715.98 -14.142,-51.518"
             id="path3169-3" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -211.12,780.63 -17.173,-48.487"
             id="path3171-8" />
          <path
             d="m -212.77,718.6 -0.32647,-53.423"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path3173-0" />
          <path
             d="m -226.57,781.83 -4.038,-51.28"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path3175-9" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -211.8,721.08 13.511,-51.687"
             id="path3177-8" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -241.5,778.58 9.3718,-50.578"
             id="path3179-9" />
          <path
             d="m -211.33,723.27 26.429,-46.429"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path3181-4" />
          <path
             d="m -254.9,771.13 22.143,-46.429"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path3183-3" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -211.43,725.52 37.545,-38.006"
             id="path3185-5" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -265.91,760.47 33.405,-39.116"
             id="path3187-8" />
          <path
             d="m -212.12,727.66 46.102,-26.994"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path3189-5" />
          <path
             d="m -273.78,747.32 42.391,-29.137"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path3191-7" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -213.33,729.54 51.52,-14.14"
             id="path3193-1" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -277.98,732.57 48.487,-17.173"
             id="path3195-6" />
          <path
             d="m -214.99,731.05 53.423,-0.32649"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path3197-3" />
          <path
             d="m -278.22,717.25 51.28,-4.038"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path3199-3" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -217.47,732.02 51.687,13.511"
             id="path3201-0" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -274.97,702.32 50.578,9.3718"
             id="path3203-5" />
          <path
             d="m -219.66,732.5 46.429,26.429"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path3205-3" />
          <path
             d="m -267.52,688.93 46.429,22.143"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path3207-6" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -221.91,732.39 38.006,37.545"
             id="path3209-1" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -256.86,677.92 39.116,33.405"
             id="path3211-4" />
        </g>
        <g
           transform="matrix(0.47794,0,0,0.48362,188.55,633.61)"
           style="stroke:#4d3636;stroke-width:3.92482"
           id="g3051-8">
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -214.15,715.98 -14.142,-51.518"
             id="path3007-7" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -211.12,780.63 -17.173,-48.487"
             id="path3009-0" />
          <path
             d="m -212.77,718.6 -0.32647,-53.423"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path3011-3" />
          <path
             d="m -226.57,781.83 -4.038,-51.28"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path3013-2" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -211.8,721.08 13.511,-51.687"
             id="path3015-47" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -241.5,778.58 9.3718,-50.578"
             id="path3017-2" />
          <path
             d="m -211.33,723.27 26.429,-46.429"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path3019-5" />
          <path
             d="m -254.9,771.13 22.143,-46.429"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path3021-1" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -211.43,725.52 37.545,-38.006"
             id="path3023-9" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -265.91,760.47 33.405,-39.116"
             id="path3025-6" />
          <path
             d="m -212.12,727.66 46.102,-26.994"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path3027-8" />
          <path
             d="m -273.78,747.32 42.391,-29.137"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path3029-3" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -213.33,729.54 51.52,-14.14"
             id="path3031-1" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -277.98,732.57 48.487,-17.173"
             id="path3033-9" />
          <path
             d="m -214.99,731.05 53.423,-0.32649"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path3035-7" />
          <path
             d="m -278.22,717.25 51.28,-4.038"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path3037-44" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -217.47,732.02 51.687,13.511"
             id="path3039-5" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -274.97,702.32 50.578,9.3718"
             id="path3041-2" />
          <path
             d="m -219.66,732.5 46.429,26.429"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path3043-77" />
          <path
             d="m -267.52,688.93 46.429,22.143"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path3045-6" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -221.91,732.39 38.006,37.545"
             id="path3047-4" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -256.86,677.92 39.116,33.405"
             id="path3049-8" />
        </g>
        <ellipse
           transform="matrix(0.2962,0,0,0.2962,85.186,737.36)"
           style="fill:url(#linearGradient4314);stroke:#333333;stroke-width:3.9243;stroke-linecap:round;stroke-linejoin:round"
           id="path4229-6"
           cx="-2.0203052"
           cy="834.67432"
           rx="19.192898"
           ry="18.687822" />
        <g
           transform="matrix(0.5599,0,0,0.55238,222.05,473.09)"
           style="stroke:#554f4f;stroke-width:2.61562"
           id="g4289-9">
          <path
             inkscape:connector-curvature="0"
             style="fill:url(#radialGradient2267-2);stroke:#554f4f;stroke-width:2.61562;stroke-linecap:round;stroke-linejoin:round"
             d="m -245.98,826.55 c -55.038,0 -99.649,44.13 -99.649,98.548 0,54.418 44.611,98.519 99.649,98.519 55.038,0 99.677,-44.102 99.677,-98.519 0,-54.418 -44.639,-98.548 -99.677,-98.548 z m 0,26.945 c 39.99,0 72.414,32.064 72.414,71.603 0,39.539 -32.424,71.575 -72.414,71.575 -39.99,0 -72.387,-32.035 -72.387,-71.575 0,-39.539 32.397,-71.603 72.387,-71.603 z"
             id="path4058-1" />
          <path
             inkscape:connector-curvature="0"
             style="fill:url(#radialGradient2269-9);stroke:#554f4f;stroke-width:2.61562;stroke-linecap:round;stroke-linejoin:round"
             d="m -244.61,826.11 c -1.6718,-0.0324 -1.4493,9.1844 -1.8988,15.193 h 0.0339 c 1.0817,0 2.1499,0.029 3.2212,0.0707 -0.21479,-6.0212 0.31566,-15.231 -1.3563,-15.263 z m 1.3563,15.263 c 0.10313,2.8885 0.35196,5.0421 1.1529,5.0878 0.80081,0.0456 1.329,-2.0538 1.7632,-4.9112 -0.96597,-0.072 -1.9409,-0.13863 -2.9161,-0.17665 z m 2.9161,0.17665 c 1.08,0.0806 2.1536,0.19581 3.2212,0.318 0.24291,-6.0201 1.5296,-15.176 -0.13562,-15.334 -1.6648,-0.15802 -2.1804,9.0588 -3.0856,15.016 z m 3.2212,0.318 c -0.11639,2.8848 -0.0146,5.0527 0.77989,5.1585 0.79573,0.10599 1.4855,-1.985 2.1362,-4.8052 -0.97056,-0.14693 -1.9344,-0.24097 -2.9161,-0.35331 z m 2.9161,0.35331 c 1.0702,0.16202 2.1315,0.3621 3.1873,0.56531 0.70049,-5.9846 2.6651,-15.016 1.0172,-15.299 -1.6474,-0.2827 -2.8498,8.861 -4.2045,14.733 z m 3.1873,0.56531 c -0.33604,2.871 -0.37794,5.0638 0.40689,5.2292 0.78191,0.16473 1.6161,-1.849 2.4752,-4.5932 -0.95558,-0.22034 -1.9138,-0.44961 -2.8821,-0.63599 z m 2.8821,0.63599 c 1.0556,0.2434 2.1142,0.49329 3.1534,0.77731 1.1507,-5.9147 3.7941,-14.786 2.1701,-15.193 -1.6246,-0.40672 -3.5227,8.6637 -5.3235,14.416 z m 3.1534,0.77731 c -0.54956,2.8248 -0.76767,5.0059 0,5.2291 0.7684,0.22344 1.7471,-1.7381 2.8143,-4.4165 -0.92615,-0.2895 -1.874,-0.55565 -2.8143,-0.81262 z m 2.8143,0.81262 c 1.0302,0.32204 2.0399,0.66323 3.0517,1.0246 1.597,-5.8112 4.9114,-14.453 3.3229,-14.981 -1.5878,-0.52739 -4.143,8.3555 -6.3746,13.956 z m 3.0517,1.0246 c -0.76267,2.7752 -1.1215,4.9135 -0.37299,5.1938 0.74738,0.27995 1.8894,-1.5534 3.1534,-4.1338 -0.91975,-0.36741 -1.8442,-0.72553 -2.7804,-1.06 z m 2.7804,1.06 c 1.007,0.40228 1.9976,0.83106 2.9838,1.272 2.0314,-5.6747 5.988,-14.087 4.4419,-14.733 -1.545,-0.64609 -4.773,8.0462 -7.4257,13.462 z m 2.9838,1.272 c -0.96564,2.6975 -1.5365,4.753 -0.81377,5.0878 0.72165,0.33431 2.0078,-1.3857 3.4586,-3.8512 -0.87151,-0.42808 -1.7557,-0.83918 -2.6448,-1.2366 z m 2.6448,1.2366 c 0.96152,0.4723 1.9429,0.93991 2.8822,1.4486 2.4572,-5.5056 7.0533,-13.584 5.5608,-14.345 -1.4936,-0.76163 -5.3841,7.6979 -8.443,12.896 z m 2.8822,1.4486 c -1.1683,2.6178 -1.8482,4.6642 -1.1529,5.0525 0.69397,0.38753 2.0625,-1.2557 3.6959,-3.6039 -0.8429,-0.49709 -1.6806,-0.98145 -2.543,-1.4486 z m 2.543,1.4486 c 0.93425,0.55097 1.8372,1.1445 2.7465,1.7313 2.8619,-5.3066 8.1152,-13.082 6.6798,-13.956 -1.4317,-0.87197 -5.9809,7.2718 -9.4263,12.225 z m 2.7465,1.7313 c -1.3457,2.4952 -2.1847,4.4396 -1.5258,4.8758 0.65669,0.43479 2.1472,-1.0638 3.9333,-3.2505 -0.79343,-0.55349 -1.5932,-1.0998 -2.4074,-1.6253 z m 2.4074,1.6253 c 0.89266,0.62269 1.7794,1.2508 2.6448,1.9079 3.2542,-5.0768 9.0979,-12.445 7.7309,-13.426 -1.367,-0.98127 -6.559,6.8453 -10.376,11.518 z m 2.6448,1.9079 c -1.5229,2.3758 -2.4867,4.2515 -1.8649,4.7345 0.61993,0.48153 2.2312,-0.89633 4.1706,-2.9326 -0.75602,-0.61873 -1.5268,-1.2104 -2.3057,-1.8019 z m 2.3057,1.8019 c 0.8283,0.67785 1.6415,1.375 2.4414,2.0846 3.6257,-4.819 10.074,-11.741 8.782,-12.826 -1.2902,-1.0828 -7.0611,6.371 -11.223,10.741 z m 2.4414,2.0846 c -1.6829,2.2367 -2.7501,4.0324 -2.1701,4.5578 0.57819,0.52375 2.2344,-0.71318 4.3062,-2.5792 -0.69861,-0.66606 -1.4136,-1.3377 -2.1362,-1.9786 z m 2.1362,1.9786 c 0.78547,0.74887 1.5515,1.5169 2.3057,2.2966 3.9737,-4.5351 10.976,-11.007 9.7654,-12.19 -1.2075,-1.1799 -7.5864,5.8539 -12.071,9.893 z m 2.3057,2.2966 c -1.8212,2.0786 -3.009,3.7478 -2.4752,4.3105 0.53371,0.56256 2.2515,-0.50216 4.4419,-2.1906 -0.64738,-0.7195 -1.2936,-1.4242 -1.9666,-2.1199 z m 1.9666,2.1199 c 0.72923,0.81043 1.4744,1.5983 2.1701,2.4379 4.307,-4.2231 11.764,-10.177 10.647,-11.448 -1.1172,-1.2707 -8.0368,5.3249 -12.817,9.0097 z m 2.1701,2.4379 c -1.974,1.9356 -3.3038,3.4973 -2.8143,4.0985 0.48671,0.59779 2.2832,-0.33317 4.5775,-1.8373 -0.58076,-0.75413 -1.1561,-1.5286 -1.7632,-2.2613 z m 1.7632,2.2613 c 0.66776,0.86706 1.3007,1.7554 1.9327,2.6499 4.6043,-3.8919 12.585,-9.3118 11.562,-10.67 -1.02,-1.3545 -8.4477,4.7114 -13.495,8.0204 z m 1.9327,2.6499 c -2.0735,1.7527 -3.4548,3.1872 -3.0178,3.8159 0.43555,0.62652 2.2772,-0.14489 4.6453,-1.4486 -0.52538,-0.80374 -1.074,-1.5838 -1.6276,-2.3672 z m 1.6276,2.3672 c 0.59415,0.9089 1.1722,1.822 1.7293,2.7559 4.8809,-3.5371 13.329,-8.3514 12.41,-9.787 -0.91817,-1.4338 -8.8546,4.1216 -14.139,7.0311 z m 1.7293,2.7559 c -2.1782,1.5785 -3.6407,2.8776 -3.2551,3.5332 0.38091,0.64765 2.2726,-0.004 4.6792,-1.0953 -0.45464,-0.82804 -0.94092,-1.6279 -1.4241,-2.4379 z m 1.4241,2.4379 c 0.52864,0.96283 1.0368,1.9463 1.5258,2.9326 5.1266,-3.1644 13.967,-7.3615 13.156,-8.8683 -0.81198,-1.5088 -9.1931,3.4488 -14.682,5.9358 z m 1.5258,2.9326 c -2.2573,1.3933 -3.824,2.5741 -3.4925,3.2506 0.32625,0.66563 2.2448,0.17614 4.6792,-0.70664 -0.3857,-0.85292 -0.77189,-1.7072 -1.1868,-2.5439 z m 1.1868,2.5439 c 0.45698,1.0106 0.87345,2.0415 1.2885,3.0739 5.3246,-2.7834 14.604,-6.3333 13.902,-7.9144 -0.69893,-1.5745 -9.5324,2.7887 -15.191,4.8405 z m 1.2885,3.0739 c -2.2592,1.181 -3.9002,2.251 -3.6281,2.9325 0.27248,0.68268 2.1814,0.33166 4.6453,-0.35331 -0.31478,-0.87086 -0.67289,-1.7227 -1.0172,-2.5792 z m 1.0172,2.5792 c 0.37906,1.0487 0.74983,2.1114 1.085,3.1799 5.5142,-2.3782 15.093,-5.2564 14.512,-6.8898 -0.57927,-1.6283 -9.8033,2.0991 -15.597,3.7099 z m 1.085,3.1799 c -2.3205,1.0008 -4.0158,1.9185 -3.7976,2.6146 0.21623,0.68984 2.0966,0.47846 4.5436,0 -0.24103,-0.87328 -0.47582,-1.7535 -0.74595,-2.6146 z m 0.74595,2.6146 c 0.29585,1.0719 0.5627,2.1612 0.81378,3.2506 5.6584,-1.9682 15.581,-4.1096 15.123,-5.7945 -0.45697,-1.6788 -10.047,1.3924 -15.937,2.5439 z m 0.81378,3.2506 c -2.3221,0.80773 -3.9933,1.5986 -3.8316,2.2966 0.16075,0.69353 1.9891,0.66848 4.408,0.38865 -0.17493,-0.90294 -0.37089,-1.7934 -0.57644,-2.6852 z m 0.57644,2.6852 c 0.21475,1.1085 0.44269,2.1969 0.61033,3.3212 5.7816,-1.5431 15.928,-2.9788 15.597,-4.6992 -0.33009,-1.719 -10.26,0.68988 -16.208,1.3779 z m 0.61033,3.3212 c -2.3447,0.62579 -4.0406,1.241 -3.9333,1.9433 0.1061,0.69399 1.8912,0.83119 4.2724,0.74197 -0.10228,-0.89597 -0.20689,-1.7986 -0.33908,-2.6852 z m 0.33908,2.6852 c 0.12708,1.1128 0.22506,2.2303 0.30516,3.3565 5.8559,-1.1212 16.239,-1.7428 16.038,-3.4979 -0.19956,-1.7486 -10.374,-0.0823 -16.343,0.14133 z m 0.30516,3.3565 c -2.3058,0.44149 -3.9519,0.96708 -3.8994,1.6606 0.0521,0.68644 1.7269,0.92912 4.035,1.0246 -0.0335,-0.89992 -0.0722,-1.7932 -0.13562,-2.6852 z m 0.13562,2.6852 c 0.0365,0.98504 0.0678,1.974 0.0678,2.9679 0,0.14188 7.7e-4,0.28227 0,0.42397 5.9003,-0.69454 16.444,-0.51878 16.377,-2.2966 -0.0666,-1.7722 -10.499,-0.84927 -16.445,-1.0953 z m 0.0678,3.3919 c -2.2644,0.26655 -3.899,0.65985 -3.8994,1.3426 -3.3e-4,0.67641 1.6013,1.074 3.8315,1.3426 0.0346,-0.89088 0.063,-1.7871 0.0678,-2.6852 z m -0.0678,2.6852 c -0.0443,1.139 -0.11112,2.2656 -0.20344,3.3919 5.9106,-0.26845 16.58,0.69526 16.649,-1.0953 0.0688,-1.7841 -10.559,-1.5875 -16.445,-2.2966 z m -0.20344,3.3919 c -2.2098,0.10047 -3.8142,0.35665 -3.8655,1.0246 -0.0509,0.6623 1.4556,1.1595 3.5942,1.59 0.1004,-0.86818 0.19941,-1.7381 0.27126,-2.6146 z m -0.27126,2.6146 c -0.13102,1.1332 -0.26209,2.2737 -0.44079,3.3919 5.8937,0.15957 16.612,1.9668 16.818,0.17666 0.20562,-1.7859 -10.59,-2.4035 -16.377,-3.5685 z m -0.44079,3.3919 c -2.1584,-0.0585 -3.6973,0.0897 -3.7976,0.74197 -0.0992,0.6442 1.2887,1.2574 3.3229,1.8373 0.16616,-0.8552 0.3365,-1.7145 0.4747,-2.5792 z m -0.4747,2.5792 c -0.21901,1.1272 -0.47933,2.2468 -0.74598,3.3565 5.8254,0.5657 16.575,3.2718 16.92,1.484 0.34315,-1.7776 -10.524,-3.23 -16.174,-4.8405 z m -0.74598,3.3565 c -2.0524,-0.19928 -3.5166,-0.16549 -3.662,0.45932 -0.14419,0.61937 1.1149,1.3021 3.0178,2.0139 0.22518,-0.81852 0.44512,-1.6444 0.64424,-2.4733 z m -0.64424,2.4733 c -0.3099,1.1266 -0.62513,2.2504 -0.98333,3.3566 5.738,0.97369 16.403,4.5248 16.886,2.7559 0.48151,-1.7621 -10.437,-4.0678 -15.903,-6.1124 z m -0.98333,3.3566 c -1.9679,-0.33395 -3.3715,-0.38801 -3.5603,0.212 -0.18781,0.597 0.95379,1.3538 2.7465,2.1906 0.28785,-0.79903 0.55129,-1.592 0.81376,-2.4026 z m -0.81376,2.4026 c -0.39653,1.1007 -0.84488,2.1728 -1.2885,3.2505 5.6095,1.359 16.196,5.8068 16.818,4.0632 0.6175,-1.7304 -10.267,-4.8575 -15.53,-7.3137 z m -1.2885,3.2505 c -1.8542,-0.44918 -3.1633,-0.56817 -3.3907,0 -0.22554,0.56342 0.775,1.3646 2.4074,2.2966 0.34094,-0.75878 0.66594,-1.5255 0.98331,-2.2966 z m -0.98331,2.2966 c -0.48023,1.0688 -0.96605,2.1366 -1.4919,3.1799 5.4619,1.7408 15.823,7.0738 16.581,5.3705 0.75507,-1.6965 -10.094,-5.6991 -15.089,-8.5504 z m -1.4919,3.1799 c -1.7559,-0.55966 -3.0246,-0.75004 -3.289,-0.21199 -0.26115,0.53141 0.64728,1.3846 2.1362,2.4026 0.39316,-0.72281 0.78189,-1.4546 1.1528,-2.1906 z m -1.1528,2.1906 c -0.56636,1.0412 -1.1519,2.0611 -1.7632,3.0739 5.2747,2.0899 15.348,8.3349 16.242,6.6778 0.88897,-1.648 -9.7709,-6.533 -14.478,-9.7517 z m -1.7632,3.0739 c -1.6298,-0.64574 -2.7901,-0.88991 -3.0856,-0.38867 -0.28973,0.49152 0.48355,1.3674 1.7971,2.4379 0.43591,-0.67499 0.87284,-1.3606 1.2885,-2.0493 z m -1.2885,2.0493 c -0.64982,1.0062 -1.3407,1.9932 -2.0344,2.9679 5.0502,2.3982 14.839,9.5542 15.869,7.9497 1.0234,-1.5947 -9.4726,-7.3629 -13.834,-10.918 z m -2.0344,2.9679 c -1.4809,-0.70324 -2.5625,-1.0241 -2.8821,-0.56532 -0.31749,0.45569 0.32534,1.3866 1.4919,2.5086 0.48225,-0.63628 0.92702,-1.2925 1.3902,-1.9433 z m -1.3902,1.9433 c -0.73365,0.96799 -1.4613,1.9288 -2.2379,2.8619 4.8324,2.7318 14.171,10.681 15.326,9.151 1.151,-1.5254 -9.0752,-8.1532 -13.088,-12.013 z m -2.2379,2.8619 c -1.3847,-0.78279 -2.3992,-1.1677 -2.7465,-0.74196 -0.34026,0.41706 0.20736,1.3212 1.2207,2.4732 0.50675,-0.57462 1.0357,-1.1423 1.5258,-1.7313 z m -1.5258,1.7313 c -0.8058,0.91369 -1.6286,1.809 -2.4753,2.6852 4.5693,2.9959 13.4,11.842 14.682,10.388 1.2751,-1.4474 -8.5759,-8.9449 -12.207,-13.073 z m -2.4753,2.6852 c -1.2454,-0.81658 -2.2128,-1.1958 -2.577,-0.81266 -0.36197,0.38082 0.10515,1.2569 0.98333,2.4379 0.54491,-0.53281 1.0642,-1.0773 1.5936,-1.6252 z m -1.5936,1.6252 c -0.89166,0.87188 -1.8146,1.7137 -2.7465,2.5439 4.3018,3.261 12.605,12.883 14.004,11.518 1.3908,-1.3564 -8.0106,-9.6962 -11.257,-14.062 z m -2.7465,2.5439 c -1.1313,-0.85755 -1.9922,-1.2987 -2.3735,-0.95396 -0.37389,0.33803 -0.007,1.2005 0.71206,2.3672 0.5557,-0.46874 1.119,-0.93001 1.6615,-1.4133 z m -1.6615,1.4133 c -0.96131,0.8109 -1.9503,1.6011 -2.95,2.3673 4.0103,3.4734 11.642,13.917 13.156,12.649 1.5065,-1.2618 -7.4018,-10.468 -10.206,-15.016 z m -2.95,2.3673 c -1.0041,-0.86972 -1.8131,-1.3276 -2.204,-1.0246 -0.39217,0.30398 -0.13183,1.1799 0.4747,2.3672 0.58957,-0.42707 1.1521,-0.90024 1.7293,-1.3426 z m -1.7293,1.3426 c -1.018,0.73742 -2.0659,1.4294 -3.1195,2.1199 3.7236,3.7177 10.628,14.82901 12.241,13.67401 1.6047,-1.14961 -6.7086,-11.07101 -9.1211,-15.79301 z m -3.1195,2.1199 c -0.91212,-0.91066 -1.6288,-1.3986 -2.0344,-1.1306 -0.40201,0.26557 -0.24215,1.1236 0.23735,2.2966 0.61057,-0.37726 1.1976,-0.77308 1.7971,-1.166 z m -1.7971,1.166 c -1.0815,0.66828 -2.1739,1.2898 -3.289,1.908 3.4197,3.9218 9.4836,15.70001 11.189,14.66301 1.6999,-1.0332 -5.9212,-11.72901 -7.9005,-16.57101 z m -3.289,1.908 c -0.81353,-0.93297 -1.4839,-1.3971 -1.8988,-1.166 -0.40945,0.22813 -0.32782,1.0025 0.0339,2.1552 0.62448,-0.32506 1.2503,-0.64861 1.8649,-0.98928 z m -1.8649,0.98928 c -1.1324,0.5895 -2.295,1.159 -3.4586,1.6959 3.1054,4.098 8.3482,16.45703 10.138,15.54603 1.7854,-0.9081 -5.1342,-12.31703 -6.6798,-17.24203 z m -3.4586,1.6959 c -0.71663,-0.94569 -1.3424,-1.431 -1.7632,-1.2366 -0.4161,0.19227 -0.39079,0.95079 -0.13564,2.0846 0.63849,-0.27483 1.2691,-0.55735 1.8988,-0.84797 z m -1.8988,0.84797 c -1.1935,0.51379 -2.4053,0.99118 -3.6281,1.4486 2.7831,4.2288 7.118,17.10196 8.9855,16.32296 1.859,-0.7752 -4.2363,-12.79096 -5.3574,-17.77196 z m -3.6281,1.4486 c -0.61783,-0.93876 -1.1389,-1.4644 -1.5597,-1.3073 -0.41946,0.15665 -0.49275,0.90868 -0.33907,2.0139 0.639,-0.22088 1.2673,-0.47041 1.8988,-0.70666 z m -1.8988,0.70666 c -1.2181,0.42105 -2.4519,0.80193 -3.6959,1.166 2.4563,4.3723 5.8017,17.633 7.7309,16.995 1.9228,-0.6366 -3.3399,-13.162 -4.035,-18.161 z m -3.6959,1.166 c -0.53312,-0.94899 -1.0343,-1.5007 -1.458,-1.3779 -0.42072,0.12189 -0.56788,0.87078 -0.5086,1.9432 0.65902,-0.17484 1.3144,-0.37444 1.9666,-0.56531 z m -1.9666,0.56531 c -1.2613,0.33466 -2.5466,0.60852 -3.8316,0.88327 2.127,4.46142 4.4921,18.09002 6.4763,17.59502 1.9752,-0.4923 -2.3693,-13.495 -2.6448,-18.47902 z m -3.8316,0.88327 c -0.4459,-0.93525 -0.86884,-1.5012 -1.2885,-1.4132 -0.41764,0.0875 -0.61427,0.77556 -0.64424,1.802 0.64652,-0.12198 1.2918,-0.2516 1.9327,-0.38877 z m -1.9327,0.38877 c -1.2908,0.24328 -2.5875,0.45426 -3.8994,0.63588 1.788,4.57657 3.0992,18.43497 5.12,18.08997 2.0182,-0.3443 -1.3644,-13.802 -1.2207,-18.72597 z m -3.8994,0.63588 c -0.36984,-0.94666 -0.76634,-1.5041 -1.1868,-1.4486 -0.41771,0.0554 -0.63631,0.69735 -0.74598,1.696 0.65124,-0.0742 1.2863,-0.15797 1.9327,-0.24745 z m -1.9327,0.24745 c -1.3121,0.14946 -2.6367,0.26672 -3.9672,0.35312 1.4435,4.6673 1.7179,18.636 3.7638,18.444 2.044,-0.1922 -0.33086,-13.932 0.20344,-18.79697 z m -3.9672,0.35312 c -0.29468,-0.95279 -0.63225,-1.54277 -1.0511,-1.51917 -0.42054,0.0236 -0.69635,0.67387 -0.8816,1.66047 0.64621,-0.027 1.2906,-0.1 1.9327,-0.1413 z m -1.9327,0.1413 c -1.1382,0.048 -2.2747,0.07 -3.4246,0.07 h -0.54254 c 1.0797,4.776 0.28709,18.658 2.3396,18.62 2.0529,-0.039 0.72451,-13.881 1.6275,-18.691 z m -3.9672,0.07 c -0.22172,-0.98073 -0.52789,-1.61707 -0.9494,-1.62517 -0.41901,-0.01 -0.72426,0.59349 -0.98333,1.55457 0.64452,0.02 1.2845,0.066 1.9327,0.07 z m -1.9327,-0.07 c -1.3401,-0.039 -2.6782,-0.1077 -4.001,-0.212 0.7154,4.8395 -1.1347,18.644 0.91548,18.761 2.052,0.1174 1.815,-13.835 3.0856,-18.549 z m -4.001,-0.212 c -0.14666,-0.99183 -0.39476,-1.62077 -0.8138,-1.66047 -0.41813,-0.0401 -0.78695,0.5411 -1.1189,1.484 0.63854,0.0653 1.2898,0.12617 1.9327,0.17657 z m -1.9327,-0.17651 c -1.3174,-0.13501 -2.6355,-0.2623 -3.9333,-0.45936 0.30978,4.94517 -2.5333,18.45697 -0.50862,18.72597 2.0363,0.271 2.8181,-13.655 4.4419,-18.26697 z m -3.9333,-0.45936 c -0.0654,-1.0452 -0.25269,-1.7289 -0.67816,-1.8019 -0.42202,-0.0729 -0.84416,0.49994 -1.2546,1.4486 0.64952,0.11498 1.2779,0.25384 1.9327,0.35332 z m -1.9327,-0.35332 c -1.3035,-0.23054 -2.5846,-0.48548 -3.8655,-0.77732 -0.0719,4.96181 -3.9299,18.19901 -1.9327,18.62001 2.0018,0.4216 3.8373,-13.31 5.7982,-17.84301 z m -3.8655,-0.77732 c 0.0151,-1.0526 -0.12255,-1.7674 -0.54251,-1.8726 -0.42702,-0.10697 -0.92774,0.44942 -1.4241,1.4133 0.64949,0.16508 1.3109,0.30991 1.9666,0.45927 z m -1.9666,-0.45927 c -1.2619,-0.3208 -2.5266,-0.64574 -3.7637,-1.0246 -0.50939,5.02108 -5.268,17.73598 -3.3229,18.30198 1.9525,0.568 4.7935,-12.824 7.0867,-17.27698 z m -3.7637,-1.0246 c 0.11263,-1.1101 0.052,-1.8374 -0.37298,-1.9786 -0.42212,-0.14022 -0.95028,0.3647 -1.5258,1.3073 0.6366,0.21255 1.2552,0.47418 1.8988,0.6713 z m -1.8988,-0.6713 c -1.2471,-0.4164 -2.4771,-0.86833 -3.6959,-1.3426 -0.89058,4.9692 -6.6094,17.30898 -4.7131,18.01898 1.8993,0.7115 5.7842,-12.378 8.4091,-16.67698 z m -3.6959,-1.3426 c 0.19703,-1.0994 0.20981,-1.8411 -0.20345,-2.0139 -0.42771,-0.17888 -1.0142,0.33595 -1.6954,1.3073 0.62125,0.25989 1.2697,0.46183 1.8988,0.70664 z m -1.8988,-0.70664 c -1.1842,-0.4954 -2.3381,-1.041 -3.4925,-1.59 -1.3382,4.9564 -7.8921,16.64502 -6.0694,17.48902 1.8226,0.8444 6.6104,-11.69002 9.5619,-15.89902 z m -3.4925,-1.59 c 0.3088,-1.1438 0.34508,-1.9447 -0.0678,-2.1553 -0.41571,-0.21196 -1.0372,0.30721 -1.7971,1.2366 0.61453,0.31187 1.2413,0.62209 1.8649,0.91864 z m -1.8649,-0.91864 c -1.1475,-0.58232 -2.2761,-1.2029 -3.3908,-1.8373 -1.7554,4.877 -9.103,15.91396 -7.3579,16.88896 1.7505,0.9775 7.4832,-11.05696 10.749,-15.05096 z m -3.3908,-1.8373 c 0.41514,-1.1533 0.50537,-1.9801 0.10166,-2.2259 -0.41059,-0.25007 -1.1026,0.24309 -1.9666,1.166 0.61369,0.37128 1.2405,0.70461 1.8649,1.06 z m -1.8649,-1.06 c -1.0818,-0.65448 -2.14,-1.3112 -3.1873,-2.0139 -2.2115,4.7917 -10.195,15.08896 -8.5447,16.18196 1.6592,1.0986 8.1549,-10.34696 11.732,-14.16796 z m -3.1873,-2.0139 c 0.54872,-1.1889 0.73799,-2.0456 0.33909,-2.3319 -0.39906,-0.28643 -1.1407,0.16648 -2.1023,1.06 0.58985,0.4193 1.1615,0.8682 1.7632,1.272 z m -1.7632,-1.272 c -1.0236,-0.72756 -2.031,-1.4881 -3.0178,-2.2612 -2.5943,4.6255 -11.29,14.229 -9.7315,15.44 1.5626,1.2136 8.8754,-9.5793 12.749,-13.179 z m -3.0178,-2.2612 c 0.65198,-1.1625 0.92238,-2.0484 0.54252,-2.3672 -0.38539,-0.32342 -1.2076,0.12922 -2.2718,0.98931 0.5662,0.46912 1.15,0.92407 1.7293,1.3779 z m -1.7293,-1.3779 c -0.95598,-0.79212 -1.8969,-1.6035 -2.8143,-2.4379 -3.0257,4.4568 -12.232,13.209 -10.783,14.521 1.4569,1.3197 9.4381,-8.7226 13.597,-12.084 z m -2.8143,-2.4379 c 0.79465,-1.1705 1.181,-2.0791 0.81377,-2.4379 -0.37256,-0.36408 -1.2875,0.0824 -2.4752,0.91863 0.54082,0.52022 1.1061,1.0141 1.6615,1.5193 z m -1.6615,-1.5193 c -0.88518,-0.8515 -1.7321,-1.7239 -2.577,-2.6146 -3.4357,4.2447 -13.134,12.162 -11.8,13.568 1.34,1.4125 9.9367,-7.8275 14.377,-10.953 z m -2.577,-2.6146 c 0.93939,-1.1606 1.4348,-2.0754 1.085,-2.4732 -0.35415,-0.4028 -1.3392,-0.0147 -2.6448,0.77731 0.51273,0.57255 1.0308,1.1383 1.5597,1.6959 z m -1.5597,-1.6959 c -0.81768,-0.91307 -1.6323,-1.8415 -2.4074,-2.7912 -3.8346,3.9962 -13.859,11.055 -12.647,12.543 1.2189,1.4971 10.355,-6.9001 15.055,-9.7516 z m -2.4074,-2.7912 c 1.0956,-1.1418 1.7199,-2.0354 1.3902,-2.4732 -0.33147,-0.44017 -1.3933,-0.0975 -2.8143,0.63598 0.47352,0.61597 0.93306,1.2356 1.4241,1.8373 z m -1.4241,-1.8373 c -0.73424,-0.9552 -1.4792,-1.909 -2.1701,-2.8972 -4.2155,3.7105 -14.545,9.8893 -13.461,11.448 1.0931,1.5724 10.692,-6.0005 15.631,-8.5503 z m -2.1701,-2.8972 c 1.2616,-1.1105 2.0016,-1.995 1.6954,-2.4732 -0.30668,-0.47888 -1.4699,-0.21096 -3.0178,0.45933 0.43934,0.67076 0.86339,1.3572 1.3224,2.0139 z m -1.3224,-2.0139 c -0.65079,-0.99362 -1.2584,-2.0151 -1.8649,-3.0386 -4.5214,3.3853 -15.165,8.689 -14.207,10.317 0.96144,1.6347 10.909,-5.0426 16.072,-7.2784 z m -1.8649,-3.0386 c 1.3797,-1.033 2.2058,-1.8951 1.9327,-2.4026 -0.27832,-0.5172 -1.4746,-0.27716 -3.1534,0.31799 0.39064,0.70779 0.8091,1.39 1.2207,2.0846 z m -1.2207,-2.0846 c -0.57127,-1.035 -1.1361,-2.0823 -1.6615,-3.1445 -4.8653,3.0333 -15.603,7.4431 -14.784,9.1157 0.82611,1.6854 11.079,-4.0689 16.445,-5.9711 z m -1.6615,-3.1445 c 1.5713,-0.97961 2.5158,-1.8175 2.2718,-2.3672 -0.24489,-0.55171 -1.5267,-0.3938 -3.3229,0.10598 0.34482,0.75649 0.68327,1.5175 1.0511,2.2612 z m -1.0511,-2.2612 c -0.48354,-1.0608 -0.95303,-2.1303 -1.3902,-3.2152 -5.1337,2.6522 -16.011,6.1984 -15.326,7.9144 0.68955,1.7275 11.181,-3.1588 16.716,-4.6991 z m -1.3902,-3.2152 c 1.7112,-0.88408 2.7834,-1.6811 2.577,-2.2612 -0.2085,-0.58608 -1.5727,-0.50009 -3.4925,-0.10599 0.29147,0.79692 0.59924,1.5824 0.91551,2.3673 z m -0.91551,-2.3672 c -0.39903,-1.091 -0.76744,-2.1733 -1.1189,-3.2859 -5.3762,2.246 -16.28,4.9668 -15.733,6.7131 0.55068,1.7567 11.17,-2.2608 16.852,-3.4272 z m -1.1189,-3.2859 c 1.8591,-0.77667 3.0483,-1.5093 2.8821,-2.1199 -0.16681,-0.6128 -1.5834,-0.61914 -3.5942,-0.35333 0.23138,0.82592 0.45462,1.6584 0.71206,2.4732 z m -0.71206,-2.4732 c -0.30771,-1.0984 -0.62096,-2.2043 -0.8816,-3.3212 -5.5698,1.8225 -16.448,3.6713 -16.038,5.4411 0.41259,1.7802 11.134,-1.355 16.92,-2.1199 z m -0.8816,-3.3212 c 1.9798,-0.64782 3.3091,-1.3092 3.1873,-1.9433 -0.12275,-0.63917 -1.6227,-0.72941 -3.7298,-0.60065 0.16909,0.85159 0.34612,1.7023 0.54251,2.5439 z m -0.54251,-2.5439 c -0.22064,-1.1109 -0.43708,-2.2296 -0.61034,-3.3566 -5.7258,1.3834 -16.548,2.3861 -16.276,4.1692 0.27365,1.7905 11.022,-0.45432 16.886,-0.81265 z m -0.61034,-3.3566 c 2.0898,-0.50489 3.5332,-1.0772 3.4586,-1.7313 -0.0751,-0.6588 -1.6221,-0.86008 -3.7976,-0.88329 0.10439,0.8714 0.20647,1.752 0.33909,2.6146 z m -0.33909,-2.6146 c -0.13601,-1.136 -0.25207,-2.277 -0.33907,-3.4272 -5.8417,0.93312 -16.547,1.1809 -16.411,2.9679 0.13586,1.7937 10.848,0.39635 16.75,0.45931 z m -0.33907,-3.4272 c 2.1836,-0.34879 3.755,-0.77927 3.7298,-1.4486 -0.0253,-0.67583 -1.6611,-0.98143 -3.8994,-1.166 0.0372,0.87766 0.10375,1.7445 0.16954,2.6146 z m -0.16954,-2.6146 c -0.0477,-1.1242 -0.0678,-2.2561 -0.0678,-3.3919 0,-0.0119 -10e-6,-0.0236 0,-0.0353 -5.9223,0.47201 -16.478,-0.0832 -16.479,1.6959 -8.9e-4,1.7855 10.638,1.2442 16.547,1.7313 z m -0.0678,-3.4272 c 2.2726,-0.18114 3.9409,-0.48377 3.9672,-1.166 0.0265,-0.68547 -1.6298,-1.1294 -3.8994,-1.484 -0.0296,0.88035 -0.0674,1.7626 -0.0678,2.6499 z m 0.0678,-2.6499 c 0.0382,-1.1379 0.0833,-2.2665 0.16954,-3.3919 -5.9622,0.006 -16.276,-1.3026 -16.411,0.45932 -0.13586,1.7707 10.367,2.0148 16.242,2.9326 z m 0.16954,-3.3919 c 2.3444,-0.002 4.0912,-0.19302 4.1706,-0.88331 0.0804,-0.69853 -1.6056,-1.267 -3.9333,-1.8019 -0.0989,0.89073 -0.16851,1.7859 -0.23735,2.6852 z m 0.23735,-2.6852 c 0.12323,-1.1102 0.27181,-2.2252 0.44081,-3.3212 -5.9649,-0.46416 -16.009,-2.5106 -16.276,-0.77731 -0.26748,1.7384 10.013,2.7605 15.835,4.0985 z m 0.44081,-3.3212 c 2.4094,0.18747 4.2059,0.13026 4.3402,-0.56533 0.13501,-0.69948 -1.5365,-1.402 -3.8655,-2.1199 -0.17091,0.89801 -0.33464,1.7769 -0.4747,2.6852 z m 0.4747,-2.6852 c 0.21056,-1.1063 0.45566,-2.1963 0.71205,-3.2859 -5.9236,-0.92755 -15.677,-3.6774 -16.072,-1.9786 -0.39678,1.7044 9.6396,3.5011 15.36,5.2645 z m 0.71205,-3.2859 c 2.4391,0.38193 4.2865,0.48088 4.4758,-0.212 0.19013,-0.69578 -1.4841,-1.5344 -3.7976,-2.4379 -0.2387,0.87603 -0.46925,1.7622 -0.67815,2.6499 z m 0.67815,-2.6499 c 0.29398,-1.0789 0.61116,-2.155 0.94942,-3.2152 -5.8439,-1.385 -15.246,-4.8357 -15.767,-3.1799 -0.5226,1.6613 9.2322,4.2138 14.818,6.3951 z m 0.94942,-3.2152 c 2.4479,0.58019 4.3331,0.82624 4.5775,0.14134 0.24631,-0.69023 -1.3992,-1.6956 -3.6959,-2.7912 -0.31304,0.87262 -0.59881,1.7636 -0.88158,2.6499 z m 0.88158,-2.6499 c 0.37305,-1.0398 0.80562,-2.0545 1.2207,-3.0739 -5.7264,-1.832 -14.785,-5.916 -15.428,-4.3105 -0.64312,1.6066 8.7846,4.7974 14.207,7.3844 z m 1.2207,-3.0739 c 2.431,0.77776 4.3128,1.1655 4.6114,0.49465 0.30389,-0.68278 -1.2484,-1.8125 -3.5264,-3.1092 -0.38645,0.85916 -0.72884,1.7397 -1.085,2.6146 z m 1.085,-2.6146 c 0.44835,-0.99676 0.90209,-1.9932 1.3902,-2.9679 -5.5762,-2.2787 -14.162,-6.9465 -14.919,-5.4058 -0.75713,1.5408 8.2969,5.3954 13.529,8.3737 z m 1.3902,-2.9679 c 2.4286,0.99248 4.3242,1.5768 4.6792,0.91865 0.35975,-0.66692 -1.1349,-1.9367 -3.3568,-3.4272 -0.45201,0.82707 -0.89953,1.6642 -1.3224,2.5086 z m 1.3224,-2.5086 c 0.52611,-0.96272 1.0968,-1.8882 1.6615,-2.8266 -5.3875,-2.6956 -13.574,-7.9783 -14.445,-6.5011 -0.86692,1.4707 7.7781,5.9702 12.783,9.3277 z m 1.6615,-2.8266 c 2.3576,1.1796 4.24,1.9036 4.6453,1.272 0.41352,-0.64434 -1.016,-2.0342 -3.1534,-3.7099 -0.5228,0.80172 -0.99783,1.6168 -1.4919,2.4379 z m 1.4919,-2.4379 c 0.58841,-0.90229 1.2071,-1.7731 1.831,-2.6499 -5.1671,-3.1155 -12.829,-8.9912 -13.8,-7.5964 -0.97189,1.395 7.2238,6.5259 11.969,10.246 z m 1.831,-2.6499 c 2.3284,1.4039 4.15,2.2368 4.6114,1.6253 0.46424,-0.61523 -0.85705,-2.145 -2.8822,-3.9925 -0.59327,0.77231 -1.1643,1.5732 -1.7293,2.3672 z m 1.7293,-2.3672 c 0.65455,-0.85207 1.3125,-1.7192 2.0005,-2.5439 -4.9135,-3.507 -12.053,-9.8258 -13.122,-8.515 -1.0721,1.3141 6.6664,6.9944 11.122,11.059 z m 2.0005,-2.5439 c 2.2515,1.607 4.0311,2.631 4.5436,2.0493 0.51261,-0.58185 -0.74775,-2.1941 -2.6448,-4.2045 -0.64071,0.71422 -1.2841,1.4184 -1.8988,2.1552 z m 1.8988,-2.1552 c 0.72818,-0.81177 1.4771,-1.5856 2.2379,-2.3672 -4.6328,-3.8668 -11.278,-10.696 -12.444,-9.469 -1.1659,1.2265 6.0672,7.4499 10.206,11.836 z m 2.2379,-2.3672 c 2.1233,1.7722 3.8526,2.9442 4.408,2.4026 0.55839,-0.54459 -0.58458,-2.288 -2.3396,-4.4518 -0.70188,0.67076 -1.3915,1.3538 -2.0684,2.0492 z m 2.0684,-2.0492 c 0.77956,-0.74505 1.5641,-1.5122 2.3735,-2.2259 -4.3214,-4.2144 -10.381,-11.446 -11.63,-10.317 -1.2526,1.1324 5.4579,7.8592 9.2567,12.543 z m 2.3735,-2.2259 c 2.0136,1.9637 3.6367,3.2952 4.2384,2.7912 0.60355,-0.50546 -0.42429,-2.3451 -2.0344,-4.6638 -0.74567,0.6114 -1.4814,1.2354 -2.204,1.8726 z m 2.204,-1.8726 c 0.83882,-0.68775 1.7099,-1.3244 2.577,-1.9786 -3.9886,-4.5274 -9.4849,-12.197 -10.816,-11.165 -1.3296,1.0306 4.7994,8.1895 8.2395,13.144 z m 2.577,-1.9786 c 1.8584,2.1095 3.3967,3.5311 4.035,3.0739 0.64018,-0.4586 -0.26628,-2.3368 -1.6954,-4.7698 -0.78983,0.55279 -1.5712,1.1161 -2.3396,1.6959 z m 2.3396,-1.6959 c 0.88881,-0.62204 1.7976,-1.2151 2.7126,-1.8019 -3.6314,-4.817 -8.4969,-12.87 -9.901,-11.942 -1.4042,0.92769 4.1347,8.5455 7.1884,13.744 z m 2.7126,-1.8019 c 1.6999,2.2549 3.1243,3.8011 3.7976,3.3919 0.67804,-0.41204 -0.066,-2.4219 -1.3224,-4.9818 -0.84574,0.49984 -1.65,1.0606 -2.4752,1.5899 z m 2.4752,-1.5899 c 0.93048,-0.54994 1.86,-1.0766 2.8143,-1.59 -3.2494,-5.0826 -7.4532,-13.359 -8.9177,-12.543 -1.4657,0.8166 3.4471,8.7203 6.1034,14.133 z m 2.8143,-1.59 c 1.541,2.4103 2.8855,4.1057 3.5942,3.7452 0.70995,-0.36113 0.0818,-2.427 -0.98332,-5.0878 -0.8736,0.42994 -1.7557,0.88261 -2.6109,1.3426 z m 2.6109,-1.3426 c 0.95769,-0.47132 1.9371,-0.90835 2.916,-1.3426 -2.8513,-5.3146 -6.4146,-13.881 -7.9344,-13.179 -1.521,0.70282 2.7779,8.9251 5.0183,14.521 z m 2.916,-1.3426 c 1.3593,2.5336 2.5862,4.3351 3.3229,4.0279 0.73458,-0.30637 0.28165,-2.4294 -0.57643,-5.1585 -0.92212,0.36895 -1.8418,0.72928 -2.7465,1.1306 z m 2.7465,-1.1306 c 0.99948,-0.39989 1.9988,-0.80503 3.0178,-1.166 -2.438,-5.5147 -5.3495,-14.294 -6.9171,-13.709 -1.5709,0.58664 2.0918,9.1258 3.8994,14.875 z m 3.0178,-1.166 c 1.1649,2.635 2.2247,4.5265 2.9839,4.2752 0.75865,-0.25116 0.44999,-2.3923 -0.20345,-5.1938 -0.93683,0.29382 -1.8588,0.5922 -2.7804,0.91863 z m 2.7804,-0.91863 c 1.0176,-0.31914 2.0509,-0.63882 3.0856,-0.91863 -2.0115,-5.6826 -4.2248,-14.669 -5.8321,-14.203 -1.6081,0.46591 1.3777,9.2536 2.7465,15.122 z m 3.0856,-0.91863 c 0.96174,2.7169 1.9023,4.7161 2.6787,4.5225 0.77794,-0.19393 0.64531,-2.3736 0.20345,-5.2291 -0.96393,0.22308 -1.9319,0.44967 -2.8822,0.70664 z m 2.8822,-0.70664 c 1.0459,-0.24209 2.0922,-0.46999 3.1534,-0.67132 -1.5715,-5.8187 -3.1112,-14.935 -4.747,-14.592 -1.6365,0.3431 0.67219,9.3086 1.5937,15.263 z m 3.1534,-0.67132 c 0.75451,2.7936 1.5146,4.8342 2.3057,4.6992 0.79325,-0.13535 0.83651,-2.3334 0.61034,-5.2292 -0.98783,0.14997 -1.9404,0.34486 -2.916,0.52998 z m 2.916,-0.52998 c 1.0523,-0.15974 2.122,-0.26958 3.1873,-0.38865 -1.1249,-5.9202 -1.9367,-15.129 -3.5942,-14.91 -1.6551,0.21862 -0.0623,9.2914 0.4069,15.299 z m 3.1873,-0.38865 c 0.53989,2.8413 1.1677,4.9155 1.9666,4.8405 0.7955,-0.0747 0.98569,-2.2147 0.98333,-5.0878 -0.98422,0.0741 -1.976,0.13846 -2.95,0.24733 z m 2.95,-0.24733 c 1.0671,-0.0806 2.1429,-0.17305 3.2212,-0.21198 -0.67184,-5.9878 -0.8057,-15.216 -2.4752,-15.122 -1.6728,0.0937 -0.75092,9.3098 -0.74596,15.334 z m 3.2212,-0.21198 c 0.32234,2.8729 0.7576,4.9965 1.5598,4.9818 0.8022,-0.0147 1.1745,-2.1695 1.3902,-5.0525 -0.99057,3.8e-4 -1.9681,0.0351 -2.95,0.0707 z"
             id="path4275-2" />
        </g>
        <path
           inkscape:connector-curvature="0"
           style="fill:url(#linearGradient4316);stroke-width:0"
           d="m 84.094,944.52 c -22.306,0 -40.388,17.943 -40.388,40.067 0,22.123 18.082,40.047 40.388,40.047 22.306,0 40.388,-17.924 40.388,-40.047 0,-22.123 -18.082,-40.067 -40.388,-40.067 z m 0,2.9728 c 20.651,0 37.397,16.612 37.397,37.094 0,20.481 -16.746,37.075 -37.397,37.075 -20.651,0 -37.397,-16.593 -37.397,-37.075 0,-20.482 16.746,-37.094 37.397,-37.094 z"
           id="path4297-9" />
        <path
           d="M 25.504,987.37 1.26,990.9 c -16.81,-104.78 142.46,-117.57 140.92,-50 31.247,15.249 62.051,10.964 78.287,-20.203 h 21.718 l -6.0609,-19.698 c 55.161,6.6009 108.75,5.3578 162.63,5.5558 l 9.0914,13.132 c 48.173,-13.472 60.032,-0.79671 75.761,9.0914 l -14.142,2.0203 c -39.099,-30.71 -114.36,40.544 -174.76,51.518 -73.263,13.312 -101.43,4.7764 -149,7.0711 -6.3093,-86.321 -118.3,-81.669 -120.21,-2.0203 z"
           inkscape:connector-curvature="0"
           style="fill:url(#linearGradient4318);stroke-width:0"
           sodipodi:nodetypes="ccccccccccscc"
           id="path4417-7" />
        <ellipse
           transform="matrix(0.44138,0,0,0.48031,384.4,561.23)"
           style="fill:url(#linearGradient2263-0);stroke-width:0"
           id="path4419-4"
           cx="104.55079"
           cy="887.20227"
           rx="73.236061"
           ry="64.144684" />
        <ellipse
           transform="matrix(0.5251,0,0,0.5251,376.42,530.74)"
           style="fill:#2d2d2d;stroke-width:0;filter:url(#filter4259-48)"
           id="path4421-1"
           cx="104.55079"
           cy="870.02966"
           rx="19.697975"
           ry="18.687822" />
        <ellipse
           transform="matrix(0.34075,0,0,0.34075,431.76,702.7)"
           style="fill:#9eabb0;stroke-width:0"
           id="path4423-2"
           cx="-2.0203052"
           cy="834.67432"
           rx="19.192898"
           ry="18.687822" />
        <path
           inkscape:connector-curvature="0"
           style="fill:url(#radialGradient5037-2);stroke:#817e7e;stroke-width:1.84657;stroke-linecap:round;stroke-linejoin:round"
           d="m 430.58,954.36 c -18.234,0 -33.015,14.667 -33.015,32.752 0,18.085 14.781,32.736 33.015,32.736 18.234,0 33.015,-14.652 33.015,-32.736 0,-18.085 -14.781,-32.752 -33.015,-32.752 z m 0,2.4301 c 16.88,0 30.569,13.579 30.569,30.322 0,16.742 -13.689,30.306 -30.569,30.306 -16.88,0 -30.569,-13.564 -30.569,-30.306 0,-16.742 13.689,-30.322 30.569,-30.322 z"
           id="path4425-3" />
        <path
           d="m 430.58,952.41 c -19.316,0 -34.975,15.538 -34.975,34.696 0,19.158 15.659,34.68 34.975,34.68 19.316,0 34.975,-15.522 34.975,-34.68 0,-19.158 -15.659,-34.696 -34.975,-34.696 z m 0,2.5744 c 17.883,0 32.384,14.386 32.384,32.122 0,17.736 -14.502,32.106 -32.384,32.106 -17.883,0 -32.384,-14.369 -32.384,-32.106 0,-17.736 14.502,-32.122 32.384,-32.122 z"
           inkscape:connector-curvature="0"
           style="fill:url(#radialGradient5034-8);stroke:#817e7e;stroke-width:1.95618;stroke-linecap:round;stroke-linejoin:round"
           id="path4427-9" />
        <path
           inkscape:connector-curvature="0"
           style="fill:#666666;stroke:#817e7e;stroke-width:2.09325;stroke-linecap:round;stroke-linejoin:round"
           d="m 430.58,949.98 c -20.67,0 -37.425,16.627 -37.425,37.127 0,20.501 16.756,37.11 37.425,37.11 20.67,0 37.425,-16.609 37.425,-37.11 0,-20.5 -16.756,-37.127 -37.425,-37.127 z m 0,2.7547 c 19.136,0 34.653,15.393 34.653,34.372 0,18.979 -15.518,34.355 -34.653,34.355 -19.136,0 -34.653,-15.376 -34.653,-34.355 0,-18.979 15.518,-34.372 34.653,-34.372 z"
           id="path4429-3" />
        <g
           transform="matrix(-0.49937,0,0,0.49632,320.89,627.71)"
           style="stroke:#333333;stroke-width:3.9243"
           id="g4431-4">
          <path
             d="m -214.15,715.98 -14.142,-51.518"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path4433-9" />
          <path
             d="m -211.12,780.63 -17.173,-48.487"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path4435-18" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -212.77,718.6 -0.32647,-53.423"
             id="path4437-9" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -226.57,781.83 -4.038,-51.28"
             id="path4439-46" />
          <path
             d="m -211.8,721.08 13.511,-51.687"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path4441-6" />
          <path
             d="m -241.5,778.58 9.3718,-50.578"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path4443-8" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -211.33,723.27 26.429,-46.429"
             id="path4445-5" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -254.9,771.13 22.143,-46.429"
             id="path4447-53" />
          <path
             d="m -211.43,725.52 37.545,-38.006"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path4449-6" />
          <path
             d="m -265.91,760.47 33.405,-39.116"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path4451-23" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -212.12,727.66 46.102,-26.994"
             id="path4453-8" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -273.78,747.32 42.391,-29.137"
             id="path4455-8" />
          <path
             d="m -213.33,729.54 51.52,-14.14"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path4457-1" />
          <path
             d="m -277.98,732.57 48.487,-17.173"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path4459-8" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -214.99,731.05 53.423,-0.32649"
             id="path4461-5" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -278.22,717.25 51.28,-4.038"
             id="path4463-3" />
          <path
             d="m -217.47,732.02 51.687,13.511"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path4465-4" />
          <path
             d="m -274.97,702.32 50.578,9.3718"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path4467-5" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             d="m -219.66,732.5 46.429,26.429"
             id="path4469-6" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             d="m -267.52,688.93 46.429,22.143"
             id="path4471-7" />
          <path
             d="m -221.91,732.39 38.006,37.545"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             id="path4473-6" />
          <path
             d="m -256.86,677.92 39.116,33.405"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#333333;stroke-width:3.9243"
             sodipodi:nodetypes="cc"
             id="path4475-2" />
        </g>
        <g
           transform="matrix(0.47794,0,0,0.48362,535.03,636.13)"
           style="stroke:#4d3636;stroke-width:3.92482"
           id="g4477-5">
          <path
             d="m -214.15,715.98 -14.142,-51.518"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path4479-65" />
          <path
             d="m -211.12,780.63 -17.173,-48.487"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path4481-3" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -212.77,718.6 -0.32647,-53.423"
             id="path4483-3" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -226.57,781.83 -4.038,-51.28"
             id="path4485-4" />
          <path
             d="m -211.8,721.08 13.511,-51.687"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path4487-7" />
          <path
             d="m -241.5,778.58 9.3718,-50.578"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path4489-0" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -211.33,723.27 26.429,-46.429"
             id="path4491-9" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -254.9,771.13 22.143,-46.429"
             id="path4493-5" />
          <path
             d="m -211.43,725.52 37.545,-38.006"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path4495-0" />
          <path
             d="m -265.91,760.47 33.405,-39.116"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path4497-1" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -212.12,727.66 46.102,-26.994"
             id="path4499-1" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -273.78,747.32 42.391,-29.137"
             id="path4501-4" />
          <path
             d="m -213.33,729.54 51.52,-14.14"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path4503-1" />
          <path
             d="m -277.98,732.57 48.487,-17.173"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path4505-1" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -214.99,731.05 53.423,-0.32649"
             id="path4507-7" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -278.22,717.25 51.28,-4.038"
             id="path4509-9" />
          <path
             d="m -217.47,732.02 51.687,13.511"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path4511-62" />
          <path
             d="m -274.97,702.32 50.578,9.3718"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path4513-9" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             d="m -219.66,732.5 46.429,26.429"
             id="path4515-3" />
          <path
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             d="m -267.52,688.93 46.429,22.143"
             id="path4517-1" />
          <path
             d="m -221.91,732.39 38.006,37.545"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             id="path4519-3" />
          <path
             d="m -256.86,677.92 39.116,33.405"
             inkscape:connector-curvature="0"
             style="fill:none;stroke:#4d3636;stroke-width:3.92482"
             sodipodi:nodetypes="cc"
             id="path4521-68" />
        </g>
        <ellipse
           transform="matrix(0.2962,0,0,0.2962,431.67,739.89)"
           style="fill:url(#linearGradient2265-1);stroke:#333333;stroke-width:3.9243;stroke-linecap:round;stroke-linejoin:round"
           id="path4523-4"
           cx="-2.0203052"
           cy="834.67432"
           rx="19.192898"
           ry="18.687822" />
        <g
           transform="matrix(0.5599,0,0,0.55238,568.54,475.62)"
           style="stroke:#9f9999;stroke-width:0.470445"
           id="g4525-6">
          <path
             d="m -245.98,826.55 c -55.038,0 -99.649,44.13 -99.649,98.548 0,54.418 44.611,98.519 99.649,98.519 55.038,0 99.677,-44.102 99.677,-98.519 0,-54.418 -44.639,-98.548 -99.677,-98.548 z m 0,26.945 c 39.99,0 72.414,32.064 72.414,71.603 0,39.539 -32.424,71.575 -72.414,71.575 -39.99,0 -72.387,-32.035 -72.387,-71.575 0,-39.539 32.397,-71.603 72.387,-71.603 z"
             inkscape:connector-curvature="0"
             style="fill:url(#radialGradient4293-7);stroke:#9f9999;stroke-width:0.470445;stroke-linecap:round;stroke-linejoin:round"
             id="path4527-4" />
          <path
             d="m -244.61,826.11 c -1.6718,-0.0324 -1.4493,9.1844 -1.8988,15.193 h 0.0339 c 1.0817,0 2.1499,0.029 3.2212,0.0707 -0.21479,-6.0212 0.31566,-15.231 -1.3563,-15.263 z m 1.3563,15.263 c 0.10313,2.8885 0.35196,5.0421 1.1529,5.0878 0.80081,0.0456 1.329,-2.0538 1.7632,-4.9112 -0.96597,-0.072 -1.9409,-0.13863 -2.9161,-0.17665 z m 2.9161,0.17665 c 1.08,0.0806 2.1536,0.19581 3.2212,0.318 0.24291,-6.0201 1.5296,-15.176 -0.13562,-15.334 -1.6648,-0.15802 -2.1804,9.0588 -3.0856,15.016 z m 3.2212,0.318 c -0.11639,2.8848 -0.0146,5.0527 0.77989,5.1585 0.79573,0.10599 1.4855,-1.985 2.1362,-4.8052 -0.97056,-0.14693 -1.9344,-0.24097 -2.9161,-0.35331 z m 2.9161,0.35331 c 1.0702,0.16202 2.1315,0.3621 3.1873,0.56531 0.70049,-5.9846 2.6651,-15.016 1.0172,-15.299 -1.6474,-0.2827 -2.8498,8.861 -4.2045,14.733 z m 3.1873,0.56531 c -0.33604,2.871 -0.37794,5.0638 0.40689,5.2292 0.78191,0.16473 1.6161,-1.849 2.4752,-4.5932 -0.95558,-0.22034 -1.9138,-0.44961 -2.8821,-0.63599 z m 2.8821,0.63599 c 1.0556,0.2434 2.1142,0.49329 3.1534,0.77731 1.1507,-5.9147 3.7941,-14.786 2.1701,-15.193 -1.6246,-0.40672 -3.5227,8.6637 -5.3235,14.416 z m 3.1534,0.77731 c -0.54956,2.8248 -0.76767,5.0059 0,5.2291 0.7684,0.22344 1.7471,-1.7381 2.8143,-4.4165 -0.92615,-0.2895 -1.874,-0.55565 -2.8143,-0.81262 z m 2.8143,0.81262 c 1.0302,0.32204 2.0399,0.66323 3.0517,1.0246 1.597,-5.8112 4.9114,-14.453 3.3229,-14.981 -1.5878,-0.52739 -4.143,8.3555 -6.3746,13.956 z m 3.0517,1.0246 c -0.76267,2.7752 -1.1215,4.9135 -0.37299,5.1938 0.74738,0.27995 1.8894,-1.5534 3.1534,-4.1338 -0.91975,-0.36741 -1.8442,-0.72553 -2.7804,-1.06 z m 2.7804,1.06 c 1.007,0.40228 1.9976,0.83106 2.9838,1.272 2.0314,-5.6747 5.988,-14.087 4.4419,-14.733 -1.545,-0.64609 -4.773,8.0462 -7.4257,13.462 z m 2.9838,1.272 c -0.96564,2.6975 -1.5365,4.753 -0.81377,5.0878 0.72165,0.33431 2.0078,-1.3857 3.4586,-3.8512 -0.87151,-0.42808 -1.7557,-0.83918 -2.6448,-1.2366 z m 2.6448,1.2366 c 0.96152,0.4723 1.9429,0.93991 2.8822,1.4486 2.4572,-5.5056 7.0533,-13.584 5.5608,-14.345 -1.4936,-0.76163 -5.3841,7.6979 -8.443,12.896 z m 2.8822,1.4486 c -1.1683,2.6178 -1.8482,4.6642 -1.1529,5.0525 0.69397,0.38753 2.0625,-1.2557 3.6959,-3.6039 -0.8429,-0.49709 -1.6806,-0.98145 -2.543,-1.4486 z m 2.543,1.4486 c 0.93425,0.55097 1.8372,1.1445 2.7465,1.7313 2.8619,-5.3066 8.1152,-13.082 6.6798,-13.956 -1.4317,-0.87197 -5.9809,7.2718 -9.4263,12.225 z m 2.7465,1.7313 c -1.3457,2.4952 -2.1847,4.4396 -1.5258,4.8758 0.65669,0.43479 2.1472,-1.0638 3.9333,-3.2505 -0.79343,-0.55349 -1.5932,-1.0998 -2.4074,-1.6253 z m 2.4074,1.6253 c 0.89266,0.62269 1.7794,1.2508 2.6448,1.9079 3.2542,-5.0768 9.0979,-12.445 7.7309,-13.426 -1.367,-0.98127 -6.559,6.8453 -10.376,11.518 z m 2.6448,1.9079 c -1.5229,2.3758 -2.4867,4.2515 -1.8649,4.7345 0.61993,0.48153 2.2312,-0.89633 4.1706,-2.9326 -0.75602,-0.61873 -1.5268,-1.2104 -2.3057,-1.8019 z m 2.3057,1.8019 c 0.8283,0.67785 1.6415,1.375 2.4414,2.0846 3.6257,-4.819 10.074,-11.741 8.782,-12.826 -1.2902,-1.0828 -7.0611,6.371 -11.223,10.741 z m 2.4414,2.0846 c -1.6829,2.2367 -2.7501,4.0324 -2.1701,4.5578 0.57819,0.52375 2.2344,-0.71318 4.3062,-2.5792 -0.69861,-0.66606 -1.4136,-1.3377 -2.1362,-1.9786 z m 2.1362,1.9786 c 0.78547,0.74887 1.5515,1.5169 2.3057,2.2966 3.9737,-4.5351 10.976,-11.007 9.7654,-12.19 -1.2075,-1.1799 -7.5864,5.8539 -12.071,9.893 z m 2.3057,2.2966 c -1.8212,2.0786 -3.009,3.7478 -2.4752,4.3105 0.53371,0.56256 2.2515,-0.50216 4.4419,-2.1906 -0.64738,-0.7195 -1.2936,-1.4242 -1.9666,-2.1199 z m 1.9666,2.1199 c 0.72923,0.81043 1.4744,1.5983 2.1701,2.4379 4.307,-4.2231 11.764,-10.177 10.647,-11.448 -1.1172,-1.2707 -8.0368,5.3249 -12.817,9.0097 z m 2.1701,2.4379 c -1.974,1.9356 -3.3038,3.4973 -2.8143,4.0985 0.48671,0.59779 2.2832,-0.33317 4.5775,-1.8373 -0.58076,-0.75413 -1.1561,-1.5286 -1.7632,-2.2613 z m 1.7632,2.2613 c 0.66776,0.86706 1.3007,1.7554 1.9327,2.6499 4.6043,-3.8919 12.585,-9.3118 11.562,-10.67 -1.02,-1.3545 -8.4477,4.7114 -13.495,8.0204 z m 1.9327,2.6499 c -2.0735,1.7527 -3.4548,3.1872 -3.0178,3.8159 0.43555,0.62652 2.2772,-0.14489 4.6453,-1.4486 -0.52538,-0.80374 -1.074,-1.5838 -1.6276,-2.3672 z m 1.6276,2.3672 c 0.59415,0.9089 1.1722,1.822 1.7293,2.7559 4.8809,-3.5371 13.329,-8.3514 12.41,-9.787 -0.91817,-1.4338 -8.8546,4.1216 -14.139,7.0311 z m 1.7293,2.7559 c -2.1782,1.5785 -3.6407,2.8776 -3.2551,3.5332 0.38091,0.64765 2.2726,-0.004 4.6792,-1.0953 -0.45464,-0.82804 -0.94092,-1.6279 -1.4241,-2.4379 z m 1.4241,2.4379 c 0.52864,0.96283 1.0368,1.9463 1.5258,2.9326 5.1266,-3.1644 13.967,-7.3615 13.156,-8.8683 -0.81198,-1.5088 -9.1931,3.4488 -14.682,5.9358 z m 1.5258,2.9326 c -2.2573,1.3933 -3.824,2.5741 -3.4925,3.2506 0.32625,0.66563 2.2448,0.17614 4.6792,-0.70664 -0.3857,-0.85292 -0.77189,-1.7072 -1.1868,-2.5439 z m 1.1868,2.5439 c 0.45698,1.0106 0.87345,2.0415 1.2885,3.0739 5.3246,-2.7834 14.604,-6.3333 13.902,-7.9144 -0.69893,-1.5745 -9.5324,2.7887 -15.191,4.8405 z m 1.2885,3.0739 c -2.2592,1.181 -3.9002,2.251 -3.6281,2.9325 0.27248,0.68268 2.1814,0.33166 4.6453,-0.35331 -0.31478,-0.87086 -0.67289,-1.7227 -1.0172,-2.5792 z m 1.0172,2.5792 c 0.37906,1.0487 0.74983,2.1114 1.085,3.1799 5.5142,-2.3782 15.093,-5.2564 14.512,-6.8898 -0.57927,-1.6283 -9.8033,2.0991 -15.597,3.7099 z m 1.085,3.1799 c -2.3205,1.0008 -4.0158,1.9185 -3.7976,2.6146 0.21623,0.68984 2.0966,0.47846 4.5436,0 -0.24103,-0.87328 -0.47582,-1.7535 -0.74595,-2.6146 z m 0.74595,2.6146 c 0.29585,1.0719 0.5627,2.1612 0.81378,3.2506 5.6584,-1.9682 15.581,-4.1096 15.123,-5.7945 -0.45697,-1.6788 -10.047,1.3924 -15.937,2.5439 z m 0.81378,3.2506 c -2.3221,0.80773 -3.9933,1.5986 -3.8316,2.2966 0.16075,0.69353 1.9891,0.66848 4.408,0.38865 -0.17493,-0.90294 -0.37089,-1.7934 -0.57644,-2.6852 z m 0.57644,2.6852 c 0.21475,1.1085 0.44269,2.1969 0.61033,3.3212 5.7816,-1.5431 15.928,-2.9788 15.597,-4.6992 -0.33009,-1.719 -10.26,0.68988 -16.208,1.3779 z m 0.61033,3.3212 c -2.3447,0.62579 -4.0406,1.241 -3.9333,1.9433 0.1061,0.69399 1.8912,0.83119 4.2724,0.74197 -0.10228,-0.89597 -0.20689,-1.7986 -0.33908,-2.6852 z m 0.33908,2.6852 c 0.12708,1.1128 0.22506,2.2303 0.30516,3.3565 5.8559,-1.1212 16.239,-1.7428 16.038,-3.4979 -0.19956,-1.7486 -10.374,-0.0823 -16.343,0.14133 z m 0.30516,3.3565 c -2.3058,0.44149 -3.9519,0.96708 -3.8994,1.6606 0.0521,0.68644 1.7269,0.92912 4.035,1.0246 -0.0335,-0.89992 -0.0722,-1.7932 -0.13562,-2.6852 z m 0.13562,2.6852 c 0.0365,0.98504 0.0678,1.974 0.0678,2.9679 0,0.14188 7.7e-4,0.28227 0,0.42397 5.9003,-0.69454 16.444,-0.51878 16.377,-2.2966 -0.0666,-1.7722 -10.499,-0.84927 -16.445,-1.0953 z m 0.0678,3.3919 c -2.2644,0.26655 -3.899,0.65985 -3.8994,1.3426 -3.3e-4,0.67641 1.6013,1.074 3.8315,1.3426 0.0346,-0.89088 0.063,-1.7871 0.0678,-2.6852 z m -0.0678,2.6852 c -0.0443,1.139 -0.11112,2.2656 -0.20344,3.3919 5.9106,-0.26845 16.58,0.69526 16.649,-1.0953 0.0688,-1.7841 -10.559,-1.5875 -16.445,-2.2966 z m -0.20344,3.3919 c -2.2098,0.10047 -3.8142,0.35665 -3.8655,1.0246 -0.0509,0.6623 1.4556,1.1595 3.5942,1.59 0.1004,-0.86818 0.19941,-1.7381 0.27126,-2.6146 z m -0.27126,2.6146 c -0.13102,1.1332 -0.26209,2.2737 -0.44079,3.3919 5.8937,0.15957 16.612,1.9668 16.818,0.17666 0.20562,-1.7859 -10.59,-2.4035 -16.377,-3.5685 z m -0.44079,3.3919 c -2.1584,-0.0585 -3.6973,0.0897 -3.7976,0.74197 -0.0992,0.6442 1.2887,1.2574 3.3229,1.8373 0.16616,-0.8552 0.3365,-1.7145 0.4747,-2.5792 z m -0.4747,2.5792 c -0.21901,1.1272 -0.47933,2.2468 -0.74598,3.3565 5.8254,0.5657 16.575,3.2718 16.92,1.484 0.34315,-1.7776 -10.524,-3.23 -16.174,-4.8405 z m -0.74598,3.3565 c -2.0524,-0.19928 -3.5166,-0.16549 -3.662,0.45932 -0.14419,0.61937 1.1149,1.3021 3.0178,2.0139 0.22518,-0.81852 0.44512,-1.6444 0.64424,-2.4733 z m -0.64424,2.4733 c -0.3099,1.1266 -0.62513,2.2504 -0.98333,3.3566 5.738,0.97369 16.403,4.5248 16.886,2.7559 0.48151,-1.7621 -10.437,-4.0678 -15.903,-6.1124 z m -0.98333,3.3566 c -1.9679,-0.33395 -3.3715,-0.38801 -3.5603,0.212 -0.18781,0.597 0.95379,1.3538 2.7465,2.1906 0.28785,-0.79903 0.55129,-1.592 0.81376,-2.4026 z m -0.81376,2.4026 c -0.39653,1.1007 -0.84488,2.1728 -1.2885,3.2505 5.6095,1.359 16.196,5.8068 16.818,4.0632 0.6175,-1.7304 -10.267,-4.8575 -15.53,-7.3137 z m -1.2885,3.2505 c -1.8542,-0.44918 -3.1633,-0.56817 -3.3907,0 -0.22554,0.56342 0.775,1.3646 2.4074,2.2966 0.34094,-0.75878 0.66594,-1.5255 0.98331,-2.2966 z m -0.98331,2.2966 c -0.48023,1.0688 -0.96605,2.1366 -1.4919,3.1799 5.4619,1.7408 15.823,7.0738 16.581,5.3705 0.75507,-1.6965 -10.094,-5.6991 -15.089,-8.5504 z m -1.4919,3.1799 c -1.7559,-0.55966 -3.0246,-0.75004 -3.289,-0.21199 -0.26115,0.53141 0.64728,1.3846 2.1362,2.4026 0.39316,-0.72281 0.78189,-1.4546 1.1528,-2.1906 z m -1.1528,2.1906 c -0.56636,1.0412 -1.1519,2.0611 -1.7632,3.0739 5.2747,2.0899 15.348,8.3349 16.242,6.6778 0.88897,-1.648 -9.7709,-6.533 -14.478,-9.7517 z m -1.7632,3.0739 c -1.6298,-0.64574 -2.7901,-0.88991 -3.0856,-0.38867 -0.28973,0.49152 0.48355,1.3674 1.7971,2.4379 0.43591,-0.67499 0.87284,-1.3606 1.2885,-2.0493 z m -1.2885,2.0493 c -0.64982,1.0062 -1.3407,1.9932 -2.0344,2.9679 5.0502,2.3982 14.839,9.5542 15.869,7.9497 1.0234,-1.5947 -9.4726,-7.3629 -13.834,-10.918 z m -2.0344,2.9679 c -1.4809,-0.70324 -2.5625,-1.0241 -2.8821,-0.56532 -0.31749,0.45569 0.32534,1.3866 1.4919,2.5086 0.48225,-0.63628 0.92702,-1.2925 1.3902,-1.9433 z m -1.3902,1.9433 c -0.73365,0.96799 -1.4613,1.9288 -2.2379,2.8619 4.8324,2.7318 14.171,10.681 15.326,9.151 1.151,-1.5254 -9.0752,-8.1532 -13.088,-12.013 z m -2.2379,2.8619 c -1.3847,-0.78279 -2.3992,-1.1677 -2.7465,-0.74196 -0.34026,0.41706 0.20736,1.3212 1.2207,2.4732 0.50675,-0.57462 1.0357,-1.1423 1.5258,-1.7313 z m -1.5258,1.7313 c -0.8058,0.91369 -1.6286,1.809 -2.4753,2.6852 4.5693,2.9959 13.4,11.842 14.682,10.388 1.2751,-1.4474 -8.5759,-8.9449 -12.207,-13.073 z m -2.4753,2.6852 c -1.2454,-0.81658 -2.2128,-1.1958 -2.577,-0.81266 -0.36197,0.38082 0.10515,1.2569 0.98333,2.4379 0.54491,-0.53281 1.0642,-1.0773 1.5936,-1.6252 z m -1.5936,1.6252 c -0.89166,0.87188 -1.8146,1.7137 -2.7465,2.5439 4.3018,3.261 12.605,12.883 14.004,11.518 1.3908,-1.3564 -8.0106,-9.6962 -11.257,-14.062 z m -2.7465,2.5439 c -1.1313,-0.85755 -1.9922,-1.2987 -2.3735,-0.95396 -0.37389,0.33803 -0.007,1.2005 0.71206,2.3672 0.5557,-0.46874 1.119,-0.93001 1.6615,-1.4133 z m -1.6615,1.4133 c -0.96131,0.8109 -1.9503,1.6011 -2.95,2.3673 4.0103,3.4734 11.642,13.917 13.156,12.649 1.5065,-1.2618 -7.4018,-10.468 -10.206,-15.016 z m -2.95,2.3673 c -1.0041,-0.86972 -1.8131,-1.3276 -2.204,-1.0246 -0.39217,0.30398 -0.13183,1.1799 0.4747,2.3672 0.58957,-0.42707 1.1521,-0.90024 1.7293,-1.3426 z m -1.7293,1.3426 c -1.018,0.73742 -2.0659,1.4294 -3.1195,2.1199 3.7236,3.7177 10.628,14.82901 12.241,13.67401 1.6047,-1.14961 -6.7086,-11.07101 -9.1211,-15.79301 z m -3.1195,2.1199 c -0.91212,-0.91066 -1.6288,-1.3986 -2.0344,-1.1306 -0.40201,0.26557 -0.24215,1.1236 0.23735,2.2966 0.61057,-0.37726 1.1976,-0.77308 1.7971,-1.166 z m -1.7971,1.166 c -1.0815,0.66828 -2.1739,1.2898 -3.289,1.908 3.4197,3.9218 9.4836,15.70001 11.189,14.66301 1.6999,-1.0332 -5.9212,-11.72901 -7.9005,-16.57101 z m -3.289,1.908 c -0.81353,-0.93297 -1.4839,-1.3971 -1.8988,-1.166 -0.40945,0.22813 -0.32782,1.0025 0.0339,2.1552 0.62448,-0.32506 1.2503,-0.64861 1.8649,-0.98928 z m -1.8649,0.98928 c -1.1324,0.5895 -2.295,1.159 -3.4586,1.6959 3.1054,4.098 8.3482,16.45703 10.138,15.54603 1.7854,-0.9081 -5.1342,-12.31703 -6.6798,-17.24203 z m -3.4586,1.6959 c -0.71663,-0.94569 -1.3424,-1.431 -1.7632,-1.2366 -0.4161,0.19227 -0.39079,0.95079 -0.13564,2.0846 0.63849,-0.27483 1.2691,-0.55735 1.8988,-0.84797 z m -1.8988,0.84797 c -1.1935,0.51379 -2.4053,0.99118 -3.6281,1.4486 2.7831,4.2288 7.118,17.10196 8.9855,16.32296 1.859,-0.7752 -4.2363,-12.79096 -5.3574,-17.77196 z m -3.6281,1.4486 c -0.61783,-0.93876 -1.1389,-1.4644 -1.5597,-1.3073 -0.41946,0.15665 -0.49275,0.90868 -0.33907,2.0139 0.639,-0.22088 1.2673,-0.47041 1.8988,-0.70666 z m -1.8988,0.70666 c -1.2181,0.42105 -2.4519,0.80193 -3.6959,1.166 2.4563,4.3723 5.8017,17.633 7.7309,16.995 1.9228,-0.6366 -3.3399,-13.162 -4.035,-18.161 z m -3.6959,1.166 c -0.53312,-0.94899 -1.0343,-1.5007 -1.458,-1.3779 -0.42072,0.12189 -0.56788,0.87078 -0.5086,1.9432 0.65902,-0.17484 1.3144,-0.37444 1.9666,-0.56531 z m -1.9666,0.56531 c -1.2613,0.33466 -2.5466,0.60852 -3.8316,0.88327 2.127,4.46142 4.4921,18.09002 6.4763,17.59502 1.9752,-0.4923 -2.3693,-13.495 -2.6448,-18.47902 z m -3.8316,0.88327 c -0.4459,-0.93525 -0.86884,-1.5012 -1.2885,-1.4132 -0.41764,0.0875 -0.61427,0.77556 -0.64424,1.802 0.64652,-0.12198 1.2918,-0.2516 1.9327,-0.38877 z m -1.9327,0.38877 c -1.2908,0.24328 -2.5875,0.45426 -3.8994,0.63588 1.788,4.57657 3.0992,18.43497 5.12,18.08997 2.0182,-0.3443 -1.3644,-13.802 -1.2207,-18.72597 z m -3.8994,0.63588 c -0.36984,-0.94666 -0.76634,-1.5041 -1.1868,-1.4486 -0.41771,0.0554 -0.63631,0.69735 -0.74598,1.696 0.65124,-0.0742 1.2863,-0.15797 1.9327,-0.24745 z m -1.9327,0.24745 c -1.3121,0.14946 -2.6367,0.26672 -3.9672,0.35312 1.4435,4.6673 1.7179,18.636 3.7638,18.444 2.044,-0.1922 -0.33086,-13.932 0.20344,-18.79697 z m -3.9672,0.35312 c -0.29468,-0.95279 -0.63225,-1.54277 -1.0511,-1.51917 -0.42054,0.0236 -0.69635,0.67387 -0.8816,1.66047 0.64621,-0.027 1.2906,-0.1 1.9327,-0.1413 z m -1.9327,0.1413 c -1.1382,0.048 -2.2747,0.07 -3.4246,0.07 h -0.54254 c 1.0797,4.776 0.28709,18.658 2.3396,18.62 2.0529,-0.039 0.72451,-13.881 1.6275,-18.691 z m -3.9672,0.07 c -0.22172,-0.98073 -0.52789,-1.61707 -0.9494,-1.62517 -0.41901,-0.01 -0.72426,0.59349 -0.98333,1.55457 0.64452,0.02 1.2845,0.066 1.9327,0.07 z m -1.9327,-0.07 c -1.3401,-0.039 -2.6782,-0.1077 -4.001,-0.212 0.7154,4.8395 -1.1347,18.644 0.91548,18.761 2.052,0.1174 1.815,-13.835 3.0856,-18.549 z m -4.001,-0.212 c -0.14666,-0.99183 -0.39476,-1.62077 -0.8138,-1.66047 -0.41813,-0.0401 -0.78695,0.5411 -1.1189,1.484 0.63854,0.0653 1.2898,0.12617 1.9327,0.17657 z m -1.9327,-0.17651 c -1.3174,-0.13501 -2.6355,-0.2623 -3.9333,-0.45936 0.30978,4.94517 -2.5333,18.45697 -0.50862,18.72597 2.0363,0.271 2.8181,-13.655 4.4419,-18.26697 z m -3.9333,-0.45936 c -0.0654,-1.0452 -0.25269,-1.7289 -0.67816,-1.8019 -0.42202,-0.0729 -0.84416,0.49994 -1.2546,1.4486 0.64952,0.11498 1.2779,0.25384 1.9327,0.35332 z m -1.9327,-0.35332 c -1.3035,-0.23054 -2.5846,-0.48548 -3.8655,-0.77732 -0.0719,4.96181 -3.9299,18.19901 -1.9327,18.62001 2.0018,0.4216 3.8373,-13.31 5.7982,-17.84301 z m -3.8655,-0.77732 c 0.0151,-1.0526 -0.12255,-1.7674 -0.54251,-1.8726 -0.42702,-0.10697 -0.92774,0.44942 -1.4241,1.4133 0.64949,0.16508 1.3109,0.30991 1.9666,0.45927 z m -1.9666,-0.45927 c -1.2619,-0.3208 -2.5266,-0.64574 -3.7637,-1.0246 -0.50939,5.02108 -5.268,17.73598 -3.3229,18.30198 1.9525,0.568 4.7935,-12.824 7.0867,-17.27698 z m -3.7637,-1.0246 c 0.11263,-1.1101 0.052,-1.8374 -0.37298,-1.9786 -0.42212,-0.14022 -0.95028,0.3647 -1.5258,1.3073 0.6366,0.21255 1.2552,0.47418 1.8988,0.6713 z m -1.8988,-0.6713 c -1.2471,-0.4164 -2.4771,-0.86833 -3.6959,-1.3426 -0.89058,4.9692 -6.6094,17.30898 -4.7131,18.01898 1.8993,0.7115 5.7842,-12.378 8.4091,-16.67698 z m -3.6959,-1.3426 c 0.19703,-1.0994 0.20981,-1.8411 -0.20345,-2.0139 -0.42771,-0.17888 -1.0142,0.33595 -1.6954,1.3073 0.62125,0.25989 1.2697,0.46183 1.8988,0.70664 z m -1.8988,-0.70664 c -1.1842,-0.4954 -2.3381,-1.041 -3.4925,-1.59 -1.3382,4.9564 -7.8921,16.64502 -6.0694,17.48902 1.8226,0.8444 6.6104,-11.69002 9.5619,-15.89902 z m -3.4925,-1.59 c 0.3088,-1.1438 0.34508,-1.9447 -0.0678,-2.1553 -0.41571,-0.21196 -1.0372,0.30721 -1.7971,1.2366 0.61453,0.31187 1.2413,0.62209 1.8649,0.91864 z m -1.8649,-0.91864 c -1.1475,-0.58232 -2.2761,-1.2029 -3.3908,-1.8373 -1.7554,4.877 -9.103,15.91396 -7.3579,16.88896 1.7505,0.9775 7.4832,-11.05696 10.749,-15.05096 z m -3.3908,-1.8373 c 0.41514,-1.1533 0.50537,-1.9801 0.10166,-2.2259 -0.41059,-0.25007 -1.1026,0.24309 -1.9666,1.166 0.61369,0.37128 1.2405,0.70461 1.8649,1.06 z m -1.8649,-1.06 c -1.0818,-0.65448 -2.14,-1.3112 -3.1873,-2.0139 -2.2115,4.7917 -10.195,15.08896 -8.5447,16.18196 1.6592,1.0986 8.1549,-10.34696 11.732,-14.16796 z m -3.1873,-2.0139 c 0.54872,-1.1889 0.73799,-2.0456 0.33909,-2.3319 -0.39906,-0.28643 -1.1407,0.16648 -2.1023,1.06 0.58985,0.4193 1.1615,0.8682 1.7632,1.272 z m -1.7632,-1.272 c -1.0236,-0.72756 -2.031,-1.4881 -3.0178,-2.2612 -2.5943,4.6255 -11.29,14.229 -9.7315,15.44 1.5626,1.2136 8.8754,-9.5793 12.749,-13.179 z m -3.0178,-2.2612 c 0.65198,-1.1625 0.92238,-2.0484 0.54252,-2.3672 -0.38539,-0.32342 -1.2076,0.12922 -2.2718,0.98931 0.5662,0.46912 1.15,0.92407 1.7293,1.3779 z m -1.7293,-1.3779 c -0.95598,-0.79212 -1.8969,-1.6035 -2.8143,-2.4379 -3.0257,4.4568 -12.232,13.209 -10.783,14.521 1.4569,1.3197 9.4381,-8.7226 13.597,-12.084 z m -2.8143,-2.4379 c 0.79465,-1.1705 1.181,-2.0791 0.81377,-2.4379 -0.37256,-0.36408 -1.2875,0.0824 -2.4752,0.91863 0.54082,0.52022 1.1061,1.0141 1.6615,1.5193 z m -1.6615,-1.5193 c -0.88518,-0.8515 -1.7321,-1.7239 -2.577,-2.6146 -3.4357,4.2447 -13.134,12.162 -11.8,13.568 1.34,1.4125 9.9367,-7.8275 14.377,-10.953 z m -2.577,-2.6146 c 0.93939,-1.1606 1.4348,-2.0754 1.085,-2.4732 -0.35415,-0.4028 -1.3392,-0.0147 -2.6448,0.77731 0.51273,0.57255 1.0308,1.1383 1.5597,1.6959 z m -1.5597,-1.6959 c -0.81768,-0.91307 -1.6323,-1.8415 -2.4074,-2.7912 -3.8346,3.9962 -13.859,11.055 -12.647,12.543 1.2189,1.4971 10.355,-6.9001 15.055,-9.7516 z m -2.4074,-2.7912 c 1.0956,-1.1418 1.7199,-2.0354 1.3902,-2.4732 -0.33147,-0.44017 -1.3933,-0.0975 -2.8143,0.63598 0.47352,0.61597 0.93306,1.2356 1.4241,1.8373 z m -1.4241,-1.8373 c -0.73424,-0.9552 -1.4792,-1.909 -2.1701,-2.8972 -4.2155,3.7105 -14.545,9.8893 -13.461,11.448 1.0931,1.5724 10.692,-6.0005 15.631,-8.5503 z m -2.1701,-2.8972 c 1.2616,-1.1105 2.0016,-1.995 1.6954,-2.4732 -0.30668,-0.47888 -1.4699,-0.21096 -3.0178,0.45933 0.43934,0.67076 0.86339,1.3572 1.3224,2.0139 z m -1.3224,-2.0139 c -0.65079,-0.99362 -1.2584,-2.0151 -1.8649,-3.0386 -4.5214,3.3853 -15.165,8.689 -14.207,10.317 0.96144,1.6347 10.909,-5.0426 16.072,-7.2784 z m -1.8649,-3.0386 c 1.3797,-1.033 2.2058,-1.8951 1.9327,-2.4026 -0.27832,-0.5172 -1.4746,-0.27716 -3.1534,0.31799 0.39064,0.70779 0.8091,1.39 1.2207,2.0846 z m -1.2207,-2.0846 c -0.57127,-1.035 -1.1361,-2.0823 -1.6615,-3.1445 -4.8653,3.0333 -15.603,7.4431 -14.784,9.1157 0.82611,1.6854 11.079,-4.0689 16.445,-5.9711 z m -1.6615,-3.1445 c 1.5713,-0.97961 2.5158,-1.8175 2.2718,-2.3672 -0.24489,-0.55171 -1.5267,-0.3938 -3.3229,0.10598 0.34482,0.75649 0.68327,1.5175 1.0511,2.2612 z m -1.0511,-2.2612 c -0.48354,-1.0608 -0.95303,-2.1303 -1.3902,-3.2152 -5.1337,2.6522 -16.011,6.1984 -15.326,7.9144 0.68955,1.7275 11.181,-3.1588 16.716,-4.6991 z m -1.3902,-3.2152 c 1.7112,-0.88408 2.7834,-1.6811 2.577,-2.2612 -0.2085,-0.58608 -1.5727,-0.50009 -3.4925,-0.10599 0.29147,0.79692 0.59924,1.5824 0.91551,2.3673 z m -0.91551,-2.3672 c -0.39903,-1.091 -0.76744,-2.1733 -1.1189,-3.2859 -5.3762,2.246 -16.28,4.9668 -15.733,6.7131 0.55068,1.7567 11.17,-2.2608 16.852,-3.4272 z m -1.1189,-3.2859 c 1.8591,-0.77667 3.0483,-1.5093 2.8821,-2.1199 -0.16681,-0.6128 -1.5834,-0.61914 -3.5942,-0.35333 0.23138,0.82592 0.45462,1.6584 0.71206,2.4732 z m -0.71206,-2.4732 c -0.30771,-1.0984 -0.62096,-2.2043 -0.8816,-3.3212 -5.5698,1.8225 -16.448,3.6713 -16.038,5.4411 0.41259,1.7802 11.134,-1.355 16.92,-2.1199 z m -0.8816,-3.3212 c 1.9798,-0.64782 3.3091,-1.3092 3.1873,-1.9433 -0.12275,-0.63917 -1.6227,-0.72941 -3.7298,-0.60065 0.16909,0.85159 0.34612,1.7023 0.54251,2.5439 z m -0.54251,-2.5439 c -0.22064,-1.1109 -0.43708,-2.2296 -0.61034,-3.3566 -5.7258,1.3834 -16.548,2.3861 -16.276,4.1692 0.27365,1.7905 11.022,-0.45432 16.886,-0.81265 z m -0.61034,-3.3566 c 2.0898,-0.50489 3.5332,-1.0772 3.4586,-1.7313 -0.0751,-0.6588 -1.6221,-0.86008 -3.7976,-0.88329 0.10439,0.8714 0.20647,1.752 0.33909,2.6146 z m -0.33909,-2.6146 c -0.13601,-1.136 -0.25207,-2.277 -0.33907,-3.4272 -5.8417,0.93312 -16.547,1.1809 -16.411,2.9679 0.13586,1.7937 10.848,0.39635 16.75,0.45931 z m -0.33907,-3.4272 c 2.1836,-0.34879 3.755,-0.77927 3.7298,-1.4486 -0.0253,-0.67583 -1.6611,-0.98143 -3.8994,-1.166 0.0372,0.87766 0.10375,1.7445 0.16954,2.6146 z m -0.16954,-2.6146 c -0.0477,-1.1242 -0.0678,-2.2561 -0.0678,-3.3919 0,-0.0119 -10e-6,-0.0236 0,-0.0353 -5.9223,0.47201 -16.478,-0.0832 -16.479,1.6959 -8.9e-4,1.7855 10.638,1.2442 16.547,1.7313 z m -0.0678,-3.4272 c 2.2726,-0.18114 3.9409,-0.48377 3.9672,-1.166 0.0265,-0.68547 -1.6298,-1.1294 -3.8994,-1.484 -0.0296,0.88035 -0.0674,1.7626 -0.0678,2.6499 z m 0.0678,-2.6499 c 0.0382,-1.1379 0.0833,-2.2665 0.16954,-3.3919 -5.9622,0.006 -16.276,-1.3026 -16.411,0.45932 -0.13586,1.7707 10.367,2.0148 16.242,2.9326 z m 0.16954,-3.3919 c 2.3444,-0.002 4.0912,-0.19302 4.1706,-0.88331 0.0804,-0.69853 -1.6056,-1.267 -3.9333,-1.8019 -0.0989,0.89073 -0.16851,1.7859 -0.23735,2.6852 z m 0.23735,-2.6852 c 0.12323,-1.1102 0.27181,-2.2252 0.44081,-3.3212 -5.9649,-0.46416 -16.009,-2.5106 -16.276,-0.77731 -0.26748,1.7384 10.013,2.7605 15.835,4.0985 z m 0.44081,-3.3212 c 2.4094,0.18747 4.2059,0.13026 4.3402,-0.56533 0.13501,-0.69948 -1.5365,-1.402 -3.8655,-2.1199 -0.17091,0.89801 -0.33464,1.7769 -0.4747,2.6852 z m 0.4747,-2.6852 c 0.21056,-1.1063 0.45566,-2.1963 0.71205,-3.2859 -5.9236,-0.92755 -15.677,-3.6774 -16.072,-1.9786 -0.39678,1.7044 9.6396,3.5011 15.36,5.2645 z m 0.71205,-3.2859 c 2.4391,0.38193 4.2865,0.48088 4.4758,-0.212 0.19013,-0.69578 -1.4841,-1.5344 -3.7976,-2.4379 -0.2387,0.87603 -0.46925,1.7622 -0.67815,2.6499 z m 0.67815,-2.6499 c 0.29398,-1.0789 0.61116,-2.155 0.94942,-3.2152 -5.8439,-1.385 -15.246,-4.8357 -15.767,-3.1799 -0.5226,1.6613 9.2322,4.2138 14.818,6.3951 z m 0.94942,-3.2152 c 2.4479,0.58019 4.3331,0.82624 4.5775,0.14134 0.24631,-0.69023 -1.3992,-1.6956 -3.6959,-2.7912 -0.31304,0.87262 -0.59881,1.7636 -0.88158,2.6499 z m 0.88158,-2.6499 c 0.37305,-1.0398 0.80562,-2.0545 1.2207,-3.0739 -5.7264,-1.832 -14.785,-5.916 -15.428,-4.3105 -0.64312,1.6066 8.7846,4.7974 14.207,7.3844 z m 1.2207,-3.0739 c 2.431,0.77776 4.3128,1.1655 4.6114,0.49465 0.30389,-0.68278 -1.2484,-1.8125 -3.5264,-3.1092 -0.38645,0.85916 -0.72884,1.7397 -1.085,2.6146 z m 1.085,-2.6146 c 0.44835,-0.99676 0.90209,-1.9932 1.3902,-2.9679 -5.5762,-2.2787 -14.162,-6.9465 -14.919,-5.4058 -0.75713,1.5408 8.2969,5.3954 13.529,8.3737 z m 1.3902,-2.9679 c 2.4286,0.99248 4.3242,1.5768 4.6792,0.91865 0.35975,-0.66692 -1.1349,-1.9367 -3.3568,-3.4272 -0.45201,0.82707 -0.89953,1.6642 -1.3224,2.5086 z m 1.3224,-2.5086 c 0.52611,-0.96272 1.0968,-1.8882 1.6615,-2.8266 -5.3875,-2.6956 -13.574,-7.9783 -14.445,-6.5011 -0.86692,1.4707 7.7781,5.9702 12.783,9.3277 z m 1.6615,-2.8266 c 2.3576,1.1796 4.24,1.9036 4.6453,1.272 0.41352,-0.64434 -1.016,-2.0342 -3.1534,-3.7099 -0.5228,0.80172 -0.99783,1.6168 -1.4919,2.4379 z m 1.4919,-2.4379 c 0.58841,-0.90229 1.2071,-1.7731 1.831,-2.6499 -5.1671,-3.1155 -12.829,-8.9912 -13.8,-7.5964 -0.97189,1.395 7.2238,6.5259 11.969,10.246 z m 1.831,-2.6499 c 2.3284,1.4039 4.15,2.2368 4.6114,1.6253 0.46424,-0.61523 -0.85705,-2.145 -2.8822,-3.9925 -0.59327,0.77231 -1.1643,1.5732 -1.7293,2.3672 z m 1.7293,-2.3672 c 0.65455,-0.85207 1.3125,-1.7192 2.0005,-2.5439 -4.9135,-3.507 -12.053,-9.8258 -13.122,-8.515 -1.0721,1.3141 6.6664,6.9944 11.122,11.059 z m 2.0005,-2.5439 c 2.2515,1.607 4.0311,2.631 4.5436,2.0493 0.51261,-0.58185 -0.74775,-2.1941 -2.6448,-4.2045 -0.64071,0.71422 -1.2841,1.4184 -1.8988,2.1552 z m 1.8988,-2.1552 c 0.72818,-0.81177 1.4771,-1.5856 2.2379,-2.3672 -4.6328,-3.8668 -11.278,-10.696 -12.444,-9.469 -1.1659,1.2265 6.0672,7.4499 10.206,11.836 z m 2.2379,-2.3672 c 2.1233,1.7722 3.8526,2.9442 4.408,2.4026 0.55839,-0.54459 -0.58458,-2.288 -2.3396,-4.4518 -0.70188,0.67076 -1.3915,1.3538 -2.0684,2.0492 z m 2.0684,-2.0492 c 0.77956,-0.74505 1.5641,-1.5122 2.3735,-2.2259 -4.3214,-4.2144 -10.381,-11.446 -11.63,-10.317 -1.2526,1.1324 5.4579,7.8592 9.2567,12.543 z m 2.3735,-2.2259 c 2.0136,1.9637 3.6367,3.2952 4.2384,2.7912 0.60355,-0.50546 -0.42429,-2.3451 -2.0344,-4.6638 -0.74567,0.6114 -1.4814,1.2354 -2.204,1.8726 z m 2.204,-1.8726 c 0.83882,-0.68775 1.7099,-1.3244 2.577,-1.9786 -3.9886,-4.5274 -9.4849,-12.197 -10.816,-11.165 -1.3296,1.0306 4.7994,8.1895 8.2395,13.144 z m 2.577,-1.9786 c 1.8584,2.1095 3.3967,3.5311 4.035,3.0739 0.64018,-0.4586 -0.26628,-2.3368 -1.6954,-4.7698 -0.78983,0.55279 -1.5712,1.1161 -2.3396,1.6959 z m 2.3396,-1.6959 c 0.88881,-0.62204 1.7976,-1.2151 2.7126,-1.8019 -3.6314,-4.817 -8.4969,-12.87 -9.901,-11.942 -1.4042,0.92769 4.1347,8.5455 7.1884,13.744 z m 2.7126,-1.8019 c 1.6999,2.2549 3.1243,3.8011 3.7976,3.3919 0.67804,-0.41204 -0.066,-2.4219 -1.3224,-4.9818 -0.84574,0.49984 -1.65,1.0606 -2.4752,1.5899 z m 2.4752,-1.5899 c 0.93048,-0.54994 1.86,-1.0766 2.8143,-1.59 -3.2494,-5.0826 -7.4532,-13.359 -8.9177,-12.543 -1.4657,0.8166 3.4471,8.7203 6.1034,14.133 z m 2.8143,-1.59 c 1.541,2.4103 2.8855,4.1057 3.5942,3.7452 0.70995,-0.36113 0.0818,-2.427 -0.98332,-5.0878 -0.8736,0.42994 -1.7557,0.88261 -2.6109,1.3426 z m 2.6109,-1.3426 c 0.95769,-0.47132 1.9371,-0.90835 2.916,-1.3426 -2.8513,-5.3146 -6.4146,-13.881 -7.9344,-13.179 -1.521,0.70282 2.7779,8.9251 5.0183,14.521 z m 2.916,-1.3426 c 1.3593,2.5336 2.5862,4.3351 3.3229,4.0279 0.73458,-0.30637 0.28165,-2.4294 -0.57643,-5.1585 -0.92212,0.36895 -1.8418,0.72928 -2.7465,1.1306 z m 2.7465,-1.1306 c 0.99948,-0.39989 1.9988,-0.80503 3.0178,-1.166 -2.438,-5.5147 -5.3495,-14.294 -6.9171,-13.709 -1.5709,0.58664 2.0918,9.1258 3.8994,14.875 z m 3.0178,-1.166 c 1.1649,2.635 2.2247,4.5265 2.9839,4.2752 0.75865,-0.25116 0.44999,-2.3923 -0.20345,-5.1938 -0.93683,0.29382 -1.8588,0.5922 -2.7804,0.91863 z m 2.7804,-0.91863 c 1.0176,-0.31914 2.0509,-0.63882 3.0856,-0.91863 -2.0115,-5.6826 -4.2248,-14.669 -5.8321,-14.203 -1.6081,0.46591 1.3777,9.2536 2.7465,15.122 z m 3.0856,-0.91863 c 0.96174,2.7169 1.9023,4.7161 2.6787,4.5225 0.77794,-0.19393 0.64531,-2.3736 0.20345,-5.2291 -0.96393,0.22308 -1.9319,0.44967 -2.8822,0.70664 z m 2.8822,-0.70664 c 1.0459,-0.24209 2.0922,-0.46999 3.1534,-0.67132 -1.5715,-5.8187 -3.1112,-14.935 -4.747,-14.592 -1.6365,0.3431 0.67219,9.3086 1.5937,15.263 z m 3.1534,-0.67132 c 0.75451,2.7936 1.5146,4.8342 2.3057,4.6992 0.79325,-0.13535 0.83651,-2.3334 0.61034,-5.2292 -0.98783,0.14997 -1.9404,0.34486 -2.916,0.52998 z m 2.916,-0.52998 c 1.0523,-0.15974 2.122,-0.26958 3.1873,-0.38865 -1.1249,-5.9202 -1.9367,-15.129 -3.5942,-14.91 -1.6551,0.21862 -0.0623,9.2914 0.4069,15.299 z m 3.1873,-0.38865 c 0.53989,2.8413 1.1677,4.9155 1.9666,4.8405 0.7955,-0.0747 0.98569,-2.2147 0.98333,-5.0878 -0.98422,0.0741 -1.976,0.13846 -2.95,0.24733 z m 2.95,-0.24733 c 1.0671,-0.0806 2.1429,-0.17305 3.2212,-0.21198 -0.67184,-5.9878 -0.8057,-15.216 -2.4752,-15.122 -1.6728,0.0937 -0.75092,9.3098 -0.74596,15.334 z m 3.2212,-0.21198 c 0.32234,2.8729 0.7576,4.9965 1.5598,4.9818 0.8022,-0.0147 1.1745,-2.1695 1.3902,-5.0525 -0.99057,3.8e-4 -1.9681,0.0351 -2.95,0.0707 z"
             inkscape:connector-curvature="0"
             style="fill:url(#radialGradient4295-71);stroke:#9f9999;stroke-width:0.470445;stroke-linecap:round;stroke-linejoin:round"
             id="path4529-4" />
        </g>
        <path
           d="m 430.58,947.04 c -22.306,0 -40.388,17.943 -40.388,40.067 0,22.124 18.082,40.048 40.388,40.048 22.306,0 40.388,-17.924 40.388,-40.048 0,-22.123 -18.082,-40.067 -40.388,-40.067 z m 0,2.9728 c 20.651,0 37.397,16.612 37.397,37.094 0,20.482 -16.746,37.075 -37.397,37.075 -20.651,0 -37.397,-16.593 -37.397,-37.075 0,-20.482 16.746,-37.094 37.397,-37.094 z"
           inkscape:connector-curvature="0"
           style="fill:url(#linearGradient4320);stroke-width:0"
           id="path4531-4" />
        <path
           d="m 237.14,859.82 c -4.7361,0.17699 -11.54,4.1577 -10.625,7.8438 3.211,12.942 10.609,33.844 10.609,33.844 l 24.75,2.0156 c 0,0 -16.498,-33.03 -20.703,-41.922 -0.63534,-1.3434 -2.178,-1.8505 -4.0312,-1.7812 z m 0.73437,5.9375 c 1.3922,-0.052 2.554,0.33453 3.0312,1.3438 3.1588,6.6794 15.547,31.484 15.547,31.484 l -18.594,-1.5156 c 0,0 -5.5566,-15.7 -7.9688,-25.422 -0.68703,-2.769 4.4266,-5.7577 7.9844,-5.8906 z"
           inkscape:connector-curvature="0"
           style="fill:url(#linearGradient4322);stroke:#6b0606;stroke-width:1.3081px"
           id="path4559-7" />
        <path
           d="m 256.97,898.59 -18.592,-1.5177 c 0,0 -5.5557,-15.699 -7.9679,-25.421 -0.95586,-3.8526 9.3063,-8.1414 11.003,-4.5531 3.1588,6.6794 15.556,31.492 15.556,31.492 z"
           inkscape:connector-curvature="0"
           style="opacity:0.75122;fill:url(#radialGradient4976-9);stroke:#e47b7b;stroke-width:0.982671px"
           sodipodi:nodetypes="ccssc"
           id="path4565-2" />
        <path
           d="m 33.585,913.63 c 0,0 -2.5336,-6.886 -9.5964,-14.647 -2.743,-3.0143 5.7885,-6.1236 9.5964,-7.5762 20.802,-7.9349 66.67,-4.0406 66.67,-4.0406 l 7.0711,13.637 c -23.86,-5.3017 -59.719,4.5553 -73.741,12.627 z"
           inkscape:connector-curvature="0"
           style="fill:url(#linearGradient4324);stroke-width:0"
           sodipodi:nodetypes="sssccs"
           id="path4576-2" />
        <rect
           x="263.57999"
           y="930.87"
           width="1.429"
           height="21.635"
           ry="1.6859"
           style="fill:url(#linearGradient4326);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4626-1" />
        <rect
           x="267.44"
           y="930.87"
           width="1.429"
           height="21.635"
           ry="1.6859"
           style="fill:url(#linearGradient4328);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4636-1" />
        <rect
           x="271.29999"
           y="930.87"
           width="1.429"
           height="21.635"
           ry="1.6859"
           style="fill:url(#linearGradient4330);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4640-8" />
        <rect
           x="274.39001"
           y="930.87"
           width="1.429"
           height="21.635"
           ry="1.6859"
           style="fill:url(#linearGradient4332);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4644-9" />
        <rect
           x="276.70999"
           y="930.87"
           width="1.429"
           height="21.635"
           ry="1.6859"
           style="fill:url(#linearGradient4334);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4648-7" />
        <rect
           x="279.79999"
           y="930.87"
           width="1.429"
           height="21.635"
           ry="1.6859"
           style="fill:url(#linearGradient4336);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4652-4" />
        <rect
           x="293.60001"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4338);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4656-1" />
        <rect
           x="297.5"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4340);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4658-3" />
        <rect
           x="301.39001"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4342);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4660-1" />
        <rect
           x="304.51001"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4344);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4662-20" />
        <rect
           x="306.85001"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4346);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4664-5" />
        <rect
           x="309.95999"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4348);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4666-4" />
        <rect
           x="304.89001"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4350);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4680-3" />
        <rect
           x="308.78"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4352);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4682-1" />
        <rect
           x="312.67999"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4354);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4684-5" />
        <rect
           x="315.79999"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4356);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4686-9" />
        <rect
           x="318.13"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4358);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4688-9" />
        <rect
           x="321.25"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4360);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4690-9" />
        <rect
           x="325.20999"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4362);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4704-08" />
        <rect
           x="329.10999"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4364);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4706-8" />
        <rect
           x="333"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4366);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4708-2" />
        <rect
           x="336.12"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4368);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4710-8" />
        <rect
           x="338.45999"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4370);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4712-2" />
        <rect
           x="341.57001"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4372);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect4714-9" />
        <rect
           x="-315.16"
           y="1024.1"
           width="832.03003"
           height="10.312"
           ry="0.085992999"
           transform="matrix(0.53638,0,0,0.80255,192.16,214.4)"
           style="opacity:0.41951;fill:#2d2d2d;stroke:#660b0b;stroke-width:0.454094;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter4892-8)"
           id="rect4886-4" />
        <rect
           x="112.37"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.5595,0,0,0.38169,200.71,623.44)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.33245;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5350-20)"
           id="rect5110-7" />
        <rect
           x="119.27"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.5595,0,0,0.38169,200.71,623.44)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.33245;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5354-0)"
           id="rect5112-4" />
        <rect
           x="126.18"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.5595,0,0,0.38169,200.71,623.44)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.33245;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5358-3)"
           id="rect5114-1" />
        <rect
           x="131.7"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.5595,0,0,0.38169,200.71,623.44)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.33245;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5362-73)"
           id="rect5116-36" />
        <rect
           x="135.84"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.5595,0,0,0.38169,200.71,623.44)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.33245;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5366-6)"
           id="rect5118-53" />
        <rect
           x="141.36"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.5595,0,0,0.38169,200.71,623.44)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.33245;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5370-4)"
           id="rect5120-7" />
        <rect
           x="172.37"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5374-9)"
           id="rect5122-9" />
        <rect
           x="221.36"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5418-4)"
           id="rect5144-8" />
        <rect
           x="228.37"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5422-2)"
           id="rect5146-3" />
        <rect
           x="235.27"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5426-7)"
           id="rect5148-1" />
        <rect
           x="242.17999"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5430-94)"
           id="rect5150-8" />
        <rect
           x="247.7"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5434-0)"
           id="rect5152-75" />
        <rect
           x="251.84"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5438-4)"
           id="rect5154-1" />
        <rect
           x="257.35999"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5442-3)"
           id="rect5156-8" />
        <rect
           x="344.39999"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4374);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect5446-0" />
        <rect
           x="348.29999"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4376);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect5448-9" />
        <rect
           x="352.19"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4378);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect5450-77" />
        <rect
           x="355.31"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4380);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect5452-0" />
        <rect
           x="357.64999"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4382);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect5454-2" />
        <rect
           x="360.76001"
           y="931.77002"
           width="1.4417"
           height="20.575001"
           ry="1.6033"
           style="fill:url(#linearGradient4384);stroke:#915d5d;stroke-width:1.07787;stroke-linecap:round;stroke-linejoin:round"
           id="rect5456-1" />
        <rect
           x="269.26999"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5426-7)"
           id="rect5458-5" />
        <rect
           x="276.17999"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5430-94)"
           id="rect5460-2" />
        <rect
           x="281.70001"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5434-0)"
           id="rect5462-8" />
        <rect
           x="285.84"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5438-4)"
           id="rect5464-4" />
        <rect
           x="291.35999"
           y="805.45001"
           width="2.5539999"
           height="56.682999"
           ry="4.4169002"
           transform="matrix(0.56447,0,0,0.36299,196.3,639.41)"
           style="opacity:0.17561;fill:#0e232e;stroke:#915d5d;stroke-width:2.38124;stroke-linecap:round;stroke-linejoin:round;filter:url(#filter5442-3)"
           id="rect5466-71" />
        <ellipse
           transform="matrix(0.78205,0,0,0.22222,185.13,737.72)"
           style="opacity:0.4878;fill:#ffffff;stroke-width:0;filter:url(#filter5499-0)"
           id="path5497-6"
           cx="178"
           cy="777.36218"
           rx="78"
           ry="9" />
        <path
           d="m 153.33,983.47 135,-1 3,-64 -9,-14"
           inkscape:connector-curvature="0"
           style="fill:none;stroke:#875c5c;stroke-width:1.3081px"
           id="path5503-92" />
        <path
           d="m 254.33,985.47 -14,-50 13,-3 v -8 l -13,-8"
           inkscape:connector-curvature="0"
           style="fill:none;stroke:#7a5252;stroke-width:1.3081px"
           id="path5505-9" />
        <path
           d="M 84.894,918.4 C 68.869,918.05255 52.773,923.1585 40.347,934.212 27.921,945.2655 19.225,962.265 17.956,985.4 l 2.75,0.14062 c 1.2354,-22.526 9.5943,-38.716 21.453,-49.266 11.859,-10.549 27.271,-15.459 42.672,-15.125 30.802,0.66785 61.365,22.149 63.125,61.391 l 2.75,-0.125 c -1.8219,-40.627 -33.762,-63.321 -65.813,-64.016 z"
           inkscape:connector-curvature="0"
           style="color:#000000;text-indent:0;text-transform:none;fill:url(#linearGradient4386);stroke-width:2.6162"
           id="path5507-0" />
        <path
           inkscape:connector-curvature="0"
           transform="matrix(0.5,0,0,0.5,207.33,521.29)"
           style="color:#000000;text-indent:0;text-transform:none;opacity:0.33659;fill:url(#linearGradient5531-9);stroke-width:2.6162;filter:url(#filter5537-5)"
           d="m -244.88,796.22 c -32.05,-0.6949 -64.242,9.517 -89.094,31.625 -24.852,22.108 -42.244,56.106 -44.781,102.38 l 5.5,0.28125 c 2.4708,-45.051 19.189,-77.433 42.906,-98.531 23.718,-21.099 54.541,-30.918 85.344,-30.25 61.605,1.3357 122.73,44.297 126.25,122.78 l 5.5,-0.25 c -3.6437,-81.254 -67.525,-126.64 -131.62,-128.03 z"
           id="path5521-7" />
        <path
           d="m 145.4,957.47 h 148.86 c 0.0397,0 38.106,5.0519 38.072,5.0717 l -38,21.857 c -0.0344,0.0198 -3.0329,-3.9368 -3.0717,-3.9283 -42.637,9.309 -95.829,4.5445 -145.86,4 -0.0397,-4.3e-4 -0.0717,-0.032 -0.0717,-0.0717 v -26.857 c 0,-0.0397 0.032,-0.0717 0.0717,-0.0717 z"
           inkscape:connector-curvature="0"
           style="opacity:0.31707;fill:url(#linearGradient4388);stroke-width:0"
           sodipodi:nodetypes="sssssssss"
           id="rect5541-2" />
        <path
           transform="matrix(0.5,0,0,0.5,207.33,521.29)"
           d="m 471.97,790.97 c -19.187,0.41332 -46.698,9.485 -69.562,18.406 -22.865,8.9212 -41.031,17.719 -41.031,17.719 l 1.25,2.5312 c 0,0 18.062,-8.7797 40.812,-17.656 22.75,-8.8766 50.242,-17.823 68.594,-18.219 9.2497,-0.19925 23.241,3.2464 34.781,6.7812 11.54,3.5348 20.688,7.125 20.688,7.125 l 1,-2.5938 c 0,0 -9.2241,-3.6187 -20.875,-7.1875 -11.6509,-3.5688 -25.669,-7.1214 -35.656,-6.9062 z"
           inkscape:connector-curvature="0"
           style="color:#000000;text-indent:0;text-transform:none;fill:url(#linearGradient5576-9);stroke-width:2.6162;filter:url(#filter5562-83)"
           id="path5560-4" />
        <ellipse
           transform="matrix(0.94285,0,0,0.51087,276.37,512.29)"
           style="opacity:0.6439;fill:url(#linearGradient5602-3);stroke-width:0"
           id="path5578-60"
           cx="-183"
           cy="781.36218"
           rx="31"
           ry="21" />
      </g>
    </g>
  </g>
  <metadata
     id="metadata1438">
    <rdf:RDF>
      <cc:Work>
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <cc:license
           rdf:resource="http://creativecommons.org/publicdomain/zero/1.0/" />
        <dc:publisher>
          <cc:Agent
             rdf:about="http://openclipart.org/">
            <dc:title>https://tekeye.uk</dc:title>
          </cc:Agent>
        </dc:publisher>
        <dc:title>Vintage red cars</dc:title>
        <dc:date>2020-05-08</dc:date>
        <dc:description>Vintage red cars</dc:description>
        <dc:source />
        <dc:creator>
          <cc:Agent>
            <dc:title>Daniel S. Fowler</dc:title>
          </cc:Agent>
        </dc:creator>
        <dc:subject>
          <rdf:Bag>
            <rdf:li>automobile; automotive; classic; cars; heritage; legacy; motor; racing; motoring; motorsports; transportation; vintage; red; green;</rdf:li>
          </rdf:Bag>
        </dc:subject>
        <dc:contributor>
          <cc:Agent>
            <dc:title>netalloy</dc:title>
          </cc:Agent>
        </dc:contributor>
        <dc:rights>
          <cc:Agent>
            <dc:title>Public Domain</dc:title>
          </cc:Agent>
        </dc:rights>
        <dc:language>en-GB</dc:language>
      </cc:Work>
      <cc:License
         rdf:about="http://creativecommons.org/publicdomain/zero/1.0/">
        <cc:permits
           rdf:resource="http://creativecommons.org/ns#Reproduction" />
        <cc:permits
           rdf:resource="http://creativecommons.org/ns#Distribution" />
        <cc:permits
           rdf:resource="http://creativecommons.org/ns#DerivativeWorks" />
      </cc:License>
    </rdf:RDF>
  </metadata>
</svg>
