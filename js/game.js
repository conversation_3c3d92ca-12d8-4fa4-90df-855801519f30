// 百家乐游戏类
class BaccaratGame {
    constructor() {
        this.deck = [];
        this.playerCards = [];
        this.bankerCards = [];
        this.currentBet = null;
        this.betAmount = 0;
        this.balance = 1000;
        this.gameStats = {
            bankerWins: 0,
            playerWins: 0,
            ties: 0
        };
        this.gameInProgress = false;
        
        this.initializeGame();
        this.setupEventListeners();
    }

    // 初始化游戏
    initializeGame() {
        this.createDeck();
        this.shuffleDeck();
        this.updateDisplay();
    }

    // 创建一副牌
    createDeck() {
        const suits = ['hearts', 'diamonds', 'clubs', 'spades'];
        const ranks = ['ace', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'jack', 'queen', 'king'];
        
        this.deck = [];
        for (let suit of suits) {
            for (let rank of ranks) {
                this.deck.push({
                    suit: suit,
                    rank: rank,
                    value: this.getCardValue(rank),
                    image: `assets/images/cards/${suit}_${rank}.png`
                });
            }
        }
    }

    // 获取牌的点数值
    getCardValue(rank) {
        if (rank === 'ace') return 1;
        if (['jack', 'queen', 'king'].includes(rank)) return 0;
        return parseInt(rank);
    }

    // 洗牌
    shuffleDeck() {
        for (let i = this.deck.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [this.deck[i], this.deck[j]] = [this.deck[j], this.deck[i]];
        }
    }

    // 发一张牌
    dealCard() {
        if (this.deck.length === 0) {
            this.createDeck();
            this.shuffleDeck();
        }
        return this.deck.pop();
    }

    // 计算手牌点数
    calculatePoints(cards) {
        let total = cards.reduce((sum, card) => sum + card.value, 0);
        return total % 10; // 百家乐只取个位数
    }

    // 设置事件监听器
    setupEventListeners() {
        // 下注按钮
        document.getElementById('bet-banker').addEventListener('click', () => this.placeBet('banker'));
        document.getElementById('bet-player').addEventListener('click', () => this.placeBet('player'));
        document.getElementById('bet-tie').addEventListener('click', () => this.placeBet('tie'));

        // 游戏控制按钮
        document.getElementById('deal-btn').addEventListener('click', () => this.dealCards());
        document.getElementById('new-game-btn').addEventListener('click', () => this.newGame());
        document.getElementById('continue-btn').addEventListener('click', () => this.continueGame());

        // 下注金额输入
        document.getElementById('bet-input').addEventListener('change', (e) => {
            this.betAmount = parseInt(e.target.value);
        });
    }

    // 下注
    placeBet(betType) {
        if (this.gameInProgress) return;

        this.betAmount = parseInt(document.getElementById('bet-input').value);
        
        if (this.betAmount > this.balance) {
            alert('余额不足！');
            return;
        }

        if (this.betAmount < 10) {
            alert('最小下注金额为10元！');
            return;
        }

        this.currentBet = betType;
        
        // 更新UI
        document.querySelectorAll('.bet-btn').forEach(btn => btn.classList.remove('selected'));
        document.getElementById(`bet-${betType}`).classList.add('selected');
        document.getElementById('current-bet-display').textContent = 
            `${this.getBetDisplayName(betType)} - ¥${this.betAmount}`;
        document.getElementById('deal-btn').disabled = false;
    }

    // 获取下注显示名称
    getBetDisplayName(betType) {
        const names = {
            'banker': '庄家',
            'player': '闲家',
            'tie': '和局'
        };
        return names[betType];
    }

    // 发牌
    dealCards() {
        if (!this.currentBet || this.gameInProgress) return;

        this.gameInProgress = true;
        this.balance -= this.betAmount;
        
        // 清空之前的牌
        this.playerCards = [];
        this.bankerCards = [];

        // 发初始两张牌
        this.playerCards.push(this.dealCard());
        this.bankerCards.push(this.dealCard());
        this.playerCards.push(this.dealCard());
        this.bankerCards.push(this.dealCard());

        this.updateCardDisplay();
        this.updatePointsDisplay();

        // 检查是否需要补牌
        setTimeout(() => {
            this.checkForThirdCard();
        }, 1000);
    }

    // 检查是否需要补牌
    checkForThirdCard() {
        const playerPoints = this.calculatePoints(this.playerCards);
        const bankerPoints = this.calculatePoints(this.bankerCards);

        let playerThirdCard = null;

        // 闲家补牌规则
        if (playerPoints <= 5) {
            playerThirdCard = this.dealCard();
            this.playerCards.push(playerThirdCard);
        }

        // 庄家补牌规则
        if (playerThirdCard) {
            // 如果闲家补了第三张牌，庄家根据复杂规则决定是否补牌
            if (this.shouldBankerDrawThirdCard(bankerPoints, playerThirdCard.value)) {
                this.bankerCards.push(this.dealCard());
            }
        } else {
            // 如果闲家没有补牌，庄家点数<=5时补牌
            if (bankerPoints <= 5) {
                this.bankerCards.push(this.dealCard());
            }
        }

        this.updateCardDisplay();
        this.updatePointsDisplay();

        setTimeout(() => {
            this.determineWinner();
        }, 1000);
    }

    // 庄家是否应该补第三张牌
    shouldBankerDrawThirdCard(bankerPoints, playerThirdCardValue) {
        switch (bankerPoints) {
            case 0:
            case 1:
            case 2:
                return true;
            case 3:
                return playerThirdCardValue !== 8;
            case 4:
                return [2, 3, 4, 5, 6, 7].includes(playerThirdCardValue);
            case 5:
                return [4, 5, 6, 7].includes(playerThirdCardValue);
            case 6:
                return [6, 7].includes(playerThirdCardValue);
            default:
                return false;
        }
    }

    // 判断胜负
    determineWinner() {
        const playerPoints = this.calculatePoints(this.playerCards);
        const bankerPoints = this.calculatePoints(this.bankerCards);

        let result = '';
        let winnings = 0;

        if (playerPoints > bankerPoints) {
            result = '闲家胜';
            this.gameStats.playerWins++;
            if (this.currentBet === 'player') {
                winnings = this.betAmount * 2; // 1:1 赔率
            }
        } else if (bankerPoints > playerPoints) {
            result = '庄家胜';
            this.gameStats.bankerWins++;
            if (this.currentBet === 'banker') {
                winnings = this.betAmount * 1.95; // 1:0.95 赔率
            }
        } else {
            result = '和局';
            this.gameStats.ties++;
            if (this.currentBet === 'tie') {
                winnings = this.betAmount * 9; // 1:8 赔率
            } else {
                winnings = this.betAmount; // 和局退还本金
            }
        }

        this.balance += winnings;
        this.showResult(result, playerPoints, bankerPoints, winnings);
        this.updateDisplay();
    }

    // 显示游戏结果
    showResult(result, playerPoints, bankerPoints, winnings) {
        const resultElement = document.getElementById('game-result');
        const resultText = document.getElementById('result-text');
        const resultDetails = document.getElementById('result-details');

        resultText.textContent = result;
        
        let detailsText = `闲家: ${playerPoints}点, 庄家: ${bankerPoints}点\n`;
        if (winnings > 0) {
            detailsText += `恭喜！您赢得了 ¥${winnings.toFixed(2)}`;
        } else {
            detailsText += `很遗憾，您输了 ¥${this.betAmount}`;
        }
        
        resultDetails.textContent = detailsText;
        resultElement.classList.remove('hidden');
    }

    // 继续游戏
    continueGame() {
        document.getElementById('game-result').classList.add('hidden');
        this.gameInProgress = false;
        this.currentBet = null;
        
        // 重置UI
        document.querySelectorAll('.bet-btn').forEach(btn => btn.classList.remove('selected'));
        document.getElementById('current-bet-display').textContent = '无';
        document.getElementById('deal-btn').disabled = true;
        
        // 清空牌桌
        document.getElementById('player-cards').innerHTML = '';
        document.getElementById('banker-cards').innerHTML = '';
        document.getElementById('player-points').textContent = '0';
        document.getElementById('banker-points').textContent = '0';
    }

    // 新游戏
    newGame() {
        this.balance = 1000;
        this.gameStats = { bankerWins: 0, playerWins: 0, ties: 0 };
        this.continueGame();
        this.updateDisplay();
    }

    // 更新显示
    updateDisplay() {
        document.getElementById('balance').textContent = this.balance.toFixed(2);
        document.getElementById('banker-wins').textContent = this.gameStats.bankerWins;
        document.getElementById('player-wins').textContent = this.gameStats.playerWins;
        document.getElementById('ties').textContent = this.gameStats.ties;
    }

    // 更新牌的显示
    updateCardDisplay() {
        this.displayCards('player-cards', this.playerCards);
        this.displayCards('banker-cards', this.bankerCards);
    }

    // 显示牌
    displayCards(containerId, cards) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';
        
        cards.forEach(card => {
            const cardElement = document.createElement('div');
            cardElement.className = 'card';
            cardElement.innerHTML = `<img src="${card.image}" alt="${card.rank} of ${card.suit}">`;
            container.appendChild(cardElement);
        });
    }

    // 更新点数显示
    updatePointsDisplay() {
        document.getElementById('player-points').textContent = this.calculatePoints(this.playerCards);
        document.getElementById('banker-points').textContent = this.calculatePoints(this.bankerCards);
    }
}

// 游戏初始化
document.addEventListener('DOMContentLoaded', () => {
    new BaccaratGame();
});
