// 百家乐游戏类
class BaccaratGame {
    constructor() {
        this.deck = [];
        this.playerCards = [];
        this.bankerCards = [];
        this.currentBet = null;
        this.betAmount = 0;
        this.balance = 1000;
        this.gameStats = {
            bankerWins: 0,
            playerWins: 0,
            ties: 0
        };
        this.gameInProgress = false;

        // 5副牌场次系统
        this.sessionNumber = 1;
        this.roundNumber = 0;
        this.totalDecks = 5;
        this.cardsPerDeck = 52;
        this.totalCards = this.totalDecks * this.cardsPerDeck; // 260张牌
        this.minCardsForNewRound = 10; // 剩余牌数少于10张时开启新场次

        // 路表数据
        this.gameHistory = [];
        this.roadmaps = {
            bigRoad: [],
            bigEye: [],
            smallRoad: [],
            cockroach: []
        };

        this.initializeGame();
        this.setupEventListeners();
    }

    // 初始化游戏
    initializeGame() {
        this.createDeck();
        this.shuffleDeck();
        this.updateDisplay();
    }

    // 创建5副牌
    createDeck() {
        const suits = ['hearts', 'diamonds', 'clubs', 'spades'];
        const ranks = ['ace', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'jack', 'queen', 'king'];

        this.deck = [];
        // 创建5副牌
        for (let deckNum = 0; deckNum < this.totalDecks; deckNum++) {
            for (let suit of suits) {
                for (let rank of ranks) {
                    this.deck.push({
                        suit: suit,
                        rank: rank,
                        value: this.getCardValue(rank),
                        image: `assets/images/cards/${suit}_${rank}.png`
                    });
                }
            }
        }
    }

    // 获取牌的点数值
    getCardValue(rank) {
        if (rank === 'ace') return 1;
        if (['jack', 'queen', 'king'].includes(rank)) return 0;
        return parseInt(rank);
    }

    // 洗牌
    shuffleDeck() {
        for (let i = this.deck.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [this.deck[i], this.deck[j]] = [this.deck[j], this.deck[i]];
        }
    }

    // 发一张牌
    dealCard() {
        // 检查是否需要开启新场次
        if (this.deck.length < this.minCardsForNewRound) {
            this.startNewSession();
        }
        return this.deck.pop();
    }

    // 开启新场次
    startNewSession() {
        this.sessionNumber++;
        this.roundNumber = 0;
        this.createDeck();
        this.shuffleDeck();

        // 清空路表
        this.gameHistory = [];
        this.roadmaps = {
            bigRoad: [],
            bigEye: [],
            smallRoad: [],
            cockroach: []
        };

        this.updateRoadmapDisplay();
        this.showSessionMessage();
    }

    // 显示新场次消息
    showSessionMessage() {
        const message = `第${this.sessionNumber}场次开始！`;
        // 可以添加一个临时提示
        const notification = document.createElement('div');
        notification.className = 'session-notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #ffd700;
            color: #0f4c3a;
            padding: 20px;
            border-radius: 10px;
            font-size: 1.2em;
            font-weight: bold;
            z-index: 2000;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        `;
        document.body.appendChild(notification);

        setTimeout(() => {
            document.body.removeChild(notification);
        }, 3000);
    }

    // 计算手牌点数
    calculatePoints(cards) {
        let total = cards.reduce((sum, card) => sum + card.value, 0);
        return total % 10; // 百家乐只取个位数
    }

    // 设置事件监听器
    setupEventListeners() {
        // 下注按钮
        document.getElementById('bet-banker').addEventListener('click', () => this.placeBet('banker'));
        document.getElementById('bet-player').addEventListener('click', () => this.placeBet('player'));
        document.getElementById('bet-tie').addEventListener('click', () => this.placeBet('tie'));

        // 游戏控制按钮
        document.getElementById('deal-btn').addEventListener('click', () => this.dealCards());
        document.getElementById('new-game-btn').addEventListener('click', () => this.newGame());
        document.getElementById('continue-btn').addEventListener('click', () => this.continueGame());

        // 下注金额输入
        document.getElementById('bet-input').addEventListener('change', (e) => {
            this.betAmount = parseInt(e.target.value);
        });
    }

    // 下注
    placeBet(betType) {
        if (this.gameInProgress) return;

        this.betAmount = parseInt(document.getElementById('bet-input').value);
        
        if (this.betAmount > this.balance) {
            alert('余额不足！');
            return;
        }

        if (this.betAmount < 10) {
            alert('最小下注金额为10元！');
            return;
        }

        this.currentBet = betType;
        
        // 更新UI
        document.querySelectorAll('.bet-btn').forEach(btn => btn.classList.remove('selected'));
        document.getElementById(`bet-${betType}`).classList.add('selected');
        document.getElementById('current-bet-display').textContent = 
            `${this.getBetDisplayName(betType)} - ¥${this.betAmount}`;
        document.getElementById('deal-btn').disabled = false;
    }

    // 获取下注显示名称
    getBetDisplayName(betType) {
        const names = {
            'banker': '庄家',
            'player': '闲家',
            'tie': '和局'
        };
        return names[betType];
    }

    // 发牌
    dealCards() {
        if (!this.currentBet || this.gameInProgress) return;

        this.gameInProgress = true;
        this.balance -= this.betAmount;
        
        // 清空之前的牌
        this.playerCards = [];
        this.bankerCards = [];

        // 发初始两张牌
        this.playerCards.push(this.dealCard());
        this.bankerCards.push(this.dealCard());
        this.playerCards.push(this.dealCard());
        this.bankerCards.push(this.dealCard());

        this.updateCardDisplay();
        this.updatePointsDisplay();

        // 检查是否需要补牌
        setTimeout(() => {
            this.checkForThirdCard();
        }, 1000);
    }

    // 检查是否需要补牌
    checkForThirdCard() {
        const playerPoints = this.calculatePoints(this.playerCards);
        const bankerPoints = this.calculatePoints(this.bankerCards);

        let playerThirdCard = null;

        // 闲家补牌规则
        if (playerPoints <= 5) {
            playerThirdCard = this.dealCard();
            this.playerCards.push(playerThirdCard);
        }

        // 庄家补牌规则
        if (playerThirdCard) {
            // 如果闲家补了第三张牌，庄家根据复杂规则决定是否补牌
            if (this.shouldBankerDrawThirdCard(bankerPoints, playerThirdCard.value)) {
                this.bankerCards.push(this.dealCard());
            }
        } else {
            // 如果闲家没有补牌，庄家点数<=5时补牌
            if (bankerPoints <= 5) {
                this.bankerCards.push(this.dealCard());
            }
        }

        this.updateCardDisplay();
        this.updatePointsDisplay();

        setTimeout(() => {
            this.determineWinner();
        }, 1000);
    }

    // 庄家是否应该补第三张牌
    shouldBankerDrawThirdCard(bankerPoints, playerThirdCardValue) {
        switch (bankerPoints) {
            case 0:
            case 1:
            case 2:
                return true;
            case 3:
                return playerThirdCardValue !== 8;
            case 4:
                return [2, 3, 4, 5, 6, 7].includes(playerThirdCardValue);
            case 5:
                return [4, 5, 6, 7].includes(playerThirdCardValue);
            case 6:
                return [6, 7].includes(playerThirdCardValue);
            default:
                return false;
        }
    }

    // 判断胜负
    determineWinner() {
        const playerPoints = this.calculatePoints(this.playerCards);
        const bankerPoints = this.calculatePoints(this.bankerCards);

        let result = '';
        let winnings = 0;
        let winner = '';

        if (playerPoints > bankerPoints) {
            result = '闲家胜';
            winner = 'player';
            this.gameStats.playerWins++;
            if (this.currentBet === 'player') {
                winnings = this.betAmount * 2; // 1:1 赔率
            }
        } else if (bankerPoints > playerPoints) {
            result = '庄家胜';
            winner = 'banker';
            this.gameStats.bankerWins++;
            if (this.currentBet === 'banker') {
                winnings = this.betAmount * 1.95; // 1:0.95 赔率
            }
        } else {
            result = '和局';
            winner = 'tie';
            this.gameStats.ties++;
            if (this.currentBet === 'tie') {
                winnings = this.betAmount * 9; // 1:8 赔率
            } else {
                winnings = this.betAmount; // 和局退还本金
            }
        }

        // 记录游戏历史
        this.roundNumber++;
        const gameRecord = {
            round: this.roundNumber,
            winner: winner,
            playerPoints: playerPoints,
            bankerPoints: bankerPoints,
            playerCards: [...this.playerCards],
            bankerCards: [...this.bankerCards],
            playerPair: this.hasPair(this.playerCards),
            bankerPair: this.hasPair(this.bankerCards)
        };

        this.gameHistory.push(gameRecord);
        this.updateRoadmaps(gameRecord);

        this.balance += winnings;
        this.showResult(result, playerPoints, bankerPoints, winnings);
        this.updateDisplay();
    }

    // 检查是否有对子
    hasPair(cards) {
        if (cards.length < 2) return false;
        return cards[0].rank === cards[1].rank;
    }

    // 显示游戏结果
    showResult(result, playerPoints, bankerPoints, winnings) {
        const resultElement = document.getElementById('game-result');
        const resultText = document.getElementById('result-text');
        const resultDetails = document.getElementById('result-details');

        resultText.textContent = result;
        
        let detailsText = `闲家: ${playerPoints}点, 庄家: ${bankerPoints}点\n`;
        if (winnings > 0) {
            detailsText += `恭喜！您赢得了 ¥${winnings.toFixed(2)}`;
        } else {
            detailsText += `很遗憾，您输了 ¥${this.betAmount}`;
        }
        
        resultDetails.textContent = detailsText;
        resultElement.classList.remove('hidden');
    }

    // 继续游戏
    continueGame() {
        document.getElementById('game-result').classList.add('hidden');
        this.gameInProgress = false;
        this.currentBet = null;
        
        // 重置UI
        document.querySelectorAll('.bet-btn').forEach(btn => btn.classList.remove('selected'));
        document.getElementById('current-bet-display').textContent = '无';
        document.getElementById('deal-btn').disabled = true;
        
        // 清空牌桌
        document.getElementById('player-cards').innerHTML = '';
        document.getElementById('banker-cards').innerHTML = '';
        document.getElementById('player-points').textContent = '0';
        document.getElementById('banker-points').textContent = '0';
    }

    // 新游戏
    newGame() {
        this.balance = 1000;
        this.gameStats = { bankerWins: 0, playerWins: 0, ties: 0 };
        this.sessionNumber = 1;
        this.roundNumber = 0;
        this.gameHistory = [];
        this.roadmaps = {
            bigRoad: [],
            bigEye: [],
            smallRoad: [],
            cockroach: []
        };
        this.createDeck();
        this.shuffleDeck();
        this.continueGame();
        this.updateDisplay();
        this.updateRoadmapDisplay();
    }

    // 更新显示
    updateDisplay() {
        document.getElementById('balance').textContent = this.balance.toFixed(2);
        document.getElementById('banker-wins').textContent = this.gameStats.bankerWins;
        document.getElementById('player-wins').textContent = this.gameStats.playerWins;
        document.getElementById('ties').textContent = this.gameStats.ties;
        document.getElementById('session-number').textContent = this.sessionNumber;
        document.getElementById('cards-remaining').textContent = this.deck.length;
        document.getElementById('round-number').textContent = this.roundNumber;
    }

    // 更新路表
    updateRoadmaps(gameRecord) {
        this.updateBigRoad(gameRecord);
        this.updateDerivedRoads();
        this.updateRoadmapDisplay();
    }

    // 更新大路
    updateBigRoad(gameRecord) {
        if (gameRecord.winner === 'tie') {
            // 和局在大路中标记在最后一个结果上
            if (this.roadmaps.bigRoad.length > 0) {
                const lastEntry = this.roadmaps.bigRoad[this.roadmaps.bigRoad.length - 1];
                lastEntry.ties = (lastEntry.ties || 0) + 1;
            }
        } else {
            // 庄家胜或闲家胜
            const entry = {
                winner: gameRecord.winner,
                playerPair: gameRecord.playerPair,
                bankerPair: gameRecord.bankerPair,
                ties: 0
            };
            this.roadmaps.bigRoad.push(entry);
        }
    }

    // 更新衍生路表（大眼仔、小路、曱甴路）
    updateDerivedRoads() {
        // 简化版本的衍生路表算法
        const bigRoadResults = this.roadmaps.bigRoad.filter(entry => entry.winner !== 'tie');

        if (bigRoadResults.length >= 2) {
            // 大眼仔路
            this.roadmaps.bigEye = this.calculateDerivedRoad(bigRoadResults, 1);
        }

        if (bigRoadResults.length >= 3) {
            // 小路
            this.roadmaps.smallRoad = this.calculateDerivedRoad(bigRoadResults, 2);
        }

        if (bigRoadResults.length >= 4) {
            // 曱甴路
            this.roadmaps.cockroach = this.calculateDerivedRoad(bigRoadResults, 3);
        }
    }

    // 计算衍生路表
    calculateDerivedRoad(results, offset) {
        const derivedRoad = [];
        for (let i = offset + 1; i < results.length; i++) {
            const current = results[i].winner;
            const compare1 = results[i - 1].winner;
            const compare2 = results[i - offset - 1].winner;

            // 简化的比较逻辑：如果当前结果与对比位置的结果相同，记录为红色，否则为蓝色
            const isRed = (current === compare1) === (compare1 === compare2);
            derivedRoad.push({
                color: isRed ? 'red' : 'blue'
            });
        }
        return derivedRoad;
    }

    // 更新路表显示
    updateRoadmapDisplay() {
        this.displayBigRoad();
        this.displayDerivedRoad('big-eye', this.roadmaps.bigEye);
        this.displayDerivedRoad('small-road', this.roadmaps.smallRoad);
        this.displayDerivedRoad('cockroach-road', this.roadmaps.cockroach);
    }

    // 显示大路
    displayBigRoad() {
        const container = document.getElementById('big-road');
        container.innerHTML = '';

        this.roadmaps.bigRoad.forEach((entry, index) => {
            const cell = document.createElement('div');
            cell.className = `road-cell ${entry.winner}`;

            let content = entry.winner === 'banker' ? '庄' : '闲';
            if (entry.ties > 0) {
                content += entry.ties;
            }

            cell.textContent = content;

            // 添加对子标记
            if (entry.playerPair) {
                cell.classList.add('player-pair');
            }
            if (entry.bankerPair) {
                cell.classList.add('banker-pair');
            }

            container.appendChild(cell);
        });
    }

    // 显示衍生路表
    displayDerivedRoad(containerId, roadData) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';

        roadData.forEach(entry => {
            const cell = document.createElement('div');
            cell.className = `road-cell ${entry.color === 'red' ? 'banker' : 'player'}`;
            cell.textContent = '●';
            container.appendChild(cell);
        });
    }

    // 更新牌的显示
    updateCardDisplay() {
        this.displayCards('player-cards', this.playerCards);
        this.displayCards('banker-cards', this.bankerCards);
    }

    // 显示牌
    displayCards(containerId, cards) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';
        
        cards.forEach(card => {
            const cardElement = document.createElement('div');
            cardElement.className = 'card';
            cardElement.innerHTML = `<img src="${card.image}" alt="${card.rank} of ${card.suit}">`;
            container.appendChild(cardElement);
        });
    }

    // 更新点数显示
    updatePointsDisplay() {
        document.getElementById('player-points').textContent = this.calculatePoints(this.playerCards);
        document.getElementById('banker-points').textContent = this.calculatePoints(this.bankerCards);
    }
}

// 游戏初始化
document.addEventListener('DOMContentLoaded', () => {
    new BaccaratGame();
});
